<!DOCTYPE html>
<?php
// 获取主题设置
$theme = isset($_COOKIE['site_theme']) ? $_COOKIE['site_theme'] : 'orange';
$theme_class = 'theme-' . $theme;
?>
<html lang="zh-CN" class="<?php echo $theme_class; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>页面未找到 - <?php echo $site_name; ?></title>
    <link rel="stylesheet" href="/static/font-awesome/css/all.min.css">
    <link rel="stylesheet" href="/static/css/themes.css">
    <link rel="stylesheet" href="/template/m/css/common.css">
    <style>
        body {background:#f5f5f5;margin:0;padding:0;font-family:system-ui,-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,sans-serif;display:flex;flex-direction:column;min-height:100vh}
        
        .message-container {display:flex;align-items:flex-start;justify-content:center;width:100%;padding:20px;box-sizing:border-box;margin-top:60px}
        .message-panel {width:100%;max-width:320px;background:#fff;border-radius:2px;padding:25px 20px;text-align:center;box-shadow:none;border:1px solid #eee}
        .error-icon {font-size:70px;font-weight:700;color:#ff4d4f;margin-bottom:20px}
        .error-title {margin:0 0 15px;font-size:22px;font-weight:600;color:#333}
        .error-message {margin-bottom:25px;color:#666;line-height:1.5;font-size:15px}
        .btn {display:block;width:100%;padding:12px 0;background-color:var(--primary-color);color:white;text-decoration:none;border-radius:0;font-size:15px;font-weight:500;text-align:center;border:none;cursor:pointer}
        .btn:hover {opacity:0.95}
        .btn-secondary {background-color:#f5f5f5;color:#666;margin-top:10px}
        
        footer {width:100%;text-align:center;padding:15px 0;font-size:12px;color:#999;position:fixed;bottom:0}
    </style>
</head>
<body>
    <div class="message-container">
        <div class="message-panel">
            <div class="error-icon">404</div>
            <h1 class="error-title">页面未找到</h1>
            <p class="error-message">您访问的页面不存在或已被删除，请检查网址是否正确。</p>
            <a href="/" class="btn">返回首页</a>
            <a href="javascript:history.back();" class="btn btn-secondary">返回上一页</a>
        </div>
    </div>

    <footer>
        <p>&copy; <?php echo date('Y'); ?> <?php echo $site_name; ?> 版权所有</p>
    </footer>
</body>
</html> 