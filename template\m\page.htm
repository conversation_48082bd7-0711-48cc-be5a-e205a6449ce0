<!DOCTYPE html>
<?php
// 获取主题设置
$theme = isset($_COOKIE['site_theme']) ? $_COOKIE['site_theme'] : 'red';
$theme_class = 'theme-' . $theme;
?>
<html lang="zh-CN" class="<?php echo $theme_class; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>{if $page.title}{$page.title} - {$site_name}{else}{$site_name}{/if}</title>
    <meta name="keywords" content="{if $page.meta_keywords}{$page.meta_keywords}{else}{$site_keywords}{/if}">
    <meta name="description" content="{if $page.meta_description}{$page.meta_description}{else}{$site_description}{/if}">
    
    <link rel="stylesheet" href="/static/font-awesome/css/all.min.css">
    <link rel="stylesheet" href="/static/css/themes.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="/template/m/css/common.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="/static/css/content-responsive.css?v=<?php echo time(); ?>">
    <script src="/static/js/common.js"></script>
    <script src="/static/js/page-stabilizer.js"></script>
    
    <style>
        /* 单页专用样式 */
        .page-container {
            background: var(--bg-color);
            margin: 0;
            min-height: calc(100vh - 120px);
            margin-top: 50px; /* 为固定头部留出空间 */
        }
        

        
        .page-content {
            padding: 15px;
            line-height: 1.6;
            font-size: 15px;
            color: var(--text-color);
            background: var(--bg-color);
            min-height: calc(100vh - 100px);
        }
        
        .page-content h1,
        .page-content h2,
        .page-content h3,
        .page-content h4,
        .page-content h5,
        .page-content h6 {
            color: var(--heading-color);
            margin: 20px 0 10px 0;
            font-weight: 600;
        }
        
        .page-content h1 {
            font-size: 20px;
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 8px;
        }
        
        .page-content h2 {
            font-size: 18px;
            border-left: 3px solid var(--primary-color);
            padding-left: 10px;
        }
        
        .page-content h3 {
            font-size: 16px;
        }
        
        .page-content p {
            margin-bottom: 12px;
            text-align: justify;
        }
        
        .page-content ul,
        .page-content ol {
            margin: 12px 0;
            padding-left: 20px;
        }
        
        .page-content li {
            margin-bottom: 6px;
        }
        
        .page-content img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            margin: 10px auto;
            display: block;
        }

        /* 保持段落中图片的居中对齐 */
        .page-content p[style*="text-align: center"] img,
        .page-content p[style*="text-align:center"] img {
            margin: 10px auto;
            display: block;
        }

        /* 支持内联样式的居中 - 不覆盖内联样式 */
        .page-content p:not([style*="text-align"]) {
            text-align: justify;
        }
        
        .page-content blockquote {
            background: var(--card-bg);
            border-left: 3px solid var(--primary-color);
            margin: 15px 0;
            padding: 10px 15px;
            font-style: italic;
            color: var(--text-secondary);
            border-radius: 0 4px 4px 0;
        }
        
        .page-content table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            font-size: 14px;
            background: var(--card-bg);
            border-radius: 4px;
            overflow: hidden;
        }
        
        .page-content table th,
        .page-content table td {
            padding: 8px 10px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }
        
        .page-content table th {
            background: var(--primary-color);
            color: white;
            font-weight: 600;
        }
        
        .page-content code {
            background: var(--code-bg);
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            color: var(--code-color);
        }
        
        .page-content pre {
            background: var(--code-bg);
            padding: 12px;
            border-radius: 4px;
            overflow-x: auto;
            margin: 12px 0;
            font-size: 13px;
        }
        
        .page-content pre code {
            background: none;
            padding: 0;
            color: var(--text-color);
        }
        

        
        /* 简约主题样式 */
        html.theme-simple .page-header {
            background: #ffffff;
            color: #333333;
            border-bottom: 1px solid #eeeeee;
        }
        
        html.theme-simple .page-title {
            color: #333333;
        }
        
        html.theme-simple .page-meta {
            color: #666666;
        }
        
        /* 图片点击放大遮罩 */
        .image-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s;
        }
        
        .image-overlay.active {
            opacity: 1;
            visibility: visible;
        }
        
        .image-overlay img {
            max-width: 90%;
            max-height: 90%;
            border-radius: 4px;
            transform: scale(0.8);
            transition: transform 0.3s;
        }
        
        .image-overlay.active img {
            transform: scale(1);
        }
        
        .image-overlay .close-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            color: white;
            font-size: 24px;
            cursor: pointer;
            z-index: 10000;
        }
    </style>
</head>
<body>
    {include file="header.htm"}
    
    <!-- 主内容区域 -->
    <div class="page-container">
        <!-- 页面内容 -->
        <div class="page-content">
            <?php echo $page['content']; ?>
        </div>
    </div>
    
    <!-- 图片放大遮罩 -->
    <div class="image-overlay" id="imageOverlay">
        <div class="close-btn" onclick="closeImageOverlay()">
            <i class="fas fa-times"></i>
        </div>
        <img id="overlayImage" src="" alt="">
    </div>
    
    {include file="footer.htm"}

    <!-- 底部导航栏 -->
    {include file="navbar.htm"}
    
    <script>
    // 页面加载完成后的处理
    document.addEventListener('DOMContentLoaded', function() {
        // 为外部链接添加新窗口打开
        const links = document.querySelectorAll('.page-content a[href^="http"]');
        links.forEach(function(link) {
            if (!link.hostname || link.hostname !== window.location.hostname) {
                link.target = '_blank';
                link.rel = 'noopener noreferrer';
            }
        });
        
        // 图片点击放大功能
        const images = document.querySelectorAll('.page-content img');
        images.forEach(function(img) {
            img.style.cursor = 'pointer';
            img.addEventListener('click', function() {
                showImageOverlay(this.src);
            });
        });
    });
    
    // 显示图片放大遮罩
    function showImageOverlay(src) {
        const overlay = document.getElementById('imageOverlay');
        const overlayImage = document.getElementById('overlayImage');
        overlayImage.src = src;
        overlay.classList.add('active');
        document.body.style.overflow = 'hidden';
    }
    
    // 关闭图片放大遮罩
    function closeImageOverlay() {
        const overlay = document.getElementById('imageOverlay');
        overlay.classList.remove('active');
        document.body.style.overflow = '';
    }
    
    // 点击遮罩背景关闭
    document.getElementById('imageOverlay').addEventListener('click', function(e) {
        if (e.target === this) {
            closeImageOverlay();
        }
    });
    
    // 分享或打印功能
    function shareOrPrint() {
        if (navigator.share) {
            navigator.share({
                title: document.title,
                url: window.location.href
            }).catch(function(error) {
                console.log('分享失败:', error);
                fallbackShare();
            });
        } else {
            fallbackShare();
        }
    }
    
    // 备用分享方式
    function fallbackShare() {
        const actions = [
            { text: '复制链接', action: copyLink },
            { text: '打印页面', action: function() { window.print(); } }
        ];
        
        const actionSheet = document.createElement('div');
        actionSheet.style.cssText = `
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: var(--card-bg);
            border-top: 1px solid var(--border-color);
            z-index: 9999;
            padding: 15px;
            transform: translateY(100%);
            transition: transform 0.3s;
        `;
        
        actions.forEach(function(action) {
            const btn = document.createElement('button');
            btn.textContent = action.text;
            btn.style.cssText = `
                display: block;
                width: 100%;
                padding: 12px;
                margin-bottom: 10px;
                background: var(--primary-color);
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 16px;
            `;
            btn.onclick = function() {
                action.action();
                document.body.removeChild(actionSheet);
            };
            actionSheet.appendChild(btn);
        });
        
        const cancelBtn = document.createElement('button');
        cancelBtn.textContent = '取消';
        cancelBtn.style.cssText = `
            display: block;
            width: 100%;
            padding: 12px;
            background: transparent;
            color: var(--text-color);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 16px;
        `;
        cancelBtn.onclick = function() {
            document.body.removeChild(actionSheet);
        };
        actionSheet.appendChild(cancelBtn);
        
        document.body.appendChild(actionSheet);
        setTimeout(function() {
            actionSheet.style.transform = 'translateY(0)';
        }, 10);
    }
    
    // 复制链接
    function copyLink() {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(window.location.href).then(function() {
                showToast('链接已复制到剪贴板');
            });
        } else {
            // 备用方法
            const textArea = document.createElement('textarea');
            textArea.value = window.location.href;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            showToast('链接已复制到剪贴板');
        }
    }
    
    // 显示提示消息
    function showToast(message) {
        const toast = document.createElement('div');
        toast.textContent = message;
        toast.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            z-index: 10000;
            font-size: 14px;
        `;
        document.body.appendChild(toast);
        setTimeout(function() {
            document.body.removeChild(toast);
        }, 2000);
    }
    </script>
</body>
</html>
