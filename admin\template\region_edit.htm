{include file="header.htm"}

<style>
    .form-group {
        margin-bottom: 15px;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
    }
    .form-label {
        display: block;
        margin-bottom: 0;
        font-weight: 500;
        width: 120px;
        text-align: right;
        padding-right: 15px;
        color: #666;
        line-height: 32px;
    }
    .form-field {
        flex: 0 0 auto;
        min-width: 300px;
        max-width: 600px;
    }
    .form-hint {
        flex: 1;
        margin-left: 15px;
        font-size: 12px;
        color: var(--text-secondary);
        padding-top: 10px;
    }
    .form-control {
        display: block;
        width: 100%;
        padding: 8px 12px;
        font-size: 14px;
        line-height: 1.5;
        color: #333;
        background-color: #fff;
        border: 1px solid var(--border-color);
        border-radius: 4px;
        transition: border-color 0.15s ease-in-out;
    }
    .form-control:focus {
        border-color: var(--primary-color);
        outline: 0;
    }
    .input-group {
        display: flex;
    }
    .input-group .form-control {
        border-radius: 4px 0 0 4px;
    }
    .input-group .input-group-append {
        display: flex;
    }
    .input-group .input-group-append .btn {
        border-radius: 0 4px 4px 0;
        border-left: 0;
    }
    .form-buttons {
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid #eee;
        padding-left: 120px;
    }
    .btn {
        display: inline-block;
        font-weight: 400;
        text-align: center;
        white-space: nowrap;
        vertical-align: middle;
        user-select: none;
        border: 1px solid transparent;
        padding: 0.375rem 0.75rem;
        font-size: 0.9rem;
        line-height: 1.5;
        border-radius: 0.25rem;
        transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        cursor: pointer;
        text-decoration: none;
    }
    
    /* 淡色按钮样式 */
    .btn-light-primary {
        background-color: #e6f0ff;
        color: #1b68ff;
        border: 1px solid #cce0ff;
    }
    .btn-light-primary:hover {
        background-color: #d1e3ff;
        color: #0056b3;
    }
    .btn-light-warning {
        background-color: #fff8e6;
        color: #ffa500;
        border: 1px solid #ffe6b3;
    }
    .btn-light-warning:hover {
        background-color: #fff0d1;
        color: #cc8400;
    }
    .btn-light-danger {
        background-color: #ffe6e6;
        color: #ff3333;
        border: 1px solid #ffb3b3;
    }
    .btn-light-danger:hover {
        background-color: #ffd1d1;
        color: #cc0000;
    }
    .btn-light-info {
        background-color: #e6f7ff;
        color: #00aaff;
        border: 1px solid #b3e0ff;
    }
    .btn-light-info:hover {
        background-color: #d1f0ff;
        color: #0088cc;
    }
    .btn-light-success {
        background-color: #e6ffe6;
        color: #00aa00;
        border: 1px solid #b3ffb3;
    }
    .btn-light-success:hover {
        background-color: #d1ffd1;
        color: #008800;
    }
    .btn-light-secondary {
        background-color: #f0f0f0;
        color: #666666;
        border: 1px solid #dddddd;
    }
    .btn-light-secondary:hover {
        background-color: #e0e0e0;
        color: #444444;
    }
    
    .section-title {
        font-size: 1.5rem;
        font-weight: 500;
        margin-bottom: 1rem;
    }
    .card {
        background: #fff;
        border-radius: 4px;
        box-shadow: 0 1px 2px rgba(0,0,0,0.05);
        margin-bottom: 20px;
        padding: 20px;
    }
    .card-title {
        font-size: 1.1rem;
        font-weight: 500;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid var(--border-color);
    }
    
    @media (max-width: 992px) {
        .form-group {
            flex-direction: column;
            align-items: flex-start;
        }
        .form-label {
            width: 100%;
            text-align: left;
            margin-bottom: 5px;
            padding-right: 0;
        }
        .form-field {
            width: 100%;
            max-width: 100%;
        }
        .form-hint {
            margin-left: 0;
            margin-top: 5px;
            width: 100%;
        }
    }
    
    @media (max-width: 768px) {
        .form-group {
            flex-direction: column;
            align-items: flex-start;
        }
        .form-label {
            width: 100%;
            text-align: left;
            margin-bottom: 5px;
            padding-right: 0;
        }
        .form-field {
            width: 100%;
        }
        .form-hint {
            margin-left: 0;
            margin-top: 5px;
            width: 100%;
        }
    }
</style>

<div class="section">
    <!-- 消息提示 -->
    {if !empty($message)}
    <div class="alert alert-success">
        <i class="fas fa-check-circle"></i>
        <div>
            <p>{$message}</p>
        </div>
    </div>
    {/if}
    
    {if !empty($error)}
    <div class="alert alert-danger">
        <i class="fas fa-exclamation-circle"></i>
        <div>
            <p>{$error}</p>
        </div>
    </div>
    {/if}
    
    <div class="card">
        <div class="card-title">
            {if $action == 'edit'}
                编辑区域信息
            {else}
                {if $parent_id > 0 && $parent_region}
                    添加 {$parent_region.name} 的子区域
                {else}
                    添加顶级区域（省/直辖市）
                {/if}
            {/if}
        </div>
        
        <form id="region-form" action="{if $action == 'edit'}region.php?action=edit&id={$region.id}{else}region.php?action=add{/if}" method="post">
            <div class="form-group">
                <label class="form-label" for="name">区域名称</label>
                <div class="form-field">
                    <input type="text" class="form-control" id="name" name="name" value="{if $action == 'edit'}{$region.name}{/if}" required>
                </div>
                <span class="form-hint">必填，区域的显示名称</span>
            </div>
            
            <div class="form-group">
                <label class="form-label" for="pinyin">拼音标识</label>
                <div class="form-field">
                    <div class="input-group">
                        <input type="text" class="form-control" id="pinyin" name="pinyin" value="{if $action == 'edit'}{$region.pinyin}{/if}">
                        <div class="input-group-append">
                            <button type="button" class="btn btn-light-info" id="generate-pinyin-btn">生成拼音</button>
                        </div>
                    </div>
                </div>
                <span class="form-hint">用于URL和唯一标识，留空将自动生成</span>
            </div>
            
            {if $action == 'edit' && $region.level > 1 || $action == 'add' && $level > 1}
            <div class="form-group">
                <label class="form-label" for="parent_id">所属省份</label>
                <div class="form-field">
                    <select class="form-control" id="parent_id" name="parent_id">
                        {if $provinces}
                            {loop $provinces $province}
                                <option value="{$province.id}" {if ($action == 'edit' && $region.parent_id == $province.id) || ($action == 'add' && $parent_id == $province.id)}selected{/if}>{$province.name}</option>
                            {/loop}
                        {/if}
                    </select>
                </div>
                <span class="form-hint">选择所属的上级区域</span>
            </div>
            {else}
            <input type="hidden" name="parent_id" value="{if $action == 'edit'}{$region.parent_id}{else}{$parent_id}{/if}">
            {/if}
            
            <div class="form-group">
                <label class="form-label" for="level">区域级别</label>
                <div class="form-field">
                    <select class="form-control" id="level" disabled>
                        <option value="1" {if ($action == 'edit' && $region.level == 1) || ($action == 'add' && $level == 1)}selected{/if}>省/直辖市</option>
                        <option value="2" {if ($action == 'edit' && $region.level == 2) || ($action == 'add' && $level == 2)}selected{/if}>市</option>
                        <option value="3" {if ($action == 'edit' && $region.level == 3) || ($action == 'add' && $level == 3)}selected{/if}>区/县</option>
                    </select>
                    <input type="hidden" name="level" value="{if $action == 'edit'}{$region.level}{else}{$level}{/if}">
                </div>
                <span class="form-hint">区域级别由父级区域决定，不能手动修改</span>
            </div>
            
            <div class="form-group">
                <label class="form-label" for="sort_order">排序</label>
                <div class="form-field">
                    <input type="number" class="form-control" style="width: 150px;" id="sort_order" name="sort_order" value="{if $action == 'edit'}{$region.sort_order}{else}0{/if}" min="0">
                </div>
                <span class="form-hint">数字越大排序越靠前</span>
            </div>
            
            <div class="form-buttons">
                <button type="submit" class="btn btn-light-primary">保存</button>
                <a href="{if $parent_id > 0}region.php?parent_id={$parent_id}{else}region.php{/if}" class="btn btn-light-secondary" style="margin-left: 10px;">返回列表</a>
            </div>
        </form>
    </div>
</div>

{include file="footer.htm"}

<script>
    $(function() {
        // 生成拼音
        $('#generate-pinyin-btn').click(function() {
            var name = $('#name').val();
            if (name) {
                $.getJSON('region.php', {
                    action: 'generate_pinyin',
                    name: name
                }, function(data) {
                    if (data && data.pinyin) {
                        $('#pinyin').val(data.pinyin);
                    }
                });
            } else {
                alert('请先输入区域名称');
            }
        });
        
        // 名称输入框失焦时，如果拼音为空，自动生成拼音
        $('#name').blur(function() {
            var name = $(this).val();
            var pinyin = $('#pinyin').val();
            
            if (name && !pinyin) {
                $('#generate-pinyin-btn').click();
            }
        });
        
        // 表单验证
        $('#region-form').submit(function() {
            var name = $('#name').val();
            
            if (!name) {
                alert('请输入区域名称');
                return false;
            }
            
            return true;
        });
    });
</script> 