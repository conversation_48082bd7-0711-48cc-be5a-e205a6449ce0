<!DOCTYPE html>
<?php
// 获取主题设置
$theme = isset($_COOKIE['site_theme']) ? $_COOKIE['site_theme'] : 'red';
$theme_class = 'theme-' . $theme;
?>
<html lang="zh-CN" class="<?php echo $theme_class; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>{$news.title} - {$site_name}</title>
    <meta name="keywords" content="{if !empty($news.keywords)}{$news.keywords}{else}{$news.title},{$site_name},新闻资讯{/if}" />
    <meta name="description" content="{if !empty($news.description)}{$news.description}{else}{$news.title} - {$site_name}新闻资讯{/if}" />

    <link rel="stylesheet" href="/static/font-awesome/css/all.min.css">
    <link rel="stylesheet" href="/static/css/themes.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="/template/m/css/common.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="/static/css/content-responsive.css?v=<?php echo time(); ?>">
    <script src="/static/js/common.js"></script>
    <script src="/static/js/page-stabilizer.js"></script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
            color: #333;
            margin: 0;
            padding: 0;
        }

        /* 搜索弹出层 */
        .search-layer {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--primary-color);
            z-index: 1001;
            transform: translateY(-100%);
            transition: transform 0.3s ease;
        }

        .search-layer.active {
            transform: translateY(0);
        }

        .search-header {
            display: flex;
            align-items: center;
            padding: 15px;
            height: 50px;
        }

        .search-back {
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            margin-right: 15px;
            cursor: pointer;
        }

        .search-input-wrapper {
            flex: 1;
            display: flex;
            background: rgba(255,255,255,0.2);
            border-radius: 20px;
            overflow: hidden;
        }

        .search-input {
            flex: 1;
            border: none;
            background: transparent;
            color: white;
            padding: 8px 15px;
            font-size: 16px;
            outline: none;
        }

        .search-input::placeholder {
            color: rgba(255,255,255,0.7);
        }

        .search-submit {
            background: none;
            border: none;
            color: white;
            padding: 8px 15px;
            cursor: pointer;
        }

        /* 头部分享按钮样式 */
        .header-share-icon {
            color: white;
            text-decoration: none;
            font-size: 18px;
            padding: 8px;
            border-radius: 50%;
            transition: background-color 0.2s;
        }

        .header-share-icon:hover {
            background-color: rgba(255,255,255,0.1);
            color: white;
        }

        /* 文章内容 */
        .article-container {
            background: white;
        }

        .article-header {
            padding: 20px;
            border-bottom: 1px solid #f0f0f0;
        }

        .article-title {
            font-size: 22px;
            font-weight: 700;
            line-height: 1.4;
            color: #333;
            margin-bottom: 15px;
        }

        .article-meta {
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 10px;
            color: #666;
            font-size: 14px;
        }

        .meta-left {
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .meta-item i {
            font-size: 13px;
            color: #999;
        }

        .article-tags {
            display: flex;
            gap: 8px;
        }

        .tag {
            padding: 4px 10px;
            font-size: 12px;
            border-radius: 12px;
            font-weight: 500;
            color: white;
        }

        .tag.top {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
        }

        .tag.rec {
            background: linear-gradient(135deg, #feca57, #ff9ff3);
        }

        .article-content {
            padding: 20px;
            line-height: 1.8;
            font-size: 16px;
            color: #333;
        }

        .article-content p {
            margin-bottom: 16px;
        }

        .article-content img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .article-content p[style*="text-align: center"] img,
        .article-content p[style*="text-align:center"] img {
            margin: 20px auto;
            display: block;
        }

        .article-content h1,
        .article-content h2,
        .article-content h3,
        .article-content h4,
        .article-content h5,
        .article-content h6 {
            margin: 25px 0 15px;
            color: #333;
            font-weight: 600;
        }

        .article-content ul,
        .article-content ol {
            margin: 16px 0;
            padding-left: 20px;
        }

        .article-content li {
            margin-bottom: 8px;
        }

        .article-content blockquote {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 15px 20px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
            font-style: italic;
        }



        /* 图片放大遮罩 */
        .image-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            z-index: 9999;
            justify-content: center;
            align-items: center;
        }

        .image-overlay img {
            max-width: 90%;
            max-height: 90%;
            object-fit: contain;
        }

        .close-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            color: white;
            font-size: 24px;
            cursor: pointer;
            z-index: 10000;
        }
    </style>
</head>
<body>
    <header>
        <div class="header-inner">
            <div class="header-left">
                <a href="javascript:history.back();" class="header-back">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </div>
            <div class="header-title">新闻详情</div>
            <div class="header-right">
                <a href="javascript:shareNews();" class="header-share-icon">
                    <i class="fas fa-share-alt"></i>
                </a>
            </div>
        </div>
    </header>

    <!-- 文章内容 -->
    <div class="article-container">
        <div class="article-header">
            <h1 class="article-title">{$news.title}</h1>
            <div class="article-meta">
                <div class="meta-left">
                    <div class="meta-item">
                        <i class="far fa-clock"></i>
                        <span><?php echo date('Y-m-d H:i', $news['addtime']); ?></span>
                    </div>
                    <div class="meta-item">
                        <i class="far fa-eye"></i>
                        <span>{$news.click}</span>
                    </div>
                    {if $news.author}
                    <div class="meta-item">
                        <i class="far fa-user"></i>
                        <span>{$news.author}</span>
                    </div>
                    {/if}
                </div>
                <div class="article-tags">
                    {if $news.is_top}<span class="tag top">置顶</span>{/if}
                    {if $news.is_recommend}<span class="tag rec">推荐</span>{/if}
                </div>
            </div>
        </div>

        <div class="article-content">
            <?php echo $news['content']; ?>
        </div>


    </div>

    {include file="footer.htm"}

    <!-- 底部导航栏 -->
    {include file="navbar.htm"}

    <!-- 图片放大遮罩 -->
    <div class="image-overlay" id="imageOverlay">
        <div class="close-btn" onclick="closeImageOverlay()">
            <i class="fas fa-times"></i>
        </div>
        <img id="overlayImage" src="" alt="">
    </div>

    <script>
    // 页面加载完成后的处理
    document.addEventListener('DOMContentLoaded', function() {
        // 为外部链接添加新窗口打开
        const links = document.querySelectorAll('.news-content a[href^="http"]');
        links.forEach(function(link) {
            if (!link.hostname || link.hostname !== window.location.hostname) {
                link.target = '_blank';
                link.rel = 'noopener noreferrer';
            }
        });

        // 图片点击放大功能
        const images = document.querySelectorAll('.news-content img');
        images.forEach(function(img) {
            img.style.cursor = 'pointer';
            img.addEventListener('click', function() {
                showImageOverlay(this.src);
            });
        });
    });

    // 显示图片放大遮罩
    function showImageOverlay(src) {
        const overlay = document.getElementById('imageOverlay');
        const overlayImage = document.getElementById('overlayImage');
        overlayImage.src = src;
        overlay.style.display = 'flex';
        document.body.style.overflow = 'hidden';
    }

    // 关闭图片放大遮罩
    function closeImageOverlay() {
        const overlay = document.getElementById('imageOverlay');
        overlay.style.display = 'none';
        document.body.style.overflow = 'auto';
    }

    // 点击遮罩背景关闭
    document.getElementById('imageOverlay').addEventListener('click', function(e) {
        if (e.target === this) {
            closeImageOverlay();
        }
    });
    </script>



    <script>
    function shareNews() {
        const title = '{$news.title}';
        const url = window.location.href;

        // 检查是否支持Web Share API
        if (navigator.share) {
            navigator.share({
                title: title,
                url: url
            }).catch(err => console.log('分享失败:', err));
        } else {
            // 降级方案：复制链接到剪贴板
            if (navigator.clipboard) {
                navigator.clipboard.writeText(url).then(() => {
                    alert('链接已复制到剪贴板');
                }).catch(() => {
                    // 如果复制失败，显示链接
                    prompt('请复制以下链接:', url);
                });
            } else {
                // 最后的降级方案
                prompt('请复制以下链接:', url);
            }
        }
    }
    </script>

    <script src="/template/m/js/app-style.js"></script>
</body>
</html>
