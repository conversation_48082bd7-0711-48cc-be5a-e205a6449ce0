<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php if(null !== ($page_title ?? null)): ?><?php echo $page_title ?? ""; ?> - <?php endif; ?>分类信息网站后台管理</title>
    <link href="../static/font-awesome/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="static/css/admin_clean.css?v=<?php echo time(); ?>" rel="stylesheet">
    <link href="static/css/pagination.css" rel="stylesheet">
    <link href="../static/css/image-compress.css" rel="stylesheet">
</head>
<body>
    <div class="wrapper" id="wrapper">
        <!-- 侧边栏 -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <a href="index.php" class="logo">
                    <i class="fas fa-cube"></i>
                    <span>管理系统</span>
                </a>
            </div>
            <!-- 侧边栏菜单 -->
<div class="menu-item <?php if($current_page == 'index'): ?>active<?php endif; ?>">
    <a href="index.php">
        <i class="fas fa-home"></i>
        <span>控制面板</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'category'): ?>active<?php endif; ?>">
    <a href="category.php">
        <i class="fas fa-list"></i>
        <span>分类管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'region'): ?>active<?php endif; ?>">
    <a href="region.php">
        <i class="fas fa-map-marker-alt"></i>
        <span>区域管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'info'): ?>active<?php endif; ?>">
    <a href="info.php">
        <i class="fas fa-file-alt"></i>
        <span>信息管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'news_category'): ?>active<?php endif; ?>">
    <a href="news_category.php">
        <i class="fas fa-newspaper"></i>
        <span>新闻栏目</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'news'): ?>active<?php endif; ?>">
    <a href="news.php">
        <i class="fas fa-edit"></i>
        <span>新闻管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'pages'): ?>active<?php endif; ?>">
    <a href="pages.php">
        <i class="fas fa-file-alt"></i>
        <span>单页管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'links'): ?>active<?php endif; ?>">
    <a href="links.php">
        <i class="fas fa-link"></i>
        <span>友情链接</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'report'): ?>active<?php endif; ?>">
    <a href="report.php">
        <i class="fas fa-flag"></i>
        <span>举报管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'admin'): ?>active<?php endif; ?>">
    <a href="admin.php">
        <i class="fas fa-user-shield"></i>
        <span>管理员管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'operation_logs'): ?>active<?php endif; ?>">
    <a href="operation_logs.php">
        <i class="fas fa-history"></i>
        <span>操作日志</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'mobile_security'): ?>active<?php endif; ?>">
    <a href="mobile_security.php">
        <i class="fas fa-shield-alt"></i>
        <span>手机号安全</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'setting'): ?>active<?php endif; ?>">
    <a href="setting.php">
        <i class="fas fa-cog"></i>
        <span>系统设置</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'cache_manager'): ?>active<?php endif; ?>">
    <a href="cache_manager.php">
        <i class="fas fa-memory"></i>
        <span>缓存管理</span>
    </a>
</div>



<div class="menu-item <?php if($current_page == 'db_backup'): ?>active<?php endif; ?>">
    <a href="db_backup.php">
        <i class="fas fa-database"></i>
        <span>数据库备份</span>
    </a>
</div>
        </div>

        <!-- 顶部导航 -->
<div class="top-nav">
    <div class="nav-left">
        <div class="toggle-sidebar" id="toggle-sidebar">
            <i class="fas fa-bars"></i>
        </div>
        <div class="breadcrumb">
            <span class="admin-badge"><?php echo (isset($admin['username'])) ? $admin['username'] : ""; ?></span>
            <i class="fas fa-chevron-right"></i>
            <span>控制台</span>
            <?php if(null !== ($breadcrumb ?? null)): ?>
            <i class="fas fa-chevron-right"></i>
            <span><?php echo $breadcrumb ?? ""; ?></span>
            <?php endif; ?>
        </div>
    </div>
    <div class="nav-right">
        <div class="nav-item" id="clear-cache-btn" title="清理所有缓存">
            <i class="fas fa-trash-alt"></i>
        </div>
        <div class="nav-item" title="前台首页">
            <a href="../" target="_blank" style="color:inherit;text-decoration:none;">
                <i class="fas fa-home"></i>
            </a>
        </div>
        <div class="user-item">
            <div class="user-avatar"><i class="fas fa-user"></i></div>
            <span class="user-name"><?php echo (isset($admin['username'])) ? $admin['username'] : ""; ?></span>
            <a href="logout.php" class="logout-link" title="退出登录">
                <i class="fas fa-sign-out-alt"></i>
            </a>
        </div>
    </div>
</div>

<!-- 清理缓存功能的遮罩层和对话框 -->
<div id="cache-overlay" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; background:rgba(0,0,0,0.5); z-index:2000;"></div>
<div id="cache-modal" style="display:none; position:fixed; top:50%; left:50%; transform:translate(-50%,-50%); background:#fff; border-radius:8px; box-shadow:0 4px 20px rgba(0,0,0,0.2); width:300px; padding:20px; z-index:2001;">
    <div style="margin-bottom:15px; font-size:16px; font-weight:600;">确认清理缓存</div>
    <p style="margin-bottom:20px; font-size:14px; color:#666;">此操作将清理所有缓存，包括：</p>
    <ul style="margin-bottom:20px; padding-left:20px; font-size:14px; color:#666;">
        <li>页面缓存</li>
        <li>数据缓存</li>
        <li>模板编译文件</li>
    </ul>
    <div style="display:flex; justify-content:flex-end; gap:10px;">
        <button id="cancel-clear-cache" style="padding:8px 16px; border:1px solid #ddd; background:#fff; border-radius:4px; cursor:pointer;">取消</button>
        <button id="confirm-clear-cache" style="padding:8px 16px; border:none; background:#dc3545; color:#fff; border-radius:4px; cursor:pointer;">确认清理</button>
    </div>
</div>

<!-- 成功提示框 -->
<div id="success-toast" style="display:none; position:fixed; top:50%; left:50%; transform:translate(-50%,-50%); background:#28a745; color:#fff; padding:15px 25px; border-radius:6px; box-shadow:0 4px 12px rgba(0,0,0,0.15); z-index:2002; font-size:14px;">
    <i class="fas fa-check-circle" style="margin-right:8px;"></i>
    缓存清理成功！
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const clearCacheBtn = document.getElementById('clear-cache-btn');
        const cacheOverlay = document.getElementById('cache-overlay');
        const cacheModal = document.getElementById('cache-modal');
        const cancelClearCache = document.getElementById('cancel-clear-cache');
        const confirmClearCache = document.getElementById('confirm-clear-cache');
        const successToast = document.getElementById('success-toast');
        
        if (clearCacheBtn && cacheOverlay && cacheModal) {
            clearCacheBtn.addEventListener('click', function() {
                cacheOverlay.style.display = 'block';
                cacheModal.style.display = 'block';
            });
            
            cancelClearCache.addEventListener('click', function() {
                cacheOverlay.style.display = 'none';
                cacheModal.style.display = 'none';
            });
            
            cacheOverlay.addEventListener('click', function() {
                cacheOverlay.style.display = 'none';
                cacheModal.style.display = 'none';
            });
            
            confirmClearCache.addEventListener('click', function() {
                // 发送清理缓存请求
                const xhr = new XMLHttpRequest();
                xhr.open('POST', 'cache_manager.php', true);
                xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                
                confirmClearCache.innerHTML = '清理中...';
                confirmClearCache.disabled = true;
                
                xhr.onreadystatechange = function() {
                    if (xhr.readyState === 4) {
                        cacheOverlay.style.display = 'none';
                        cacheModal.style.display = 'none';
                        
                        if (xhr.status === 200) {
                            // 显示成功提示
                            successToast.style.display = 'block';
                            successToast.style.transform = 'translate(-50%, -50%) scale(0.8)';
                            
                            setTimeout(function() {
                                successToast.style.transform = 'translate(-50%, -50%) scale(1)';
                            }, 100);
                            
                            setTimeout(function() {
                                successToast.style.transform = 'translate(-50%, -50%) scale(0.8)';
                                setTimeout(function() {
                                    successToast.style.display = 'none';
                                    successToast.style.transform = 'translate(-50%, -50%) scale(1)';
                                }, 300);
                            }, 2000);
                        }
                        
                        confirmClearCache.disabled = false;
                        confirmClearCache.innerHTML = '确认清理';
                    }
                };
                
                xhr.send('action=clear_cache&type=all');
            });
        }
    });
</script>


        <!-- 主内容区 (开始) -->
        <div class="main-content">

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 侧边栏折叠功能
        const toggleSidebar = document.getElementById('toggle-sidebar');
        const sidebar = document.getElementById('sidebar');
        const wrapper = document.getElementById('wrapper');
        
        if (toggleSidebar && sidebar && wrapper) {
            toggleSidebar.addEventListener('click', function() {
                sidebar.classList.toggle('collapsed');
                wrapper.classList.toggle('collapsed');
                localStorage.setItem('sidebar_collapsed', sidebar.classList.contains('collapsed'));
            });
            
            // 恢复侧边栏状态
            const isCollapsed = localStorage.getItem('sidebar_collapsed') === 'true';
            if (isCollapsed) {
                sidebar.classList.add('collapsed');
                wrapper.classList.add('collapsed');
            }
        }
        
        // 设置当前页面的菜单项为激活状态
        const currentPath = window.location.pathname;
        const menuItems = document.querySelectorAll('.menu-item a');
        
        menuItems.forEach(function(item) {
            const href = item.getAttribute('href');
            if (href) {
                // 检查完整路径匹配
                if (currentPath.endsWith(href)) {
                    const menuItem = item.closest('.menu-item');
                    if (menuItem) {
                        menuItem.classList.add('active');
                    }
                    
                    foundActive = true;
                }
            }
        });
        
        // 如果没有找到完全匹配的，尝试部分匹配
        if (!foundActive) {
            const pathParts = currentPath.split('/');
            const filename = pathParts[pathParts.length - 1];
            
            if (filename) {
                menuItems.forEach(function(item) {
                    const href = item.getAttribute('href');
                    if (href && href.includes(filename.split('.')[0])) {
                        const menuItem = item.closest('.menu-item');
                        if (menuItem) {
                            menuItem.classList.add('active');
                        }
                    }
                });
            }
        }
        
        // 保存当前激活的菜单项
        menuItems.forEach(function(item) {
            item.addEventListener('click', function() {
                const href = this.getAttribute('href');
                if (href) {
                    localStorage.setItem('active_menu_item', href);
                }
            });
        });
    });

    // 处理URL参数中的错误和成功消息
    function handleUrlMessages() {
        const urlParams = new URLSearchParams(window.location.search);
        const message = urlParams.get('message');
        const error = urlParams.get('error');

        if (message) {
            showSuccessMessage(message);
            // 清除URL中的message参数
            clearUrlParameter('message');
        }

        if (error) {
            showErrorMessage(error);
            // 清除URL中的error参数
            clearUrlParameter('error');
        }
    }

    // 显示成功消息
    function showSuccessMessage(message) {
        const alertHtml = `
            <div class="alert alert-success alert-dismissible fade show" role="alert" style="position: fixed; top: 70px; right: 20px; z-index: 9999; min-width: 300px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);">
                <i class="fas fa-check-circle me-2"></i>
                <span>${message}</span>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
        document.body.insertAdjacentHTML('beforeend', alertHtml);

        // 5秒后自动消失
        setTimeout(() => {
            const alert = document.querySelector('.alert-success');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }

    // 显示错误消息
    function showErrorMessage(message) {
        const alertHtml = `
            <div class="alert alert-danger alert-dismissible fade show" role="alert" style="position: fixed; top: 70px; right: 20px; z-index: 9999; min-width: 300px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);">
                <i class="fas fa-exclamation-circle me-2"></i>
                <span>${message}</span>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
        document.body.insertAdjacentHTML('beforeend', alertHtml);

        // 8秒后自动消失（错误消息显示时间稍长）
        setTimeout(() => {
            const alert = document.querySelector('.alert-danger');
            if (alert) {
                alert.remove();
            }
        }, 8000);
    }

    // 清除URL参数
    function clearUrlParameter(param) {
        const url = new URL(window.location);
        url.searchParams.delete(param);
        window.history.replaceState({}, document.title, url.toString());
    }

    // 页面加载完成后处理URL消息
    document.addEventListener('DOMContentLoaded', function() {
        handleUrlMessages();
    });
</script>


<style>
/* 页面标题样式 */
.page-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #ddd;
}

.page-title h1 {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.d-flex {
    display: flex !important;
}

.gap-2 {
    gap: 0.5rem !important;
}

/* 表单样式 - 紧凑布局 */
.form-group {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16px;
    gap: 20px;
}

.form-label, .control-label {
    flex: 0 0 120px;
    margin: 8px 0 0 0;
    font-weight: 600;
    color: #333;
    font-size: 14px;
    text-align: right;
}

.form-field {
    flex: 1;
    min-width: 0;
}

.form-control, .form-select {
    display: block;
    width: 100%;
    padding: 6px 12px;
    font-size: 14px;
    line-height: 1.5;
    color: #333;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus, .form-select:focus {
    border-color: #1b68ff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(27, 104, 255, 0.25);
}

textarea.form-control {
    min-height: 60px;
    resize: vertical;
}

/* 表单描述在右侧 */
.form-description {
    flex: 0 0 200px;
    margin: 8px 0 0 0;
    font-size: 12px;
    color: #666;
    line-height: 1.4;
}

/* 输入组样式 */
.input-group {
    display: flex;
    align-items: stretch;
    max-width: 400px;
}

.input-group .form-control {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-right: 0;
}

.input-group-append {
    display: flex;
}

.input-group-append .btn {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-left: 0;
    padding: 6px 12px;
    font-size: 13px;
}

/* 图标预览样式 */
.icon-preview {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background-color: #f8f9fa;
    border: 1px solid #ddd;
    border-left: 0;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    font-size: 14px;
    color: #666;
}

/* 单选按钮组样式 */
.radio-group {
    display: flex;
    gap: 20px;
    align-items: center;
}

.radio-group label {
    display: flex;
    align-items: center;
    gap: 6px;
    font-weight: normal;
    margin: 0;
    cursor: pointer;
    font-size: 14px;
}

.radio-group input[type="radio"] {
    margin: 0;
}

/* 帮助文本样式 - 已移到右侧 */
.help-block, .form-hint {
    display: none; /* 隐藏原有的帮助文本，使用右侧描述 */
}

/* 按钮样式 */
.btn {
    display: inline-block;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.5;
    text-align: center;
    text-decoration: none;
    vertical-align: middle;
    cursor: pointer;
    border: 1px solid transparent;
    border-radius: 4px;
    transition: all 0.15s ease-in-out;
}

.btn-primary {
    color: #fff;
    background-color: #1b68ff;
    border-color: #1b68ff;
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
}

.btn-outline {
    color: #666;
    background-color: #fff;
    border-color: #ddd;
}

.btn-outline:hover {
    background-color: #f8f9fa;
    border-color: #ccc;
}

.btn-light-success {
    background-color: #e6ffe6;
    color: #00aa00;
    border: 1px solid #b3ffb3;
}

.btn-light-success:hover {
    background-color: #d1ffd1;
    color: #008800;
}

.btn-light-danger {
    background-color: #ffe6e6;
    color: #ff3333;
    border: 1px solid #ffb3b3;
}

.btn-light-danger:hover {
    background-color: #ffd1d1;
    color: #cc0000;
}

/* 卡片样式 - 紧凑版 */
.section {
    margin-bottom: 20px;
}

.card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.card-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
}

/* 表单按钮区域 */
.form-actions {
    display: flex;
    gap: 12px;
    padding-top: 16px;
    border-top: 1px solid #eee;
    margin-top: 20px;
    margin-left: 140px; /* 与标签对齐 */
}

/* 特殊字段样式 */
.form-group.compact {
    margin-bottom: 12px;
}

.form-group.inline .form-field {
    display: flex;
    align-items: center;
    gap: 15px;
}

.form-group.inline .form-control {
    width: auto;
    flex: 0 0 auto;
}

/* 移除链接下划线 */
a, a:hover, a:focus, a:active {
    text-decoration: none !important;
}

/* SEO折叠样式 */
.seo-section .collapsible {
    user-select: none;
    transition: color 0.2s;
}

.seo-section .collapsible:hover {
    color: #1b68ff;
}

.seo-section .collapsible-content {
    overflow: hidden;
    transition: max-height 0.3s ease-out;
}

.seo-section .collapsible-content.collapsed {
    max-height: 0;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .page-title {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .form-group {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .form-label, .control-label {
        flex: none;
        text-align: left;
        margin: 0 0 4px 0;
    }

    .form-description {
        flex: none;
        margin: 4px 0 0 0;
    }

    .form-actions {
        margin-left: 0;
        flex-direction: column;
    }

    .input-group {
        flex-direction: column;
        max-width: none;
    }

    .input-group .form-control {
        border-radius: 4px;
        border-right: 1px solid #ddd;
        margin-bottom: 8px;
    }

    .input-group-append .btn {
        border-radius: 4px;
        border-left: 1px solid #ddd;
    }

    .form-group.inline .form-field {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }
}
</style>

<!-- 页面标题 -->
<div class="page-title">
    <h1><?php if(null !== ($category ?? null)): ?>编辑分类<?php else: ?>添加分类<?php endif; ?></h1>
    <div class="d-flex gap-2">
        <a href="category.php" class="btn btn-outline">
            <i class="fas fa-arrow-left"></i>
            返回列表
        </a>
    </div>
</div>

<?php if(null !== ($error ?? null) && !empty($error)): ?>
<div class="alert alert-danger">
    <i class="fas fa-exclamation-circle"></i>
    <span><?php echo $error ?? ""; ?></span>
</div>
<?php endif; ?>

<div class="section">
    <div class="card">
        <h3 class="card-title">基本信息</h3>
        <form method="post" action="" id="categoryForm" class="form-horizontal" <?php if(null !== ($category ?? null)): ?>data-category-id="<?php echo (isset($category['id'])) ? $category['id'] : ""; ?>"<?php endif; ?>>
            <?php if(null !== ($category ?? null) && $category['parent_id'] > 0): ?>
            <input type="hidden" name="return_parent_id" value="<?php echo (isset($category['parent_id'])) ? $category['parent_id'] : ""; ?>">
            <?php elseif(null !== ($selected_parent_id ?? null) && $selected_parent_id > 0): ?>
            <input type="hidden" name="return_parent_id" value="<?php echo $selected_parent_id ?? ""; ?>">
            <?php endif; ?>

            <div class="form-group">
                <label class="control-label" for="name">分类名称</label>
                <div class="form-field">
                    <input type="text" id="name" name="name" value="<?php if(null !== ($category ?? null)): ?><?php echo (isset($category['name'])) ? $category['name'] : ""; ?><?php endif; ?>" class="form-control" required style="max-width: 300px;">
                </div>
                <div class="form-description">必填，分类的显示名称</div>
            </div>

            <div class="form-group">
                <label class="control-label" for="parent_id">父分类</label>
                <div class="form-field">
                    <select id="parent_id" name="parent_id" class="form-select" style="max-width: 300px;" <?php if(null !== ($is_top_level ?? null) && $is_top_level): ?>disabled<?php endif; ?>>
                        <option value="0" <?php if((!null !== ($category ?? null) && !null !== ($selected_parent_id ?? null)) || (null !== ($category ?? null) && $category['parent_id'] == 0) || (null !== ($selected_parent_id ?? null) && $selected_parent_id == 0)): ?>selected<?php endif; ?>>-- 无父分类（作为一级分类）--</option>
                        <?php if(null !== ($parent_categories ?? null) && is_array($parent_categories)): foreach($parent_categories as $parent): ?>
                        <option value="<?php echo (isset($parent['id'])) ? $parent['id'] : ""; ?>" <?php if((null !== ($category ?? null) && $category['parent_id'] == $parent['id']) || (null !== ($selected_parent_id ?? null) && $selected_parent_id == $parent['id'])): ?>selected<?php endif; ?>>
                            <?php echo (isset($parent['name'])) ? $parent['name'] : ""; ?>
                        </option>
                        <?php endforeach; endif; ?>
                    </select>
                    <?php if(null !== ($is_top_level ?? null) && $is_top_level): ?>
                    <input type="hidden" name="parent_id" value="0">
                    <?php endif; ?>
                </div>
                <div class="form-description">
                    <?php if(null !== ($is_top_level ?? null) && $is_top_level): ?>
                    一级分类不能修改父分类
                    <?php else: ?>
                    选择父分类，如无则作为一级分类。只能选择一级分类作为父分类
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="form-group">
                <label class="form-label" for="pinyin">拼音标识</label>
                <div class="form-field">
                    <div class="input-group">
                        <input type="text" id="pinyin" name="pinyin" value="<?php if(null !== ($category ?? null)): ?><?php echo (isset($category['pinyin'])) ? $category['pinyin'] : ""; ?><?php endif; ?>" class="form-control">
                        <div class="input-group-append">
                            <button type="button" id="generatePinyin" class="btn btn-outline">生成拼音</button>
                        </div>
                    </div>
                </div>
                <div class="form-description">用于URL和标识，留空将自动生成。只能包含小写字母、数字和短横线</div>
            </div>

            <div class="form-group compact">
                <label class="form-label" for="sort_order">排序</label>
                <div class="form-field">
                    <input type="number" id="sort_order" name="sort_order" value="<?php if(null !== ($category ?? null)): ?><?php echo (isset($category['sort_order'])) ? $category['sort_order'] : ""; ?><?php else: ?>0<?php endif; ?>" class="form-control" style="width: 120px;">
                </div>
                <div class="form-description">数字越小排序越靠前</div>
            </div>
            
            <div class="form-group compact">
                <label class="form-label" for="icon">图标</label>
                <div class="form-field">
                    <div class="input-group">
                        <input type="text" id="icon" name="icon" value="<?php if(null !== ($category ?? null)): ?><?php echo (isset($category['icon'])) ? $category['icon'] : ""; ?><?php endif; ?>" class="form-control" placeholder="例如: fas fa-home">
                        <div id="iconPreview" class="icon-preview">
                            <?php if(null !== ($category ?? null) && null !== ($category ?? null) && is_array($category) && array_key_exists('icon', $category) && !empty($category['icon'])): ?><i class="<?php echo (isset($category['icon'])) ? $category['icon'] : ""; ?>"></i><?php endif; ?>
                        </div>
                    </div>
                </div>
                <div class="form-description">可选，FontAwesome图标类名，如"fas fa-home"</div>
            </div>

            <div class="form-group compact">
                <label class="form-label">状态</label>
                <div class="form-field">
                    <div class="radio-group">
                        <label>
                            <input type="radio" name="status" value="1" <?php if(!null !== ($category ?? null) || $category['status'] == 1): ?>checked<?php endif; ?>> 启用
                        </label>
                        <label>
                            <input type="radio" name="status" value="0" <?php if(null !== ($category ?? null) && $category['status'] == 0): ?>checked<?php endif; ?>> 禁用
                        </label>
                    </div>
                </div>
                <div class="form-description">设置分类是否可用</div>
            </div>
            
            <div class="form-group">
                <label class="form-label" for="description">分类描述</label>
                <div class="form-field">
                    <textarea id="description" name="description" class="form-control" style="max-width: 400px;"><?php if(null !== ($category ?? null)): ?><?php echo (isset($category['description'])) ? $category['description'] : ""; ?><?php endif; ?></textarea>
                </div>
                <div class="form-description">可选，简短描述此分类，用于前台展示</div>
            </div>
            
            <div class="form-group inline">
                <label class="form-label" for="template">栏目模板</label>
                <div class="form-field">
                    <select id="template" name="template" class="form-control" style="width: 250px;">
                        <option value="">默认模板 (category.htm)</option>
                        <?php if(null !== ($template_files ?? null) && is_array($template_files)): foreach($template_files as $template_file): ?>
                        <option value="<?php echo (isset($template_file['name'])) ? $template_file['name'] : ""; ?>" <?php if(null !== ($category ?? null) && $category['template'] == $template_file['name']): ?>selected<?php endif; ?>>
                            <?php echo (isset($template_file['name'])) ? $template_file['name'] : ""; ?> <?php if(!$template_file['exists_in_mobile']): ?><span style="color:red;">[仅PC端]</span><?php endif; ?>
                        </option>
                        <?php endforeach; endif; ?>
                    </select>
                    <label style="white-space: nowrap; font-weight: normal;">
                        <input type="checkbox" name="sync_template" id="sync_template" value="1"> 同步子栏目
                    </label>
                </div>
                <div class="form-description">标记为[仅PC端]的模板在移动端访问时将自动使用默认模板</div>
            </div>

            <div class="form-group inline">
                <label class="form-label" for="detail_template">详情模板</label>
                <div class="form-field">
                    <select id="detail_template" name="detail_template" class="form-control" style="width: 250px;">
                        <option value="">默认模板 (view.htm)</option>
                        <?php if(null !== ($detail_template_files ?? null) && is_array($detail_template_files)): foreach($detail_template_files as $template_file): ?>
                        <option value="<?php echo (isset($template_file['name'])) ? $template_file['name'] : ""; ?>" <?php if(null !== ($category ?? null) && $category['detail_template'] == $template_file['name']): ?>selected<?php endif; ?>>
                            <?php echo (isset($template_file['name'])) ? $template_file['name'] : ""; ?> <?php if(!$template_file['exists_in_mobile']): ?><span style="color:red;">[仅PC端]</span><?php endif; ?>
                        </option>
                        <?php endforeach; endif; ?>
                    </select>
                    <label style="white-space: nowrap; font-weight: normal;">
                        <input type="checkbox" name="sync_detail_template" id="sync_detail_template" value="1"> 同步子栏目
                    </label>
                </div>
                <div class="form-description">标记为[仅PC端]的模板在移动端访问时将自动使用默认模板</div>
            </div>
            
            <div class="seo-section" style="margin-top: 20px; padding-top: 16px; border-top: 1px solid #eee;">
                <h3 class="collapsible" onclick="toggleSeoSection()" style="font-size: 14px; font-weight: 600; color: #666; cursor: pointer; margin-bottom: 12px;">
                    SEO设置 <span id="seo-toggle">▼</span>
                </h3>
                <div class="collapsible-content" id="seo-section">
                    <div class="form-group compact">
                        <label class="form-label" for="seo_title">SEO标题</label>
                        <div class="form-field">
                            <input type="text" id="seo_title" name="seo_title" value="<?php if(null !== ($category ?? null)): ?><?php echo (isset($category['seo_title'])) ? $category['seo_title'] : ""; ?><?php endif; ?>" class="form-control" style="max-width: 400px;">
                        </div>
                        <div class="form-description">可选，用于SEO的页面标题，留空则使用分类名称</div>
                    </div>

                    <div class="form-group compact">
                        <label class="form-label" for="seo_keywords">SEO关键词</label>
                        <div class="form-field">
                            <input type="text" id="seo_keywords" name="seo_keywords" value="<?php if(null !== ($category ?? null)): ?><?php echo (isset($category['seo_keywords'])) ? $category['seo_keywords'] : ""; ?><?php endif; ?>" class="form-control" style="max-width: 400px;">
                        </div>
                        <div class="form-description">可选，用于SEO的关键词，多个关键词用逗号分隔</div>
                    </div>

                    <div class="form-group compact">
                        <label class="form-label" for="seo_description">SEO描述</label>
                        <div class="form-field">
                            <textarea id="seo_description" name="seo_description" class="form-control" style="max-width: 400px;"><?php if(null !== ($category ?? null)): ?><?php echo (isset($category['seo_description'])) ? $category['seo_description'] : ""; ?><?php endif; ?></textarea>
                        </div>
                        <div class="form-description">可选，用于SEO的描述信息，留空则使用分类描述</div>
                    </div>
                </div>
            </div>
            
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i>
                    保存分类
                </button>

                <a href="category.php" class="btn btn-outline">
                    <i class="fas fa-times"></i>
                    取消
                </a>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 生成拼音按钮事件
        document.getElementById('generatePinyin').addEventListener('click', function() {
            var name = document.getElementById('name').value;
            if (name) {
                fetch('category.php?action=generate_pinyin&name=' + encodeURIComponent(name))
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('网络响应异常');
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data && data.pinyin) {
                            document.getElementById('pinyin').value = data.pinyin;
                        } else {
                            alert('拼音生成失败，请手动输入');
                        }
                    })
                    .catch(error => {
                        console.error('生成拼音出错:', error);
                        alert('拼音生成失败，请手动输入');
                    });
            } else {
                alert('请先输入分类名称');
            }
        });
        
        // 检查拼音有效性
        document.getElementById('pinyin').addEventListener('blur', function() {
            var pinyin = this.value;
            if (pinyin) {
                var form = document.getElementById('categoryForm');
                var categoryId = form.dataset.categoryId || 0;
                fetch('category.php?action=check_pinyin&pinyin=' + encodeURIComponent(pinyin) + '&id=' + categoryId)
                    .then(response => response.json())
                    .then(data => {
                        if (!data.valid) {
                            alert(data.message || '拼音已存在，请更换');
                        }
                    });
            }
        });
        
        // 图标预览
        document.getElementById('icon').addEventListener('input', function() {
            var iconPreview = document.getElementById('iconPreview');
            iconPreview.innerHTML = this.value ? '<i class="' + this.value + '"></i>' : '';
        });
    });
    
    // SEO部分的折叠显示
    function toggleSeoSection() {
        var content = document.getElementById('seo-section');
        var toggle = document.getElementById('seo-toggle');

        if (content.classList.contains('collapsed')) {
            content.classList.remove('collapsed');
            content.style.maxHeight = content.scrollHeight + 'px';
            toggle.textContent = '▲';
        } else {
            content.classList.add('collapsed');
            content.style.maxHeight = '0';
            toggle.textContent = '▼';
        }
    }

    // 初始化SEO部分为折叠状态
    document.addEventListener('DOMContentLoaded', function() {
        var content = document.getElementById('seo-section');
        content.style.maxHeight = content.scrollHeight + 'px';
        // 默认展开，如果需要默认折叠，取消下面两行注释
        // content.classList.add('collapsed');
        // content.style.maxHeight = '0';
    });
</script>

        </div>
        <!-- 主内容区 (结束) -->
    </div>
    <!-- wrapper (结束) -->

    <!-- jQuery (必须在Bootstrap之前加载) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 