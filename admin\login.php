<?php
// 定义安全常量
define('IN_BTMPS', true);
/**
 * 后台登录处理文件
 */
require_once('../include/common.inc.php');
require_once(INCLUDE_PATH . 'captcha.class.php');

// 检查是否已登录
if (isset($_SESSION['admin']) && $_SESSION['admin']['is_login'] === true) {
    header("Location: index.php");
    exit;
}

// 创建验证码对象
$captcha = new Captcha();

// 处理登录请求
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = isset($_POST['username']) ? filter($_POST['username']) : '';
    $password = isset($_POST['password']) ? $_POST['password'] : '';
    $verify_code = isset($_POST['verify_code']) ? filter($_POST['verify_code']) : '';
    
    // 检查验证码
    if (!$captcha->verify($verify_code)) {
        $login_error = "验证码错误";
    } else {
        // 验证用户名和密码
        $sql = "SELECT * FROM admins WHERE username = '" . $db->escape($username) . "' LIMIT 1";
        $result = $db->query($sql);
        
        if ($result && $db->num_rows($result) > 0) {
            $admin = $db->fetch_array($result);
            
            // 验证密码
            if (password_verify($password, $admin['password']) || md5($password) === $admin['password']) {
                // 登录成功
                $_SESSION['admin'] = array(
                    'id' => $admin['id'],
                    'username' => $admin['username'],
                    'realname' => $admin['realname'] ?? '',
                    'last_login' => $admin['last_login'] ?? '',
                    'is_login' => true
                );
                
                // 更新最后登录时间
                $sql = "UPDATE admins SET last_login = " . time() . " WHERE id = " . $admin['id'];
                $db->query($sql);
                
                // 跳转到后台首页
                header("Location: index.php");
                exit;
            } else {
                $login_error = "密码错误";
            }
        } else {
            $login_error = "用户名不存在";
        }
    }
}

// 修改模板目录为admin/template目录
$tpl->setTemplateDir(dirname(__FILE__) . '/template/');

// 分配模板变量
if (isset($login_error)) {
    $tpl->assign('login_error', $login_error);
}

// 显示登录模板
$tpl->display('login.htm'); 