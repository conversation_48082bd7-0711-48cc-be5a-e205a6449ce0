<?php
/**
 * 浏览次数API接口
 * 用于AJAX实时更新和获取浏览次数
 */

// 定义安全常量
define('IN_BTMPS', true);

// 引入公共文件
require_once dirname(__DIR__) . '/include/common.inc.php';

// 设置JSON响应头
header('Content-Type: application/json; charset=utf-8');

// 获取请求参数
$action = isset($_GET['action']) ? $_GET['action'] : '';
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;

// 验证参数
if (!$id || $id <= 0) {
    echo json_encode(array(
        'success' => false,
        'message' => '无效的信息ID'
    ));
    exit;
}

// 根据操作类型处理请求
switch ($action) {
    case 'update':
        // 更新浏览次数
        updateViewCount($id);
        
        // 获取更新后的浏览次数
        $view_count = getCurrentViewCount($id);
        
        echo json_encode(array(
            'success' => true,
            'view_count' => $view_count,
            'message' => '浏览次数更新成功'
        ));
        break;
        
    case 'get':
        // 只获取当前浏览次数，不更新
        $view_count = getCurrentViewCount($id);
        
        echo json_encode(array(
            'success' => true,
            'view_count' => $view_count
        ));
        break;
        
    case 'batch_update':
        // 批量更新浏览次数（用于页面离开时批量提交）
        $ids = isset($_POST['ids']) ? $_POST['ids'] : array();
        
        if (!is_array($ids) || empty($ids)) {
            echo json_encode(array(
                'success' => false,
                'message' => '无效的ID列表'
            ));
            exit;
        }
        
        $updated_counts = array();
        foreach ($ids as $post_id) {
            $post_id = intval($post_id);
            if ($post_id > 0) {
                updateViewCount($post_id);
                $updated_counts[$post_id] = getCurrentViewCount($post_id);
            }
        }
        
        echo json_encode(array(
            'success' => true,
            'updated_counts' => $updated_counts,
            'message' => '批量更新成功'
        ));
        break;
        
    default:
        echo json_encode(array(
            'success' => false,
            'message' => '无效的操作类型'
        ));
        break;
}
?>
