{if !empty($posts)}
    {foreach $posts as $post}
    <div class="post-item {if !empty($post.is_top_category) || !empty($post.is_top_subcategory)}is-top{/if}">
        <div class="post-image">
            {if !empty($post.image_url)}
            <img src="{$post.image_url}" alt="{$post.title}">
            {else}
            <img src="/static/images/no-image.png" alt="无图片">
            {/if}
        </div>
        <div class="post-content">
            <?php 
            $days = getRemainingDaysInt($post['expired_at']);
            if (!empty($post['expired_at']) && $days <= 0):
            ?>
            <a href="/{$category.pinyin}/{$post.id}.html" class="post-title post-expired">
                {if !empty($post.is_top_category) || !empty($post.is_top_subcategory)}<span class="top-tag">顶</span>{/if}
                {$post.title}
            </a>
            <?php else: ?>
            <a href="/{$category.pinyin}/{$post.id}.html" class="post-title">
                {if !empty($post.is_top_category) || !empty($post.is_top_subcategory)}<span class="top-tag">顶</span>{/if}
                {$post.title}
            </a>
            <?php endif; ?>
            <div class="post-info">
                <div class="post-meta">
                    {if !empty($post.region_name)}
                    <div class="post-region">{$post.region_name}</div>
                    {/if}
                    <div class="post-expire"><?php 
                        if (!empty($post['expired_at'])) {
                            $days = getRemainingDaysInt($post['expired_at']);
                            echo $days > 0 ? '剩余'.$days.'天' : '已过期';
                        } else {
                            echo '长期';
                        }
                    ?></div>
                </div>
                <div class="post-time">{$post.updated_at|friendlyTime}</div>
            </div>
        </div>
    </div>
    {/foreach}
{/if}
