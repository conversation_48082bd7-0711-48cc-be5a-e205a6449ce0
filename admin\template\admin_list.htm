{include file="header.htm"}

<div class="page-title">
    <h1>管理员管理</h1>
    <small style="margin-left: 15px; color: var(--text-secondary); font-weight: normal; font-size: 13px;">
        <i class="fas fa-users" style="color: var(--primary-color);"></i> {$pagination.total_items} 个管理员
    </small>
</div>

<style>
.table-responsive {
    overflow-x: auto;
}
.info-actions {
    display: flex;
    flex-wrap: nowrap;
    justify-content: flex-end;
    gap: 5px;
    min-width: 120px;
}
.info-actions .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    white-space: nowrap;
    text-decoration: none !important;
}
.info-actions .btn-group {
    display: inline-block;
}
a, a:hover, a:focus, a:active {
    text-decoration: none !important;
}
.nav-link {
    text-decoration: none !important;
}
.nav-link.active {
    background-color: #3490dc;
    color: #fff;
}

/* 淡色按钮样式 */
.btn-light-primary {
    background-color: #e6f0ff;
    color: #1b68ff;
    border: 1px solid #cce0ff;
}
.btn-light-primary:hover {
    background-color: #d1e3ff;
    color: #0056b3;
}
.btn-light-warning {
    background-color: #fff8e6;
    color: #ffa500;
    border: 1px solid #ffe6b3;
}
.btn-light-warning:hover {
    background-color: #fff0d1;
    color: #cc8400;
}
.btn-light-danger {
    background-color: #ffe6e6;
    color: #ff3333;
    border: 1px solid #ffb3b3;
}
.btn-light-danger:hover {
    background-color: #ffd1d1;
    color: #cc0000;
}
.btn-light-info {
    background-color: #e6f7ff;
    color: #00aaff;
    border: 1px solid #b3e0ff;
}
.btn-light-info:hover {
    background-color: #d1f0ff;
    color: #0088cc;
}
.btn-light-success {
    background-color: #e6ffe6;
    color: #00aa00;
    border: 1px solid #b3ffb3;
}
.btn-light-success:hover {
    background-color: #d1ffd1;
    color: #008800;
}
.btn-light-secondary {
    background-color: #f0f0f0;
    color: #666666;
    border: 1px solid #dddddd;
}
.btn-light-secondary:hover {
    background-color: #e0e0e0;
    color: #444444;
}

/* 分页样式 */
.simple-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 5px;
}
.pagination-btn {
    display: inline-block;
    padding: 5px 12px;
    background: #fff;
    border: 1px solid #ddd;
    color: #333;
    text-decoration: none;
    border-radius: 3px;
    transition: all 0.2s;
}
.pagination-btn:hover {
    background: #f8f9fa;
    border-color: #ccc;
}
.pagination-btn.active {
    background: #1b68ff;
    color: white;
    border-color: #1b68ff;
}
.pagination-btn.disabled {
    color: #aaa;
    background: #f8f8f8;
    cursor: not-allowed;
}

/* 固定表格列宽 */
.table {
    width: 100%;
    table-layout: fixed;
    white-space: nowrap;
}
.table th {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.table td {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
/* 设置每列的固定宽度 */
.table .col-id { width: 60px; }
.table .col-username { width: 120px; }
.table .col-realname { width: 120px; }
.table .col-role { width: 120px; }
.table .col-login-time { width: 150px; }
.table .col-status { width: 80px; }
.table .col-actions { width: 200px; text-align: right; }
</style>

<!-- 消息提示 -->
{if $message}
<div class="alert alert-success">
    <i class="fas fa-check-circle"></i>
    <div>
        <p>{$message}</p>
    </div>
</div>
{/if}
{if $error}
<div class="alert alert-danger">
    <i class="fas fa-exclamation-circle"></i>
    <div>
        <p>{$error}</p>
    </div>
</div>
{/if}

<!-- 操作栏 -->
<div class="section">
    <div class="card">
        <div class="card-title" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
            <span>管理系统账号，控制登录权限</span>
            <a href="admin.php?action=add" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                添加管理员
            </a>
        </div>

        <!-- 筛选表单 -->
        <div class="filter-form-expanded">
            <form method="get" action="admin.php">
                <div class="filter-row-expanded">
                    <div class="filter-item-expanded">
                        <label>关键词:</label>
                        <input type="text" name="keyword" value="{$keyword}" placeholder="搜索用户名或真实姓名..." class="form-control">
                    </div>

                    <div class="filter-buttons-expanded">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i>
                            筛选
                        </button>
                        <a href="admin.php" class="btn btn-outline">
                            <i class="fas fa-undo"></i>
                            重置
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 管理员列表 -->
<div class="section">
    <div class="card">
        <div class="table-responsive">
            <table class="table table-vcenter table-bordered table-hover">
                <thead>
                    <tr>
                        <th class="col-id">ID</th>
                        <th class="col-username">用户名</th>
                        <th class="col-realname">真实姓名</th>
                        <th class="col-role">角色</th>
                        <th class="col-login-time">最后登录时间</th>
                        <th class="col-status">状态</th>
                        <th class="col-actions">操作</th>
                    </tr>
                </thead>
                <tbody>
                    {if !$admins}
                    <tr>
                        <td colspan="7" class="text-center">暂无管理员数据</td>
                    </tr>
                    {else}
                    {foreach from=$admins item=item}
                    <tr>
                        <td>{$item.id}</td>
                        <td>{$item.username}</td>
                        <td>{$item.realname|default:'--'}</td>
                        <td>
                            {if $item.role == 'admin'}
                                <span class="badge badge-primary">管理员</span>
                            {elseif $item.role == 'super_admin'}
                                <span class="badge badge-danger">超级管理员</span>
                            {else}
                                <span class="badge badge-secondary">{$item.role}</span>
                            {/if}
                        </td>
                        <td>{$item.last_login_formatted}</td>
                        <td>
                            {if $item.status == 1}
                                <span class="badge badge-success">正常</span>
                            {else}
                                <span class="badge badge-danger">禁用</span>
                            {/if}
                        </td>
                        <td>
                            <div class="info-actions">
                                <a href="admin.php?action=edit&id={$item.id}" class="btn btn-sm btn-light-primary">编辑</a>
                                {if $admin.id != $item.id}
                                    <a href="admin.php?action=toggle_status&id={$item.id}" class="btn btn-sm {if $item.status == 1}btn-light-warning{else}btn-light-success{/if}" onclick="return confirm('{if $item.status == 1}禁用{else}启用{/if}管理员？')">{if $item.status == 1}禁用{else}启用{/if}</a>
                                    <a href="admin.php?action=delete&id={$item.id}" class="btn btn-sm btn-light-danger" onclick="return confirm('确定要删除这个管理员账号吗？此操作不可撤销！')">删除</a>
                                {else}
                                    <span class="btn btn-sm btn-light-secondary" disabled>当前账号</span>
                                {/if}
                            </div>
                        </td>
                    </tr>
                    {/foreach}
                    {/if}
                </tbody>
            </table>
        </div>
        
        <!-- 分页 -->
        {if $pagination.total_pages > 1}
        <div style="margin-top: 20px; display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap;">
            <div class="simple-pagination" style="justify-content: flex-end;">
                {if $pagination.current_page > 1}
                <a href="{$pagination.previous_link}" class="pagination-btn">上一页</a>
                {else}
                <span class="pagination-btn disabled">上一页</span>
                {/if}
                
                {foreach $pagination.page_links as $page => $link}
                <a href="{$link}" class="pagination-btn {if $page == $pagination.current_page}active{/if}">{$page}</a>
                {/foreach}
                
                {if $pagination.current_page < $pagination.total_pages}
                <a href="{$pagination.next_link}" class="pagination-btn">下一页</a>
                {else}
                <span class="pagination-btn disabled">下一页</span>
                {/if}
                
                <span style="margin-left: 10px; color: #6c757d; font-size: 14px;">共 {$pagination.total_pages} 页</span>
            </div>
        </div>
        {/if}
        </div>
    </div>
</div>

{include file="footer.htm"}