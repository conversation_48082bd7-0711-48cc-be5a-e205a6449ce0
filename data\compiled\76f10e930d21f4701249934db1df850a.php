<!DOCTYPE html>
<?php
// 获取主题设置
$theme = isset($_COOKIE['site_theme']) ? $_COOKIE['site_theme'] : 'red';
$theme_class = 'theme-' . $theme;
?>
<html lang="zh-CN" class="<?php echo $theme_class; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>选择栏目 - <?php echo $site_name; ?></title>
    <link rel="stylesheet" href="/static/font-awesome/css/all.min.css">
    <link rel="stylesheet" href="/static/css/themes.css">
    <link rel="stylesheet" href="/template/m/css/common.css">
    <link rel="stylesheet" href="/template/m/css/post.css">
    <style>
    /* 简约主题头部样式 */
    html.theme-simple header {
        background-color: #ffffff !important;
        color: #333333 !important;
        border-bottom: 1px solid #eeeeee;
    }
    
    html.theme-simple header .header-title,
    html.theme-simple header .header-back i,
    html.theme-simple header .header-right a {
        color: #333333 !important;
    }
    
    html.theme-simple .header-inner {
        background-color: #ffffff !important;
    }
    </style>
  
</head>
<body>
    <header>
        <div class="header-inner">
            <div class="header-left">
                <a href="javascript:history.back();" class="header-back"><i class="fas fa-chevron-left"></i></a>
            </div>
            <div class="header-title"><?php echo isset($selected_parent_id) ? '选择子分类' : '选择发布栏目'; ?></div>
            <div class="header-right"></div>
        </div>
    </header>

    <div class="tip-panel">
        <i class="fas fa-info-circle" style="margin-right: 8px;"></i>
        <?php if (isset($selected_parent_id)): ?>
            请选择您要发布信息的具体子分类
        <?php else: ?>
            请选择合适的栏目发布您的信息
        <?php endif; ?>
    </div>
    
    <div class="category-container">
        <div class="left-menu">
            <?php if (isset($selected_parent_id)): ?>
                <!-- 显示特定一级分类及其子分类 -->
                <?php foreach ($selected_categories as $index => $category): ?>
                    <?php $activeClass = $index === 0 ? 'active' : ''; ?>
            <div class="category-item <?php echo $activeClass; ?>" onclick="showSubcategories(<?php echo $category['id']; ?>, this)">
                <i class="fas fa-graduation-cap" style="color: #7b68ee; font-size: 20px; margin-right: 15px;"></i>
                <span class="name"><?php echo $category['name']; ?></span>
            </div>
            <?php endforeach; ?>
            <?php else: ?>
                <?php
                // 获取所有分类（使用缓存）
                $categories = $GLOBALS['cached_categories'];

                // 按父子关系整理分类
                $parentCategories = array();
                $subCategories = array();

                foreach ($categories as $cat) {
                    if (isset($cat['parent_id']) && $cat['parent_id'] == 0) {
                        $parentCategories[] = $cat;
                    } else if (isset($cat['parent_id']) && $cat['parent_id'] > 0) {
                        $subCategories[$cat['parent_id']][] = $cat;
                    } else {
                        // 没有parent_id的分类视为顶级分类
                        $cat['parent_id'] = 0;
                        $parentCategories[] = $cat;
                    }
                }

                // 遍历父级栏目
                foreach ($parentCategories as $index => $category) {
                    $activeClass = $index === 0 ? 'active' : '';
                ?>
            <div class="category-item <?php echo $activeClass; ?>" onclick="showSubcategories(<?php echo $category['id']; ?>, this)">
                <?php if (isset($category['icon']) && $category['icon']) { ?>
                <img src="static/icons/<?php echo $category['icon']; ?>" class="icon" alt="<?php echo $category['name']; ?>">
                <?php } else { 
                    // 根据分类名称或ID选择不同的图标
                    $icon_class = 'fa-folder';
                    $icon_color = '#ff6600';
                    
                    // 根据分类名称判断
                    $category_name = strtolower($category['name']);
                    
                    if (strpos($category_name, '招聘') !== false || strpos($category_name, '求职') !== false) {
                        $icon_class = 'fa-briefcase';
                        $icon_color = '#4285f4';
                    } 
                    else if (strpos($category_name, '房屋') !== false || strpos($category_name, '房产') !== false) {
                        $icon_class = 'fa-house';
                        $icon_color = '#00a878';
                    }
                    else if (strpos($category_name, '车') !== false || strpos($category_name, '汽车') !== false) {
                        $icon_class = 'fa-car';
                        $icon_color = '#e53935';
                    }
                    else if (strpos($category_name, '教育') !== false || strpos($category_name, '培训') !== false) {
                        $icon_class = 'fa-graduation-cap';
                        $icon_color = '#7b68ee';
                    }
                    else if (strpos($category_name, '二手') !== false || strpos($category_name, '物品') !== false) {
                        $icon_class = 'fa-tag';
                        $icon_color = '#ff6b01';
                    }
                    else if (strpos($category_name, '服务') !== false) {
                        $icon_class = 'fa-bell-concierge';
                        $icon_color = '#4285f4';
                    }
                    else if (strpos($category_name, '商务') !== false || strpos($category_name, '生意') !== false) {
                        $icon_class = 'fa-handshake';
                        $icon_color = '#00a878';
                    }
                    else if (strpos($category_name, '交友') !== false || strpos($category_name, '婚恋') !== false) {
                        $icon_class = 'fa-heart';
                        $icon_color = '#e53935';
                    }
                    else if (strpos($category_name, '餐饮') !== false || strpos($category_name, '美食') !== false) {
                        $icon_class = 'fa-utensils';
                        $icon_color = '#ff6b01';
                    }
                    // 可以继续添加更多分类判断
                ?>
                <i class="fas <?php echo $icon_class; ?>" style="color: <?php echo $icon_color; ?>; font-size: 20px; margin-right: 15px;"></i>
                <?php } ?>
                <span class="name"><?php echo $category['name']; ?></span>
            </div>
            <?php } ?>
            <?php endif; ?>
        </div>
        
        <div class="right-content">
            <?php if (isset($selected_parent_id)): ?>
                <!-- 显示特定一级分类的子分类 -->
                <?php foreach ($selected_categories as $index => $category): ?>
                    <?php $activeClass = $index === 0 ? 'active' : ''; ?>
                    <?php $hasSubcategories = isset($sub_categories_map[$category['id']]) && !empty($sub_categories_map[$category['id']]); ?>
                    <div class="subcategory-list <?php echo $activeClass; ?>" id="sub-<?php echo $category['id']; ?>">
                        <?php if ($hasSubcategories): ?>
                            <?php foreach ($sub_categories_map[$category['id']] as $subcat): ?>
                                <a href="post.php?category_id=<?php echo $subcat['id']; ?>" class="subcategory-item">
                                    <?php echo $subcat['name']; ?> <i class="fas fa-angle-right" style="float:right; color:#ccc;"></i>
                                </a>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="subcategory-item" style="color:#999; text-align:center;">
                                该分类下暂无子栏目
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>

                <!-- 添加返回选择其他分类的按钮 -->
                <div style="padding: 20px; text-align: center; border-top: 1px solid #f0f0f0;">
                    <a href="post.php?action=select" style="display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 20px; font-size: 14px;">
                        <i class="fas fa-arrow-left" style="margin-right: 6px;"></i> 选择其他分类
                    </a>
                </div>
            <?php else: ?>
                <!-- 显示所有分类的子分类（原有逻辑） -->
                <?php
                // 遍历父级栏目的子栏目
                foreach ($parentCategories as $index => $category) {
                    $activeClass = $index === 0 ? 'active' : '';
                    $hasSubcategories = isset($subCategories[$category['id']]) && !empty($subCategories[$category['id']]);
                ?>
                <div class="subcategory-list <?php echo $activeClass; ?>" id="sub-<?php echo $category['id']; ?>">
                    <?php
                    if ($hasSubcategories) {
                        foreach ($subCategories[$category['id']] as $subcat) {
                    ?>
                        <a href="post.php?category_id=<?php echo $subcat['id']; ?>" class="subcategory-item">
                            <?php echo $subcat['name']; ?> <i class="fas fa-angle-right" style="float:right; color:#ccc;"></i>
                        </a>
                    <?php
                        }
                    } else {
                    ?>
                        <div class="subcategory-item" style="color:#999; text-align:center;">
                            该分类下暂无子栏目
                        </div>
                    <?php
                    }
                    ?>
                </div>
                <?php } ?>
            <?php endif; ?>
        </div>
    </div>

    <script>
    <?php if (!isset($selected_parent_id)): ?>
    // 只在显示所有分类时需要切换逻辑
    function showSubcategories(id, element) {
        // 隐藏所有子分类
        var subcategories = document.querySelectorAll('.subcategory-list');
        for (var i = 0; i < subcategories.length; i++) {
            subcategories[i].classList.remove('active');
        }

        // 移除所有分类项的激活状态
        var categoryItems = document.querySelectorAll('.category-item');
        for (var i = 0; i < categoryItems.length; i++) {
            categoryItems[i].classList.remove('active');
        }

        // 激活当前点击的分类项
        element.classList.add('active');

        // 显示对应的子分类
        var targetSubcategory = document.getElementById('sub-' + id);
        if (targetSubcategory) {
            targetSubcategory.classList.add('active');
        }
    }

    // 页面加载时自动显示第一个分类的子分类
    window.onload = function() {
        var firstCategoryItem = document.querySelector('.category-item');
        if (firstCategoryItem) {
            var idMatch = firstCategoryItem.getAttribute('onclick').match(/showSubcategories\((\d+)/);
            if (idMatch && idMatch[1]) {
                var id = idMatch[1];
                var subcategory = document.getElementById('sub-' + id);
                if (subcategory) {
                    subcategory.classList.add('active');
                }
            }
        }
    };
    <?php endif; ?>
    </script>
</body>
</html> 