<!-- 移动端头部 -->
<header class="mobile-header">
    <div class="header-inner">
        <a href="javascript:history.back()" class="header-back">
            <i class="fas fa-arrow-left"></i>
        </a>
        <h1 class="header-title">{if isset($page.title)}{$page.title}{else}页面{/if}</h1>
        <a href="/" class="header-home">
            <i class="fas fa-home"></i>
        </a>
    </div>
</header>

<style>
.mobile-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 50px;
    background: var(--primary-color);
    color: white;
    z-index: 1000;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-inner {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 15px;
    max-width: 100%;
}

.header-back,
.header-home {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    color: white;
    text-decoration: none;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.header-back:hover,
.header-home:hover,
.header-back:active,
.header-home:active {
    background-color: rgba(255,255,255,0.1);
    color: white;
}

.header-title {
    flex: 1;
    text-align: center;
    font-size: 16px;
    font-weight: 500;
    margin: 0;
    padding: 0 10px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: white;
}

/* 简约主题样式 */
html.theme-simple .mobile-header {
    background: #ffffff;
    color: #333333;
    border-bottom: 1px solid #eeeeee;
}

html.theme-simple .header-back,
html.theme-simple .header-home,
html.theme-simple .header-title {
    color: #333333;
}

html.theme-simple .header-back:hover,
html.theme-simple .header-home:hover,
html.theme-simple .header-back:active,
html.theme-simple .header-home:active {
    background-color: rgba(0,0,0,0.05);
    color: #333333;
}

/* 为页面内容添加顶部间距 */
body {
    padding-top: 50px;
}
</style>