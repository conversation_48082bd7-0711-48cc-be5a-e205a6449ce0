# 后台管理系统样式指南

## 概述

本文档提供了后台管理系统的统一样式指南，确保所有页面的样式一致性。

## CSS 变量

系统使用 CSS 变量定义主题色彩：

```css
:root {
    --primary-color: #1b68ff;      /* 主色调 */
    --primary-hover: #0045ce;      /* 主色调悬停 */
    --success-color: #3ad29f;      /* 成功色 */
    --warning-color: #eea303;      /* 警告色 */
    --danger-color: #f82f58;       /* 危险色 */
    --info-color: #17a2b8;         /* 信息色 */
    --text-color: #001a4e;         /* 主文本色 */
    --text-secondary: #6c757d;     /* 次要文本色 */
    --border-color: #e9ecef;       /* 边框色 */
    --bg-gray: #f8f9fa;            /* 背景灰色 */
}
```

## 布局结构

### 基本页面结构

```html
{include file="header.htm"}

<!-- 页面标题 -->
<div class="page-title">
    <h1>页面标题</h1>
    <div class="d-flex gap-2">
        <a href="#" class="btn btn-primary">主要操作</a>
        <a href="#" class="btn btn-outline">次要操作</a>
    </div>
</div>

<!-- 内容区域 -->
<div class="section">
    <div class="card">
        <h3 class="card-title">卡片标题</h3>
        <!-- 卡片内容 -->
    </div>
</div>

{include file="footer.htm"}
```

## 组件样式

### 1. 按钮组件

#### 基础按钮
```html
<button class="btn btn-primary">主要按钮</button>
<button class="btn btn-success">成功按钮</button>
<button class="btn btn-warning">警告按钮</button>
<button class="btn btn-danger">危险按钮</button>
<button class="btn btn-outline">普通按钮</button>
```

#### 淡色按钮
```html
<button class="btn btn-light-primary">淡色主要</button>
<button class="btn btn-light-success">淡色成功</button>
<button class="btn btn-light-warning">淡色警告</button>
<button class="btn btn-light-danger">淡色危险</button>
```

#### 按钮尺寸
```html
<button class="btn btn-primary">默认尺寸</button>
<button class="btn btn-sm btn-primary">小按钮</button>
<button class="btn btn-xs btn-primary">超小按钮</button>
```

### 2. 表单组件

#### 基础表单
```html
<form class="form-horizontal">
    <div class="form-group">
        <label class="form-label">标签</label>
        <input type="text" class="form-control" placeholder="请输入内容">
    </div>
    
    <div class="form-group">
        <label class="form-label">下拉选择</label>
        <select class="form-select">
            <option value="">请选择</option>
            <option value="1">选项1</option>
        </select>
    </div>
    
    <div class="form-group">
        <label class="form-label">文本域</label>
        <textarea class="form-textarea" placeholder="请输入内容"></textarea>
    </div>
</form>
```

#### 表单行布局
```html
<div class="form-row">
    <div class="form-col">
        <div class="form-group">
            <label class="form-label">左侧字段</label>
            <input type="text" class="form-control">
        </div>
    </div>
    <div class="form-col">
        <div class="form-group">
            <label class="form-label">右侧字段</label>
            <input type="text" class="form-control">
        </div>
    </div>
</div>
```

#### 单选框和复选框
```html
<div class="form-group">
    <label class="form-label">性别</label>
    <div class="d-flex gap-3">
        <div class="form-check">
            <input type="radio" name="gender" id="male">
            <label class="form-check-label" for="male">男</label>
        </div>
        <div class="form-check">
            <input type="radio" name="gender" id="female">
            <label class="form-check-label" for="female">女</label>
        </div>
    </div>
</div>
```

#### 开关按钮
```html
<div class="form-group">
    <label class="form-label">开启通知</label>
    <label class="form-switch">
        <input type="checkbox">
        <span class="switch-slider"></span>
    </label>
</div>
```

### 3. 表格组件

#### 基础表格
```html
<div class="table-responsive">
    <table class="table">
        <thead>
            <tr>
                <th>列标题1</th>
                <th>列标题2</th>
                <th style="text-align: right;">操作</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>数据1</td>
                <td>数据2</td>
                <td>
                    <div class="action-buttons">
                        <a href="#" class="btn btn-sm btn-light-primary">编辑</a>
                        <a href="#" class="btn btn-sm btn-light-danger">删除</a>
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
</div>
```

#### 筛选表单
```html
<div class="filter-form">
    <div class="filter-item">
        <label class="filter-label">关键字:</label>
        <input type="text" class="form-control" placeholder="搜索...">
    </div>
    <div class="filter-item">
        <label class="filter-label">状态:</label>
        <select class="form-select">
            <option value="">全部</option>
            <option value="1">启用</option>
            <option value="0">禁用</option>
        </select>
    </div>
    <div class="filter-buttons">
        <button class="btn btn-sm btn-primary">筛选</button>
        <button class="btn btn-sm btn-outline">重置</button>
    </div>
    <div class="ml-auto">
        <button class="btn btn-sm btn-light-success">新增</button>
    </div>
</div>
```

### 4. 标签组件

```html
<span class="tag tag-primary">主要标签</span>
<span class="tag tag-success">成功标签</span>
<span class="tag tag-warning">警告标签</span>
<span class="tag tag-danger">危险标签</span>
```

### 5. 警告框组件

```html
<div class="alert alert-info">
    <i class="fas fa-info-circle"></i>
    <span>信息提示</span>
</div>

<div class="alert alert-success">
    <i class="fas fa-check-circle"></i>
    <span>成功提示</span>
</div>

<div class="alert alert-warning">
    <i class="fas fa-exclamation-circle"></i>
    <span>警告提示</span>
</div>

<div class="alert alert-danger">
    <i class="fas fa-times-circle"></i>
    <span>错误提示</span>
</div>
```

### 6. 统计卡片

```html
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-icon" style="background-color: var(--primary-color);">
            <i class="fas fa-users"></i>
        </div>
        <div class="stat-title">统计标题</div>
        <div class="stat-value">12,345</div>
        <div class="stat-trend trend-up">
            <i class="fas fa-arrow-up"></i>
            <span>12.5%</span>
        </div>
    </div>
</div>
```

## 工具类

### 布局工具类
```html
<div class="d-flex">弹性布局</div>
<div class="d-flex gap-2">带间距的弹性布局</div>
<div class="justify-content-between">两端对齐</div>
<div class="align-items-center">垂直居中</div>
<div class="text-center">文本居中</div>
<div class="text-right">文本右对齐</div>
```

### 间距工具类
```html
<div class="mb-3">下边距</div>
<div class="mt-3">上边距</div>
<div class="ml-auto">左边距自动</div>
<div class="mr-auto">右边距自动</div>
```

## 响应式设计

系统支持响应式设计，在移动设备上会自动调整布局：

- 768px 以下：侧边栏折叠，表单垂直布局
- 480px 以下：进一步优化移动端显示

## 最佳实践

1. **保持一致性**：使用统一的 CSS 类名和组件结构
2. **避免内联样式**：尽量使用 CSS 类而不是内联样式
3. **语义化标签**：使用合适的 HTML 标签和 ARIA 属性
4. **图标使用**：统一使用 Font Awesome 图标库
5. **颜色使用**：使用 CSS 变量定义的主题色彩

## 示例页面

参考 `admin/template/style_demo.htm` 查看完整的组件示例。
