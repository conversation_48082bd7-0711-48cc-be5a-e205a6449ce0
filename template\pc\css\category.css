/* 面包屑导航样式 - 扁平化设计 */
.breadcrumb-container {
    margin: 0px 0 10px 0;
    padding: 10px;
    border: none;
    background-color: #fff;
 
    width: 100%;
    box-sizing: border-box;
    margin-top: 10px;
}

.breadcrumb {
    font-size: 14px;
    color: #666;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

.breadcrumb a {
    color: #666;
    text-decoration: none;
    transition: color 0.3s;
}

.breadcrumb a:hover {
    color: #EE3131;
    text-decoration: none;
}

.breadcrumb .separator {
    margin: 0 8px;
    color: #ccc;
}

.breadcrumb .current {
    color: #EE3131;
}

/* 列表页容器样式 */
.yui-index-info-list {
    display: flex;
    justify-content: space-between;
    width: 100%;
    background-color: transparent;
    border-radius: 0;
    padding: 0;
    margin-bottom: 20px;
    box-shadow: none;
    overflow: hidden;
    height: auto;
}

.yui-index-info-list-left { 
    width: 908px;
    float: none;
    background-color: #fff;
    border-radius: 4px;
    padding: 0px 10px;
    margin-right: 10px;
}

.yui-index-info-list-right { 
    width: 260px;
    float: none;
    background-color: transparent;
    border-radius: 0;
    padding: 0;
    box-shadow: none;
}

/* 右侧子容器统一宽度自适应 */
.yui-index-info-list-right .bbs-hot,
.yui-index-info-list-right .yui-service,
.yui-index-info-list-right .yui-topic-list,
.yui-index-info-list-right .yui-img-list {
    width: 100%;
    box-sizing: border-box;
}

/* 列表标签样式 */
.yui-list-tabs {
    background: transparent;
    border-radius: 0;
    margin-bottom: 20px;
    border: none;
    padding: 0;
}

.tab-header {
    border-bottom: 1px solid #eee;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0;
}

.tab-header ul {
    display: flex;
    gap: 40px;
}

.tab-header li {
    font-size: 16px;
    color: #666;
    cursor: pointer;
    padding: 0 10px;
    height: 56px;
    line-height: 56px;
    position: relative;
    transition: all 0.3s;
}

.tab-header li:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 3px;
    background: #EE3131;
    border-radius: 3px 3px 0 0;
    transition: width 0.3s ease-in-out;
}

.tab-header li:hover {
    color: #EE3131;
}

.tab-header li:hover:after {
    width: 100%;
}

.tab-header li.active {
    color: #000;
    font-weight: bold;
    font-size: 17px;
}

.tab-header li.active:after {
    width: 100%;
}

/* 列表头部样式 */
.list-header {
    display: flex;
    align-items: center;
    padding: 12px 5px;
    border-bottom: 1px solid #eee;
    background: linear-gradient(to bottom, #f9f9f9, #f5f5f5);
    border-radius: 4px 4px 0 0;
    box-shadow: 0 1px 2px rgba(0,0,0,0.03);
}

.list-title {
    flex: 1;
    font-weight: 500;
    font-size: 15px;
    color: #333;
    padding-left: 10px;
    position: relative;
}

.list-meta {
    display: flex;
    min-width: 380px;
    color: #666;
    font-size: 13px;
    font-weight: 500;
    justify-content: flex-end;
    margin-right: 0;
}

.list-meta span {
    width: 78px;
    text-align: center;
    margin: 0 8px;
}

/* 信息列表样式 */
.info-list {
    margin-bottom: 10px;
}

.info-list li {
    display: flex;
    justify-content: space-between;
    padding: 10px 5px;
    border-bottom: 1px solid #f5f5f5;
    line-height: 24px;
}

.info-list li:hover {
    background: #f9f9f9;
}

.info-title {
    flex: 1;
    padding-right: 20px;
    line-height: 24px;
    position: relative;
}

.info-title a {
    color: #333;
    font-size: 15px;
    transition: color 0.3s;
    display: inline-block;
    position: relative;
}

.info-title a:hover {
    color: #3366cc;
}

.info-meta {
    display: flex;
    min-width: 380px;
    color: #999;
    font-size: 13px;
    justify-content: flex-end;
    margin-right: 0;
}

.info-meta span {
    width: 78px;
    text-align: center;
    margin: 0 8px;
}

.info-meta span.price {
    color: #ff5722;
    font-weight: 500;
}

.info-meta span.red,
.info-meta span.price b.red {
    color: #EE3131;
    font-weight: bold;
}

/* 置顶信息样式 */
.info-list.pinned {
    background: #fff9f9;
    border: 1px solid #ffe7e7;
    border-radius: 4px;
    margin-bottom: 15px;
}

.info-list.pinned li {
    border-bottom: 1px solid #ffe7e7;
}

.info-list.pinned li:last-child {
    border-bottom: none;
}

.info-list.pinned li:hover {
    background: #fff5f5;
}

.info-list.pinned .info-title:before {
    content: '置顶';
    display: inline-block;
    padding: 0 6px;
    height: 20px;
    line-height: 20px;
    background: #EE3131;
    color: #fff;
    font-size: 12px;
    border-radius: 2px;
    margin-right: 8px;
    transform: scale(0.9);
}

/* 图文列表样式 */
.info-imglist {
    background: #fff;
    border-radius: 4px;
    overflow: hidden;
}

.info-imglist li {
    display: flex;
    padding: 10px 20px;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.3s ease;
}

.info-imglist li:hover {
    background-color: #f9f9f9;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.info-img {
    width: 133px;
    height: 100px;
    margin-right: 15px;
    overflow: hidden;
    border-radius: 4px;
    flex-shrink: 0;
}

.info-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.info-img:hover img {
    transform: scale(1.05);
}

.info-imgcontent {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.info-imgtitle {
    margin-bottom: 5px;
}

.info-imgtitle a {
    font-size: 18px;
    color: #333;
    font-weight: 500;
    transition: color 0.3s;
    line-height: 1.4;
}

.info-imgtitle a:hover {
    color: #EE3131;
}

.info-summary {
    color: #666;
    font-size: 14px;
    line-height: 1.6;
    margin-bottom: 8px;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.info-imgmeta {
    display: flex;
    color: #999;
    font-size: 13px;
    flex-wrap: wrap;
}

.info-imgmeta span {
    margin-right: 20px;
    display: flex;
    align-items: center;
}

.info-imgmeta i {
    margin-right: 5px;
    font-size: 14px;
}

/* 卡片式列表样式 */
.card-imglist {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -10px;
}

.card-imglist li {
    width: calc(25% - 20px);
    margin: 0 10px 20px;
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    overflow: hidden;
    transition: all 0.3s ease;
}

.card-img {
    position: relative;
    height: 150px;
    overflow: hidden;
}

.card-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.card-price {
    position: absolute;
    bottom: 10px;
    left: 10px;
    background: rgba(238, 49, 49, 0.9);
    color: #fff;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 13px;
    font-weight: 500;
}

.card-tag {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.5);
    color: #fff;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.card-content {
    padding: 12px;
}

.card-title {
    margin-bottom: 10px;
    height: 40px;
    overflow: hidden;
}

.card-title a {
    color: #333;
    font-size: 15px;
    font-weight: 500;
    line-height: 1.4;
    transition: color 0.3s;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.card-imglist li:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.card-img:hover img {
    transform: scale(1.08);
}

/* 右侧图片列表样式 */
.yui-index-info-list-right .yui-img-list {
    margin-bottom: 10px;
}

.yui-index-info-list-right .yui-img-list img {
    width: 100%;

    height: auto;
    margin: 5px 0;
    box-sizing: border-box;
    display: block;
}

/* 横向网格列表样式 */
.horizontal-grid-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.grid-item {
    margin-bottom: 20px;
    border: 1px solid #e5e5e5;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
}

/* 右侧模块样式 */
.bbs-hot {
    background-color: #fff;
    border-radius: 4px;
    margin-bottom: 10px;
    padding: 0 10px 10px 10px;

}

.yui-small-list ul {
    padding: 5px 0;
}

.yui-small-list ul li {
    line-height: 28px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.yui-small-list ul li a {
    color: #666;
    font-size: 14px;
}

.yui-small-list ul li a:hover {
    color: #EE3131;
}

/* 暂无信息样式 */
.no-info-message {
    padding: 60px 0;
    margin: 20px 0;
    text-align: center;
}

.no-info-icon {
    display: block;
    width: 48px;
    height: 48px;
    margin: 0 auto 15px;
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23ccc"><path d="M20 6h-4V4c0-1.1-.9-2-2-2h-4c-1.1 0-2 .9-2 2v2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2zM10 4h4v2h-4V4zm10 16H4V8h16v12z"/><path d="M12 17.5a5.5 5.5 0 1 1 0-11 5.5 5.5 0 0 1 0 11zm0-9.5a4 4 0 1 0 0 8 4 4 0 0 0 0-8z"/></svg>') no-repeat center !important;
}

.no-info-message p {
    color: #999;
    font-size: 14px;
    margin: 0;
}

/* 发布按钮样式 */
.index-list-post a {
    display: inline-block;
    padding: 0 20px;
    height: 32px;
    line-height: 32px;
    background: #3092d5;
    color: #fff;
    border-radius: 4px;
}

.index-list-post a:hover {
    opacity: 0.9;
}

.top-tag {
    display: inline-block;
    width: 16px;
    height: 16px;
    vertical-align: middle;
    margin-right: 5px;
   
    background-size: contain;
}

.top-tag-category {
    background-image: url('../images/icon_title2.gif');
}

/* 当前分类标签样式 */
.yui_cur2{color:#c4284d;font-weight:700} /* 当前分类高亮 */

/* 横向格子布局样式 */
.horizontal-grid-list{list-style:none;padding:0;margin:0} /* 网格列表容器 */
.grid-item{margin-bottom:20px;border:1px solid #e5e5e5;border-radius:8px;overflow:hidden;box-shadow:0 2px 8px rgba(0,0,0,.08);transition:all .3s ease} /* 网格项样式 */
.grid-item:hover{transform:translateY(-3px);box-shadow:0 4px 12px rgba(0,0,0,.15);border-color:#ddd} /* 网格项悬停效果 */
.grid-content{display:flex;background:#fff} /* 网格内容布局 */
.grid-img{width:220px;height:165px;overflow:hidden;flex-shrink:0} /* 图片容器 */
.grid-img img{width:100%;height:100%;object-fit:cover;transition:transform .5s ease} /* 图片样式 */
.grid-img img:hover{transform:scale(1.1)} /* 图片悬停放大效果 */
.grid-info{padding:15px 20px;flex:1;position:relative} /* 信息区域布局 */
.grid-title{margin:0 0 10px;font-size:18px;line-height:24px;font-weight:600} /* 标题样式 */
.grid-title a{color:#333;text-decoration:none} /* 标题链接样式 */
.grid-title a:hover{color:#c4284d} /* 标题链接悬停效果 */
.grid-desc{margin:0 0 10px;font-size:14px;line-height:22px;color:#666;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical} /* 描述文本样式 */
.grid-meta{display:flex;flex-wrap:wrap;margin-bottom:12px;gap:15px} /* 元数据布局 */
.grid-meta span{font-size:13px;color:#777;display:inline-flex;align-items:center} /* 元数据项样式 */
.grid-meta i{margin-right:4px;font-size:14px} /* 元数据图标样式 */
.grid-price-info{display:flex;justify-content:space-between;align-items:center;margin-top:10px} /* 价格信息布局 */
.grid-price{font-size:15px;color:#333} /* 价格文本样式 */
.price-value{font-size:20px;color:#c4284d;font-weight:700;margin:0 3px} /* 价格数值样式 */
.price-unit{font-size:14px} /* 价格单位样式 */
.grid-contact{display:flex;gap:10px} /* 联系按钮组布局 */
.contact-phone,.btn-view{display:inline-block;padding:5px 12px;font-size:13px;border-radius:4px;text-decoration:none;transition:all .3s} /* 联系按钮基础样式 */
.contact-phone{background:#f0f7ff;color:#0a66c2;border:1px solid #d1e5ff} /* 电话按钮样式 */
.btn-view{background:#c4284d;color:#fff;border:1px solid #c4284d} /* 查看按钮样式 */
.contact-phone:hover{background:#e0f0ff} /* 电话按钮悬停效果 */
.btn-view:hover{background:#b21d43} /* 查看按钮悬停效果 */

/* 分页样式 */
.pagination{margin:30px 0;display:flex;justify-content:center;align-items:center;gap:4px} /* 分页容器 */
.pagination a,.pagination span{display:inline-flex;align-items:center;justify-content:center;min-width:26px;height:26px;font-size:13px;border:1px solid #e0e0e0;background-color:#fff;color:#333;text-decoration:none;border-radius:3px;transition:all .3s ease} /* 分页按钮基础样式 */
.pagination a:hover{background-color:#f5f5f5;border-color:#d0d0d0;color:#c4284d} /* 分页按钮悬停效果 */
.pagination .current{background-color:#c4284d;color:#fff;border-color:#c4284d;font-weight:500} /* 当前页样式 */
.pagination .disabled{color:#999;cursor:not-allowed;background-color:#f5f5f5;border-color:#e0e0e0} /* 禁用按钮样式 */
.pagination .prev,.pagination .next{padding:0 12px;min-width:60px} /* 上一页下一页按钮样式 */
.pagination .ellipsis{border:none;padding:0 4px;color:#666;min-width:20px} /* 省略号样式 */

/* 加载更多按钮样式 - 简洁版 v2.0 */
.pagination-container {
    margin: 20px 0;
    text-align: center;
    width: 100%;
    display: flex;
    justify-content: center;
    clear: both;
}

.load-more {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    color: #666;
    text-align: center;
    padding: 10px 20px;
    font-size: 14px;
    border-radius: 20px;
    border: 1px solid #e0e0e0;
    cursor: pointer;
    position: relative;
    transition: all 0.2s ease;
    margin: 0 auto;
    font-weight: 500;
    box-shadow: 0 1px 3px rgba(0,0,0,0.08);
    min-width: 120px;
    backdrop-filter: blur(10px);
}

.load-more:before {
    content: '↓';
    font-size: 12px;
    transition: transform 0.2s ease;
    opacity: 0.7;
}

.load-more:hover {
    background: #c4284d;
    border-color: #c4284d;
    color: #fff;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(196,40,77,0.2);
}

.load-more:hover:before {
    transform: translateY(2px);
}

.load-more.loading {
    color: #999;
    pointer-events: none;
    background: #f8f9fa;
    border-color: #e9ecef;
}

.load-more.loading:before {
    display: none;
}

.load-more.loading:after {
    content: '';
    display: inline-block;
    width: 14px;
    height: 14px;
    border: 2px solid #e9ecef;
    border-top: 2px solid #c4284d;
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
}

.load-more-end {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    background-color: #f8f9fa;
    color: #999;
    text-align: center;
    padding: 8px 16px;
    font-size: 13px;
    border-radius: 16px;
    border: 1px solid #e9ecef;
    margin: 0 auto;
    font-weight: 500;
}

.load-more-end:before {
    content: '✓';
    font-size: 12px;
    color: #28a745;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* PC端加载更多按钮的微妙脉冲动画 */
@keyframes pulse-subtle {
    0% { box-shadow: 0 1px 3px rgba(0,0,0,0.08); }
    50% { box-shadow: 0 2px 6px rgba(0,0,0,0.12); }
    100% { box-shadow: 0 1px 3px rgba(0,0,0,0.08); }
}

.load-more {
    animation: pulse-subtle 3s ease-in-out infinite;
}

/* 标签样式 */
.top-tag{display:inline-block;padding:0;background-color:#EE3131;color:#fff;font-size:12px;border-radius:2px;margin-left:5px;line-height:20px;vertical-align:middle;text-align:center;width:20px;height:20px;box-sizing:border-box} /* 置顶标签 - 20px正方形小圆角 */
.img-tag{display:inline-block;padding:2px 5px;background-color:#3498db;color:#fff;font-size:12px;border-radius:2px;margin-right:5px} /* 图片标签 */

/* 置顶信息标题样式 - 与首页保持一致 */
.info-list li.top-item .info-title a,
.category-list li.top-item .info-title a {
    color: #EE3131 !important;
    font-weight: normal;
}

.info-list li.top-item .info-title a:hover,
.category-list li.top-item .info-title a:hover {
    color: #EE3131 !important;
    text-decoration: underline;
}