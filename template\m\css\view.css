/* 详情页样式 */

/* 头部样式现在统一在common.css中 */

/* 统一背景色 */
body {
    background-color: #f5f5f5 !important;
}

/* 详情页内容样式 */
.post-header {
    background-color: var(--card-color);
    padding: 7px 10px;
    margin-bottom: 0;
    border-radius: var(--radius);
    border: 1px solid var(--border-color);
}

.post-title {
    font-size: 22px;
    font-weight: 600;
    margin-bottom: 12px;
    line-height: 1.4;
    color: var(--text-color);
    font-family: var(--font-family);
    letter-spacing: -0.3px;
}

/* 详情页元数据样式 - 与列表页风格一致 - 使用更高优先级 */
.post-header .detail-meta,
.detail-meta {
    margin-bottom: 15px !important;
    font-size: 12px !important;
    color: #999 !important;
}

.post-header .detail-meta .meta-row,
.detail-meta .meta-row {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 12px !important;
    margin-bottom: 8px !important;
}

.post-header .detail-meta .meta-row:last-child,
.detail-meta .meta-row:last-child {
    margin-bottom: 0 !important;
}

.post-header .detail-meta .meta-item,
.detail-meta .meta-item {
    display: flex !important;
    align-items: center !important;
    white-space: nowrap !important;
}

.post-header .detail-meta .meta-item i,
.detail-meta .meta-item i {
    margin-right: 4px !important;
    font-size: 11px !important;
    width: 12px !important;
    text-align: center !important;
}

/* 图标颜色 - 与列表页一致 - 使用更高优先级 */
.post-header .detail-meta .meta-row:first-child .meta-item:first-child i,
.detail-meta .meta-row:first-child .meta-item:first-child i {
    color: #ff6b6b !important; /* 地区图标 */
}

.post-header .detail-meta .meta-row:first-child .meta-item:nth-child(2) i,
.detail-meta .meta-row:first-child .meta-item:nth-child(2) i {
    color: #4ecdc4 !important; /* 有效期图标 */
}

.post-header .detail-meta .meta-row:first-child .meta-item:last-child i,
.detail-meta .meta-row:first-child .meta-item:last-child i {
    color: #45b7d1 !important; /* 时间图标 */
}

.post-header .detail-meta .meta-row:last-child .meta-item:first-child i,
.detail-meta .meta-row:last-child .meta-item:first-child i {
    color: #28a745 !important; /* 浏览图标 */
}

.post-header .detail-meta .meta-row:last-child .meta-item:nth-child(2) i,
.detail-meta .meta-row:last-child .meta-item:nth-child(2) i {
    color: #6c757d !important; /* 编号图标 */
}

.post-header .detail-meta .report-link,
.detail-meta .report-link {
    color: #dc3545 !important;
    text-decoration: none !important;
}

.post-header .detail-meta .report-link:hover,
.detail-meta .report-link:hover {
    color: #c82333 !important;
}

.post-header .detail-meta .report-link i,
.detail-meta .report-link i {
    color: #dc3545 !important;
}

/* 保留旧的post-meta样式以兼容其他地方 */
.post-meta {
    display: flex;
    flex-wrap: wrap;
    font-size: 14px;
    color: var(--text-light);
    margin-bottom: 10px;
    gap: 12px;
    font-family: var(--font-family);
}

.post-price {
    color: var(--accent-color);
    font-weight: bold;
    font-size: 20px;
    margin-bottom: 5px;
    margin-top: 5px;
}

/* 图片展示区 */
.post-gallery {
    background-color: var(--card-color);
    padding: 7px 10px;
    margin-bottom: 0;
    border-radius: var(--radius);
    border: 1px solid var(--border-color);
}

.post-gallery-title {
    font-size: 17px;
    margin-bottom: 16px;
    color: var(--text-color);
    font-weight: 600;
    position: relative;
    padding-left: 12px;
}

.post-gallery-title:before {
    content: '';
    position: absolute;
    left: 0;
    top: 2px;
    bottom: 2px;
    width: 4px;
    background-color: var(--primary-color);
    border-radius: 2px;
}

.gallery-container {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
}

.gallery-item {
    width: calc((100% - 16px) / 3);
    cursor: pointer;
    border-radius: var(--radius);
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.2s ease;
}

.gallery-item:active {
    transform: scale(0.98);
}

.gallery-item img {
    width: 100%;
    height: 100px;
    object-fit: cover;
    display: block;
}

/* 详情内容区 */
.post-content {
    background-color: var(--card-color);
    padding: 7px 10px;
    margin-bottom: 0;
    border-radius: var(--radius);
    border: 1px solid var(--border-color);
}

.post-content-title {
    font-size: 17px;
    margin-bottom: 16px;
    color: var(--text-color);
    font-weight: 600;
    position: relative;
    padding-left: 12px;
}

.post-content-title:before {
    content: '';
    position: absolute;
    left: 0;
    top: 2px;
    bottom: 2px;
    width: 4px;
    background-color: var(--primary-color);
    border-radius: 2px;
}

.post-text {
    line-height: 1.8;
    color: var(--text-color);
    font-size: 16px;
    font-family: var(--font-family);
    letter-spacing: -0.2px;
}

/* 字段信息区 */
.post-fields {
    background-color: var(--card-color);
    padding: 7px 16px;
    margin-bottom: 0;
    border-radius: var(--radius);
    border: 1px solid var(--border-color);
}

.post-fields-title {
    font-size: 16px;
    margin-bottom: 16px;
    color: var(--text-color);
    font-weight: 600;
    position: relative;
    padding-left: 12px;
}

.post-fields-title:before {
    content: '';
    position: absolute;
    left: 0;
    top: 2px;
    bottom: 2px;
    width: 4px;
    background-color: var(--primary-color);
    border-radius: 2px;
}

.field-item {
    display: flex;
    padding: 10px 0;
    border-bottom: 1px solid var(--border-color);
}

.field-label {
    width: 80px;
    color: var(--text-light);
    flex-shrink: 0;
    font-weight: 500;
    font-size: 15px;
}

.field-value {
    flex: 1;
    color: var(--text-color);
    font-size: 15px;
}

/* 联系信息区 */
.contact-box {
    background-color: var(--card-color);
    padding: 7px 10px;
    margin-bottom: 0;
    border-radius: var(--radius);
    border: 1px solid var(--border-color);
}

.contact-title {
    font-size: 16px;
    margin-bottom: 16px;
    color: var(--text-color);
    font-weight: 600;
    position: relative;
    padding-left: 12px;
}

.contact-title:before {
    content: '';
    position: absolute;
    left: 0;
    top: 2px;
    bottom: 2px;
    width: 4px;
    background-color: var(--primary-color);
    border-radius: 2px;
}

.contact-item {
    display: flex;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid var(--border-color);
    position: relative;
}

.contact-label {
    width: 40px;
    color: var(--text-light);
    flex-shrink: 0;
    font-weight: 500;
}

.contact-value {
    flex: 1;
    display: flex;
    align-items: center;
    color: var(--text-color);
    font-weight: 500;
}

.phone-icon {
    position: absolute;
    right: 5px;
    color: var(--secondary-color);
    text-decoration: none;
    font-size: 16px;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: rgba(52, 168, 83, 0.1);
}

/* 新的联系信息样式 - 使用图标风格 */
.contact-info-meta {
    margin-top: 10px;
}

.contact-box .contact-meta-item,
.contact-meta-item {
    display: flex !important;
    align-items: center !important;
    margin-bottom: 12px !important;
    font-family: var(--font-family) !important;
    font-size: 14px !important;
}

.contact-box .contact-meta-item:last-child,
.contact-meta-item:last-child {
    margin-bottom: 0 !important;
}

.contact-box .contact-meta-item i,
.contact-meta-item i {
    width: 16px !important;
    text-align: center !important;
    margin-right: 8px !important;
    font-size: 14px !important;
}

.contact-box .contact-meta-item .fas.fa-user,
.contact-meta-item .fas.fa-user {
    color: #17a2b8 !important; /* 用户图标 */
}

.contact-box .contact-meta-item .fas.fa-phone,
.contact-meta-item .fas.fa-phone {
    color: #28a745 !important; /* 电话图标 */
}

.contact-box .contact-meta-item .fas.fa-map-marker-alt,
.contact-meta-item .fas.fa-map-marker-alt {
    color: #ff6b6b !important; /* 地址图标 */
}

.contact-box .contact-meta-item .fab.fa-weixin,
.contact-meta-item .fab.fa-weixin {
    color: #07c160 !important; /* 微信图标 */
}

.contact-meta-item .contact-label {
    min-width: 30px;
    color: var(--text-light);
    font-weight: 500;
    margin-right: 4px;
}

.contact-meta-item .contact-value {
    flex: 1;
    color: var(--text-color);
    word-break: break-all;
}

.contact-meta-item .phone-number {
    font-size: 16px;
    font-weight: bold;
    color: var(--accent-color);
}

.contact-meta-item .expired-text {
    font-size: 14px;
    color: #999;
}

/* 简化信息布局 - 单行显示带图标 */
.post-info-simple {
    margin: 10px 0;
    font-size: 12px;
    color: #666;
    line-height: 1.4;
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
}

.post-info-simple .info-item {
    display: flex;
    align-items: center;
    white-space: nowrap;
}

.post-info-simple .info-item i {
    margin-right: 4px;
    font-size: 11px;
    width: 12px;
    text-align: center;
}

/* 图标颜色 */
.post-info-simple .info-item:nth-child(1) i {
    color: #6c757d; /* 编号图标 */
}

.post-info-simple .info-item:nth-child(2) i {
    color: #45b7d1; /* 时间图标 */
}

.post-info-simple .info-item:nth-child(3) i {
    color: #4ecdc4; /* 有效期图标 */
}

.post-info-simple .info-item:nth-child(4) i {
    color: #28a745; /* 浏览图标 */
}

/* 交易提醒样式 - 两行左对齐 */
.trade-warning {
    margin: 15px 0;
    padding: 10px 12px;
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    font-size: 12px;
    color: #856404;
    line-height: 1.5;
}

.warning-line {
    margin-bottom: 4px;
}

.warning-line:last-child {
    margin-bottom: 0;
}

/* 海报生成模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    align-items: center;
    justify-content: center;
}

.modal-content {
    background-color: white;
    margin: 15% auto;
    padding: 0;
    border-radius: 12px;
    width: 90%;
    max-width: 400px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    position: relative;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    padding: 20px 20px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    color: #333;
    font-weight: 600;
}

.close {
    color: #aaa;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    line-height: 1;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.2s;
}

.close:hover {
    color: #333;
    background-color: #f5f5f5;
}

.modal-body {
    padding: 20px;
}

/* 海报加载动画 */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 底部操作区 */
.post-actions {
    background-color: var(--card-color);
    padding: 10px 10px;
    margin-bottom: 0;
    display: flex;
    justify-content: space-around;
    border-radius: var(--radius);
    border: 1px solid var(--border-color);
}

.action-btn {
    text-align: center;
    width: 30%;
}

.action-btn a {
    display: block;
    padding: 8px 0;
    background-color: var(--primary-color);
    color: #fff;
    text-decoration: none;
    border-radius: 4px;
    font-size: 13px;
    transition: background-color 0.3s ease;
}

.action-btn a:hover {
    background-color: var(--secondary-color);
}

/* 图片灯箱 */
.lightbox {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    z-index: 1000;
    text-align: center;
}

.lightbox-img {
    max-width: 100%;
    max-height: 90vh;
    margin: auto;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.lightbox-close {
    position: absolute;
    top: 20px;
    right: 20px;
    color: #fff;
    font-size: 30px;
    cursor: pointer;
}

.lightbox-prev, .lightbox-next {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    color: #fff;
    font-size: 30px;
    cursor: pointer;
    background: rgba(0, 0, 0, 0.3);
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    border-radius: 50%;
}

.lightbox-prev {
    left: 20px;
}

.lightbox-next {
    right: 20px;
}

/* 分享样式 */
.share-modal {
    display: none;
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: #fff;
    border-radius: 16px 16px 0 0;
    box-shadow: 0 -5px 25px rgba(0,0,0,0.1);
    z-index: 1010;
    padding: 20px 15px;
    transform: translateY(100%);
    transition: transform 0.3s;
}

.share-modal.active {
    transform: translateY(0);
}

.share-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.share-title {
    font-size: 17px;
    font-weight: 600;
    color: #333;
}

.share-close {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    color: #999;
    font-size: 16px;
}

.share-options {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    padding-bottom: 10px;
}

.share-option {
    width: 25%;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px 0;
}

.share-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
    font-size: 22px;
    color: #fff;
}

.wechat {
    background-color: #07c160;
}

.weibo {
    background-color: #e6162d;
}

.qq {
    background-color: #12b7f5;
}

.link {
    background-color: #ff9500;
}

.share-name {
    font-size: 12px;
    color: #666;
}

.overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    z-index: 1009;
    opacity: 0;
    transition: opacity 0.3s;
}

.overlay.active {
    opacity: 1;
}

/* 拨打电话按钮样式 */
.call-btn {
    display: inline-block !important;
    background-color: var(--secondary-color) !important;
    color: white !important;
    text-align: center !important;
    padding: 8px 40px !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    text-decoration: none !important;
    border-radius: 8px !important;
    margin: 0 auto !important;
    min-width: 240px !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
}

.call-btn:hover {
    background-color: #45a049 !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
}

.call-btn:active {
    transform: translateY(0) !important;
    box-shadow: 0 2px 6px rgba(0,0,0,0.1) !important;
}

/* 拨打电话按钮容器 */
.call-btn-container {
    text-align: center !important;
    margin-top: 20px !important;
}

/* 底部导航特定样式 */
.navbar .phone-btn {
    color: var(--secondary-color);
}

.navbar .sms-btn {
    color: var(--primary-color);
}

/* 举报区域 */
.report-section {
    background-color: #fff;
    padding: 15px 10px;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.report-text {
    flex: 1;
}

.report-warning {
    color: #0066cc;
    font-size: 14px;
    margin-bottom: 5px;
}

.report-note {
    color: #999;
    font-size: 12px;
}

.report-btn {
    background-color: #ff3b30;
    color: #fff;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 12px;
    cursor: pointer;
    text-decoration: none;
    margin-left: 10px;
}

/* 通用标题样式 */
.section-title {
    font-size: 16px;
    margin-bottom: 16px;
    color: var(--text-color);
    font-weight: 600;
    position: relative;
    padding-left: 12px;
}

.section-title:before {
    content: '';
    position: absolute;
    left: 0;
    top: 2px;
    bottom: 2px;
    width: 4px;
    background-color: var(--primary-color);
    border-radius: 2px;
}

/* 骨架屏样式 */
.skeleton-container {
    position: relative;
    overflow: hidden;
}

.skeleton {
    position: relative;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: 4px;
    overflow: hidden;
}

.skeleton-text {
    height: 16px;
    margin-bottom: 8px;
    width: 100%;
    border-radius: 4px;
}

.skeleton-lines {
    display: flex;
    flex-direction: column;
}

.skeleton-lines::before,
.skeleton-lines::after {
    content: "";
    width: 100%;
    height: 16px;
    margin: 8px 0;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: 4px;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* 图片加载效果 */
.lazy-image {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.lazy-image.loaded {
    opacity: 1;
}

/* 内容加载效果 */
body:not(.content-loaded) .skeleton-container {
    opacity: 0.8;
}

body.content-loaded .skeleton-container {
    opacity: 1;
    transition: opacity 0.3s ease;
}

/* 防止闪烁 */
.no-fouc .post-header,
.no-fouc .post-gallery,
.no-fouc .post-content,
.no-fouc .post-fields,
.no-fouc .contact-box {
    visibility: hidden;
}

body.content-loaded .post-header,
body.content-loaded .post-gallery,
body.content-loaded .post-content,
body.content-loaded .post-fields,
body.content-loaded .contact-box {
    visibility: visible;
    transition: opacity 0.3s ease;
}