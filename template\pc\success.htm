<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>操作成功 - {$site_name}</title>
    <link rel="stylesheet" href="/template/pc/css/common.css">
    <style>
        .success-container {
   
            background: #fff;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 40px 0px;
            text-align: center;
      
        }
        
        .success-icon {
            margin-bottom: 30px;
        }
        
        .success-icon i {
            font-size: 80px;
            color: #52c41a;
        }
        
        .success-title {
            font-size: 28px;
            color: #333;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .success-message {
            font-size: 16px;
            color: #666;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        
        .countdown {
            font-size: 14px;
            color: #999;
            margin-bottom: 20px;
        }
        
        .btn-group {
            display: flex;
            justify-content: center;
            gap: 15px;
        }
        
        .btn {
            display: inline-block;
            min-width: 120px;
            padding: 10px 25px;
            font-size: 14px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
        }
        
        .btn-primary {
            background: #1890ff;
            color: #fff;
            border: 1px solid #1890ff;
        }
        
        .btn-primary:hover {
            background: #40a9ff;
            border-color: #40a9ff;
        }
        
        .btn-default {
            background: #fff;
            color: #666;
            border: 1px solid #d9d9d9;
        }
        
        .btn-default:hover {
            color: #40a9ff;
            border-color: #40a9ff;
        }
    </style>
</head>
<body>
    {include file="header.htm"}
    
    <div class="success-container yui-1200">
        <div class="success-icon">
            <i class="fas fa-check-circle"></i>
        </div>
        <h1 class="success-title">操作成功</h1>
        <div class="success-message">{$success_message}</div>
        
        {if isset($redirect_url) && !empty($redirect_url)}
        <div class="countdown">页面将在 <span id="second">3</span> 秒后自动跳转</div>
        <div class="btn-group">
            <a href="{$redirect_url}" class="btn btn-primary" id="redirect-btn">立即跳转</a>
            <a href="javascript:history.back();" class="btn btn-default">返回上一页</a>
        </div>
        {else}
        <div class="btn-group">
            <a href="/" class="btn btn-primary">返回首页</a>
            <a href="javascript:history.back();" class="btn btn-default">返回上一页</a>
        </div>
        {/if}
    </div>

    {include file="footer.htm"}

    {if isset($redirect_url) && !empty($redirect_url)}
    <script>
        var second = 113;
        var timer = null;
        
        function countDown() {
            second--;
            if (second <= 0) {
                clearInterval(timer);
                window.location.href = '{$redirect_url}';
                return;
            }
            document.getElementById('second').innerText = second;
        }
        
        timer = setInterval(countDown, 1000);
    </script>
    {/if}
</body>
</html> 