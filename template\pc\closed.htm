<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网站维护中 - {$site_name}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }
        .maintenance-container {
            max-width: 800px;
            width: 100%;
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.08);
            overflow: hidden;
            position: relative;
        }
        .maintenance-header {
            background: linear-gradient(135deg, #3498db, #2c3e50);
            padding: 30px;
            text-align: center;
            color: #fff;
        }
        .maintenance-header h1 {
            font-size: 28px;
            margin-bottom: 10px;
            font-weight: 600;
        }
        .maintenance-body {
            padding: 40px;
            text-align: center;
        }
        .maintenance-icon {
            font-size: 80px;
            margin-bottom: 30px;
            color: #3498db;
        }
        .maintenance-message {
            font-size: 18px;
            color: #555;
            margin-bottom: 30px;
            line-height: 1.7;
        }
        .maintenance-footer {
            border-top: 1px solid #eee;
            padding: 20px;
            text-align: center;
            background: #f9f9f9;
        }
        .maintenance-footer p {
            color: #888;
            font-size: 14px;
        }
        .back-button {
            display: inline-block;
            padding: 12px 24px;
            background-color: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 30px;
            margin-top: 20px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }
        .back-button:hover {
            background-color: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }
        .logo {
            max-height: 60px;
            margin-bottom: 20px;
        }
        @media (max-width: 600px) {
            .maintenance-header {
                padding: 20px;
            }
            .maintenance-body {
                padding: 30px 20px;
            }
            .maintenance-header h1 {
                font-size: 24px;
            }
            .maintenance-message {
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="maintenance-container">
        <div class="maintenance-header">
            <img src="/template/pc/images/logo.png" alt="{$site_name}" class="logo">
            <h1>网站维护中</h1>
        </div>
        
        <div class="maintenance-body">
            <div class="maintenance-icon">🛠️</div>
            <div class="maintenance-message">
                {$close_reason}
            </div>
            <a href="javascript:history.back();" class="back-button">返回上一页</a>
        </div>
        
        <div class="maintenance-footer">
            <p>感谢您的理解与支持！我们将尽快恢复服务。</p>
        </div>
    </div>
</body>
</html> 