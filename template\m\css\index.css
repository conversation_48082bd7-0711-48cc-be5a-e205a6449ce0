.header-inner {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 5px;
    height: 56px;
}
.menu-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    width: 40px;
    flex-shrink: 0;
    cursor: pointer;
}

.menu-icon i {
    font-size: 20px;
}
.search-box {
    flex: 1;
    height: 36px;
    background: rgba(255,255,255,0.15);
    border-radius: 18px;
    display: flex;
    align-items: center;
    padding: 0 15px;
    margin: 0 10px;
    color: #fff;
    font-size: 15px;
    text-decoration: none;
    cursor: pointer;
    max-width: calc(100% - 100px);
}
.search-box i {
    margin-right: 10px;
    opacity: 0.9;
    font-size: 18px;
}
.search-placeholder {
    opacity: 0.9;
}
.header-actions {
    display: flex;
    align-items: center;
    min-width: 40px;
    justify-content: flex-end;
}
.add-btn {
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    width: 40px;
    height: 40px;
    text-align: center;
    flex-shrink: 0;
    cursor: pointer;
}

.add-btn i {
    font-size: 20px;
}
.banner {
    background-color: #fff;
    padding: 15px 0;
    margin-bottom: 0;
    text-align: center;
    border-bottom: 1px solid #f0f0f0;
}
.banner-title {
    font-size: 18px;
    margin-bottom: 8px;
    color: #333;
}
.banner-subtitle {
    font-size: 14px;
    color: #666;
    margin-bottom: 10px;
}
.banner-btn {
    display: inline-block;
    padding: 8px 20px;
    background-color: var(--primary-color);
    color: #fff;
    text-decoration: none;
    border-radius: 4px;
    font-weight: bold;
    font-size: 14px;
    margin-bottom: 5px;
}
.category-section {
    background-color: #fff;

    border-radius: 0;
    overflow: hidden;
    padding-bottom: 5px;
    box-shadow: none;
    border-bottom: 1px solid #f0f0f0;
}
.category-title {
    font-size: 16px;
    padding: 12px 10px;
    color: #333;
    margin: 0;
    font-weight: 600;
    border-bottom: 1px solid #f5f5f5;
}
.category-title i {
    color: var(--primary-color);
    margin-right: 5px;
}
.category-scroll {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
    padding: 20px 0;
    margin: 0;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}
.category-scroll::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}
.category-grid-container {
    display: flex;
    padding: 0 10px;
}
.category-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    grid-template-rows: repeat(2, auto);
    gap: 12px 0;
    min-width: 100%;
}
.category-item {
    text-align: center;
    text-decoration: none;
    display: flex;
    flex-direction: column;
    align-items: center;
}
.category-icon {
    width: 50px;
    height: 50px;
    margin: 0 auto 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    background-color: #f8f9fa;
    overflow: hidden;
    box-shadow: 0 2px 6px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
}
.category-icon i {
    font-size: 22px;
    color: #4a89dc;
}
/* 各种图标颜色 */
.category-icon.house { background-color: #e3f2fd; }
.category-icon.house i { color: #2196f3; }

.category-icon.job { background-color: #e8f5e9; }
.category-icon.job i { color: #4caf50; }

.category-icon.car { background-color: #fff3e0; }
.category-icon.car i { color: #ff9800; }

.category-icon.edu { background-color: #f3e5f5; }
.category-icon.edu i { color: #9c27b0; }

.category-icon.service { background-color: #e0f7fa; }
.category-icon.service i { color: #00bcd4; }

.category-icon.pet { background-color: #fce4ec; }
.category-icon.pet i { color: #e91e63; }

.category-icon.phone { background-color: #e8eaf6; }
.category-icon.phone i { color: #3f51b5; }

.category-icon.furniture { background-color: #fff8e1; }
.category-icon.furniture i { color: #ffc107; }

.category-icon.repair { background-color: #efebe9; }
.category-icon.repair i { color: #795548; }

.category-icon.food { background-color: #e0f2f1; }
.category-icon.food i { color: #009688; }

.category-icon.more { background-color: #eceff1; }
.category-icon.more i { color: #607d8b; }

.category-item:hover .category-icon {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
.category-name {
    font-size: 14px;
    color: #333;
    text-decoration: none;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    transition: color 0.3s ease;
    font-family: var(--font-family);
}
.category-item:hover .category-name {
    color: var(--primary-color);
}

/* 首页搜索层样式 */
.search-layer {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: var(--primary-color);
    z-index: 101;
    display: none;
    opacity: 0;
    transform: translateY(-10px);
    transition: opacity 0.25s ease, transform 0.25s ease;
}

.search-layer.active {
    display: block;
    opacity: 1;
    transform: translateY(0);
}

.search-header {
    display: flex;
    align-items: center;
    height: 50px;
    padding: 0 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.search-back {
    color: #fff;
    padding: 5px;
    margin-right: 10px;
    font-size: 16px;
    display: flex;
    align-items: center;
    text-decoration: none;
}

.search-form {
    display: flex;
    align-items: center;
    flex: 1;
    background-color: rgba(255, 255, 255, 0.15);
    border-radius: 18px;
    margin-right: 10px;
    padding: 0 15px;
    height: 36px;
}

.search-icon {
    color: rgba(255, 255, 255, 0.8);
    font-size: 18px;
    margin-right: 10px;
}

.search-input {
    flex: 1;
    background: none;
    border: none;
    color: #fff;
    padding: 0;
    font-size: 15px;
    outline: none;
    height: 36px;
}

.search-input::placeholder {
    color: rgba(255, 255, 255, 0.8);
}

.search-cancel {
    color: #fff;
    padding: 8px 5px;
    font-size: 15px;
    background: none;
    border: none;
    cursor: pointer;
    white-space: nowrap;
}

.search-content {
    background-color: #fff;
    min-height: 100vh;
    padding: 10px 15px;
}

.search-history {
    margin-bottom: 20px;
}

.search-section-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    color: #999;
    font-size: 14px;
}

.search-clear {
    color: #999;
    font-size: 14px;
    background: none;
    border: none;
    padding: 5px;
    cursor: pointer;
}

.search-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.search-tag {
    background-color: #f5f5f5;
    color: #666;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 13px;
    text-decoration: none;
}

/* Tab container styles */
.tab-container {
    background-color: #fff;
    margin-bottom: 10px;
    border-radius: 3px;
}

.tab-header {
    display: flex;
    border-bottom: 1px solid #f0f0f0;
    justify-content: space-between;
    align-items: center;
    padding: 0 15px;
}

.tab-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    padding: 12px 0;
}

.view-switch {
    display: flex;
    align-items: center;
}

.view-btn {
    width: 32px;
    height: 32px;
    background: none;
    border: 1px solid #eee;
    border-radius: 3px;
    margin-left: 5px;
    cursor: pointer;
    font-size: 14px;
    color: #999;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.view-btn:first-child {
    margin-left: 0;
}

.view-btn.active {
    background-color: var(--primary-color);
    color: #fff;
    border-color: var(--primary-color);
}

.view-btn:hover:not(.active) {
    border-color: #ddd;
    color: #666;
}

.tab-item {
    flex: 0 0 auto;
    text-align: center;
    padding: 12px 20px;
    font-size: 15px;
    cursor: pointer;
    position: relative;
    color: #666;
}

.tab-item.active {
    color: var(--primary-color);
    font-weight: bold;
}

.tab-item.active:after {
    content: '';
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: 0;
    width: 40%;
    height: 3px;
    background-color: var(--primary-color);
    border-radius: 1.5px;
}

.tab-content {
    display: none;
    padding: 0;
}

.tab-content.active {
    display: block;
}

/* List items styles */
.post-list {
    padding: 0;
}

.post-item {
    display: flex;
    padding: 12px 15px; /* 调整内边距，与分类页保持一致 */
    border-bottom: 1px solid #f5f5f5;
    transition: background-color 0.1s ease;
    -webkit-tap-highlight-color: rgba(0,0,0,0);
    cursor: pointer;
}

.post-item:active {
    background-color: rgba(0,0,0,0.05);
}

.post-item:last-child {
    border-bottom: none;
}

.post-image {
    width: 90px; /* 调整图片宽度，与分类页保持一致 */
    height: 70px; /* 调整图片高度，与分类页保持一致 */
    margin-right: 12px;
    flex-shrink: 0;
    overflow: hidden;
    border-radius: 4px; /* 稍微增加圆角 */
    background-color: #f5f5f5; /* 调整背景色，与分类页保持一致 */
}

.post-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: opacity 0.2s;
}

.post-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 75px;
    justify-content: space-between;
    overflow: hidden;
}

.post-title {
    font-size: 15px; /* 调整标题字体大小，与分类页保持一致 */
    font-weight: 500; /* 增加字体粗细 */
    margin-bottom: 6px; /* 增加标题与元数据的间距 */
    color: #333;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    line-height: 1.4;
    text-decoration: none;
    font-family: var(--font-family);
    letter-spacing: 0; /* 移除负字间距，提升可读性 */
}

.top-tag {
    display: inline-block;
    padding: 2px 4px;
    background-color: #ff4444; /* 红色徽章 */
    color: #fff;
    font-size: 10px;
    line-height: 1;
    border-radius: 3px;
    margin-right: 6px;
    vertical-align: middle;
    font-weight: bold;
}

.post-summary {
    font-size: 14px;
    color: #999;
    margin-bottom: 5px;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.post-meta {
    display: flex;
    font-size: 12px; /* 调整元数据字体大小 */
    color: #666; /* 调整元数据颜色，提升可读性 */
    margin-top: auto;
}

.post-meta div {
    margin-right: 12px; /* 增加元数据项之间的间距 */
}

/* Simple list styles */
.simple-list {
    padding: 0;
}

/* 新的列表项样式 - 使用更高优先级 */
.simple-list .list-item,
.list-item {
    padding: 15px !important;
    border-bottom: 1px solid #f5f5f5 !important;
    transition: background-color 0.1s ease !important;
    -webkit-tap-highlight-color: rgba(0,0,0,0) !important;
    cursor: pointer !important;
    display: block !important; /* 覆盖flex布局 */
}

.list-item:active {
    background-color: rgba(0,0,0,0.05);
}

.list-item:last-child {
    border-bottom: none;
}

.item-title {
    margin: 0;
}

.item-title a {
    color: #333;
    text-decoration: none;
    font-size: 15px;
    font-weight: 500;
    line-height: 1.4;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
}

.item-title a.expired {
    color: #999;
    text-decoration: line-through;
}

.top-tag {
    background: #ff4444; /* 红色徽章 */
    color: white;
    font-size: 10px;
    padding: 2px 4px;
    border-radius: 3px;
    margin-left: 4px; /* 减少间距，紧挨着标题 */
    font-weight: bold;
    display: inline;
    vertical-align: middle;
    line-height: 1;
}

/* 隐藏原来的meta信息 */
.item-meta {
    display: none;
}

.meta-item {
    display: flex;
    align-items: center;
    white-space: nowrap;
}

.meta-item i {
    margin-right: 4px;
    font-size: 11px;
    width: 12px;
    text-align: center;
}

.meta-item:first-child i {
    color: #ff6b6b !important;
}

.meta-item:nth-child(2) i {
    color: #4ecdc4 !important;
}

.meta-item:last-child i {
    color: #45b7d1 !important;
}

/* 置顶帖子样式 */
.list-item.is-top {
    background-color: #ffffff !important; /* 背景与其他一样 */
    border-left: none !important; /* 左侧不要边框 */
}

/* 置顶标题红色 */
.list-item.is-top .item-title a {
    color: #ff4444 !important;
    font-weight: 600 !important;
}

/* 兼容旧的simple-item样式 */
.simple-item {
    display: flex;
    justify-content: space-between;
    padding: 15px;
    border-bottom: 1px solid #f5f5f5;
    transition: background-color 0.1s ease;
    -webkit-tap-highlight-color: rgba(0,0,0,0);
    cursor: pointer;
}

.simple-item:active {
    background-color: rgba(0,0,0,0.05);
}

.simple-item:last-child {
    border-bottom: none;
}

.simple-title {
    flex: 1;
    font-size: 17px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.simple-title a {
    color: #333;
    text-decoration: none;
}

.simple-time {
    font-size: 13px;
    color: #999;
    margin-left: 10px;
}

.simple-item.is-top {
    background-color: #ffffff; /* 背景与其他一样 */
    border-left: none; /* 左侧不要边框 */
}

.simple-item.is-top .simple-title a {
    color: #ff4444; /* 标题红色 */
    font-weight: 600;
}

/* 添加点击效果 */
.post-item {
    transition: background-color 0.1s ease;
    -webkit-tap-highlight-color: rgba(0,0,0,0);
    cursor: pointer;
}

.post-item:active {
    background-color: rgba(0,0,0,0.05);
}

.simple-item {
    transition: background-color 0.1s ease;
    -webkit-tap-highlight-color: rgba(0,0,0,0);
    cursor: pointer;
}

.simple-item:active {
    background-color: rgba(0,0,0,0.05);
}

/* 新的行布局样式 */
.item-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
}

.item-left {
    flex: 1;
    min-width: 0; /* 允许内容收缩 */
}

.item-right {
    flex-shrink: 0;
}


.item-time {
    color: #999;
    font-size: 12px;
    white-space: nowrap;
}

/* 隐藏切换按钮 */
.view-switch {
    display: none;
}

/* 添加粘性头部样式 */
.sticky-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: var(--primary-color) !important;
    z-index: 100;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    transform: translateY(-100%);
    transition: transform 0.2s ease-out, opacity 0.2s ease-out;
    opacity: 0;
    will-change: transform, opacity;
}

/* 简约主题粘性头部特殊处理 */
.theme-simple .sticky-header {
    background-color: var(--header-bg-color, #ffffff) !important;
    box-shadow: var(--shadow) !important;
}

.theme-simple .sticky-header .menu-icon,
.theme-simple .sticky-header .search-box,
.theme-simple .sticky-header .add-btn {
    color: var(--header-text-color, #333333);
}

.theme-simple .sticky-header .search-box {
    background: rgba(0,0,0,0.05);
}

.sticky-header.visible {
    transform: translateY(0);
    opacity: 1;
}

.sticky-inner {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 5px;
    height: 50px; /* 紧凑一些的高度 */
}

.sticky-header .search-box {
    max-width: calc(100% - 100px);
}

/* 保持主头部样式一致 */
.main-header {
    background-color: var(--primary-color) !important;
    color: #fff;
    position: relative;
    z-index: 10;
}

/* 简约主题首页头部特殊处理 */
.theme-simple .main-header {
    background-color: var(--header-bg-color, #ffffff) !important;
}

.theme-simple .main-header .menu-icon,
.theme-simple .main-header .search-box,
.theme-simple .main-header .add-btn {
    color: var(--header-text-color, #333333);
}

.theme-simple .main-header .search-box {
    background: rgba(0,0,0,0.05);
}