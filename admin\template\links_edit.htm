{include file="header.htm"}

<style>
/* 页面标题样式 */
.page-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid #ddd;
}

.page-title h1 {
    margin: 0;
    font-size: 24px;
    color: #333;
}

.page-title .breadcrumb {
    background: none;
    padding: 0;
    margin: 0;
    font-size: 14px;
}

/* 表单样式 */
.form-container {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
}

.form-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    margin: 0;
    font-size: 18px;
    font-weight: 500;
}

.form-body {
    padding: 30px;
}

.form-group {
    margin-bottom: 25px;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.form-label .required {
    color: #e74c3c;
    margin-left: 3px;
}

.form-field {
    position: relative;
}

.form-control {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.3s ease;
    background-color: #fff;
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-control.error {
    border-color: #e74c3c;
}

.form-select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 40px;
}

.form-textarea {
    min-height: 100px;
    resize: vertical;
}

.form-help {
    font-size: 12px;
    color: #6c757d;
    margin-top: 5px;
}

.form-error {
    color: #e74c3c;
    font-size: 12px;
    margin-top: 5px;
}

/* 按钮样式 */
.form-actions {
    display: flex;
    gap: 12px;
    padding-top: 20px;
    border-top: 1px solid #e1e5e9;
    margin-top: 30px;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-outline {
    background: white;
    color: #6c757d;
    border: 2px solid #e1e5e9;
}

.btn-outline:hover {
    background: #f8f9fa;
    border-color: #d1d5db;
}

/* 帮助面板样式 */
.help-panel {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
    position: sticky;
    top: 20px;
}

.help-panel h4 {
    color: #495057;
    font-size: 16px;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid #dee2e6;
}

.help-panel h4 i {
    color: #007bff;
    margin-right: 8px;
}

.help-item {
    margin-bottom: 15px;
    padding-bottom: 12px;
    border-bottom: 1px solid #f1f3f4;
}

.help-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.help-item strong {
    display: block;
    color: #495057;
    font-size: 14px;
    margin-bottom: 4px;
}

.help-item p {
    color: #6c757d;
    font-size: 13px;
    line-height: 1.4;
    margin: 0;
}

/* 表单组样式优化 */
.form-group {
    margin-bottom: 20px;
}

.form-group:last-child {
    margin-bottom: 0;
}

.form-label {
    font-weight: 500;
    margin-bottom: 6px;
    color: #495057;
}

.form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
    padding: 8px 12px;
    font-size: 14px;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-textarea {
    resize: vertical;
    min-height: 80px;
}

/* 响应式布局 */
@media (max-width: 768px) {
    .form-body {
        padding: 20px;
    }

    .help-panel {
        margin-top: 30px;
        position: static;
    }

    .form-actions {
        flex-direction: column;
        margin-top: 30px;
    }

    .btn {
        justify-content: center;
        margin-bottom: 10px;
    }

    .btn:last-child {
        margin-bottom: 0;
    }
}


</style>

<div class="page-title">
    <div>
        <h1>{if isset($link_data.id)}编辑友情链接{else}添加友情链接{/if}</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="index.php">控制面板</a></li>
                <li class="breadcrumb-item"><a href="links.php">友情链接管理</a></li>
                <li class="breadcrumb-item active">{if isset($link_data.id)}编辑友情链接{else}添加友情链接{/if}</li>
            </ol>
        </nav>
    </div>
</div>

{if isset($error)}
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-circle"></i>
    {$error}
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
{/if}

<div class="section">
    <div class="form-container">
        <div class="form-header">
            <i class="fas fa-link"></i>
            友情链接信息
        </div>
        
        <div class="form-body">
            <div class="row">
                <!-- 左侧表单区域 -->
                <div class="col-md-8">
                    <form method="POST" action="" id="link-form">
                        {if isset($link_data.id)}
                        <input type="hidden" name="id" value="{$link_data.id}">
                        {/if}

                        <div class="form-group">
                            <label class="form-label">
                                链接名称 <span class="required">*</span>
                            </label>
                            <input type="text" name="name" class="form-control" value="{$link_data.name}" placeholder="请输入链接名称" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                链接地址 <span class="required">*</span>
                            </label>
                            <input type="url" name="url" class="form-control" value="{$link_data.url}" placeholder="https://www.example.com" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">链接描述</label>
                            <textarea name="description" class="form-control form-textarea" rows="3" placeholder="请输入链接描述（可选）">{$link_data.description}</textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">排序</label>
                                    <input type="number" name="sort_order" class="form-control" value="{$link_data.sort_order}" placeholder="0" min="0">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">打开方式</label>
                                    <select name="target" class="form-control form-select">
                                        <option value="_blank" {if $link_data.target == '_blank'}selected{/if}>新窗口</option>
                                        <option value="_self" {if $link_data.target == '_self'}selected{/if}>当前窗口</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">状态</label>
                                    <select name="status" class="form-control form-select">
                                        <option value="1" {if $link_data.status == 1}selected{/if}>启用</option>
                                        <option value="0" {if $link_data.status == 0}selected{/if}>禁用</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                </div>

                <!-- 右侧帮助信息区域 -->
                <div class="col-md-4">
                    <div class="help-panel">
                        <h4><i class="fas fa-info-circle"></i> 填写说明</h4>
                        <div class="help-item">
                            <strong>链接名称</strong>
                            <p>显示在网站上的链接文字，建议简洁明了</p>
                        </div>
                        <div class="help-item">
                            <strong>链接地址</strong>
                            <p>请输入完整的网址，包含 http:// 或 https://</p>
                        </div>
                        <div class="help-item">
                            <strong>链接描述</strong>
                            <p>简短描述这个网站的内容或特色，可选填写</p>
                        </div>
                        <div class="help-item">
                            <strong>排序</strong>
                            <p>数字越小排序越靠前，默认为0</p>
                        </div>
                        <div class="help-item">
                            <strong>打开方式</strong>
                            <p>建议选择"新窗口"，避免用户离开本站</p>
                        </div>
                        <div class="help-item">
                            <strong>状态</strong>
                            <p>只有启用状态的链接才会在前台显示</p>
                        </div>
                    </div>
                </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> 保存友情链接
                            </button>
                            <a href="links.php" class="btn btn-outline">
                                <i class="fas fa-arrow-left"></i> 返回列表
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {

    
    // 表单验证
    $('#link-form').on('submit', function(e) {
        var name = $('input[name="name"]').val().trim();
        var url = $('input[name="url"]').val().trim();
        
        if (!name) {
            alert('请输入链接名称');
            $('input[name="name"]').focus();
            e.preventDefault();
            return false;
        }
        
        if (!url) {
            alert('请输入链接地址');
            $('input[name="url"]').focus();
            e.preventDefault();
            return false;
        }
        
        if (!isValidUrl(url)) {
            alert('请输入有效的链接地址');
            $('input[name="url"]').focus();
            e.preventDefault();
            return false;
        }
    });
});

// 验证URL格式
function isValidUrl(string) {
    try {
        new URL(string);
        return true;
    } catch (_) {
        return false;
    }
}
</script>

{include file="footer.htm"}
