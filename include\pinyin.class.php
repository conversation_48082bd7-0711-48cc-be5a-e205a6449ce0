<?php
/**
 * 拼音转换类
 * 用于将中文文本转换为拼音
 */
class Pinyin {
    // 声母表
    private $firstkey = array(
        'a'=>-20319, 'b'=>-20283, 'c'=>-19775, 'd'=>-19218, 'e'=>-18710, 'f'=>-18526, 'g'=>-18239, 
        'h'=>-17922, 'i'=>-17417, 'j'=>-16474, 'k'=>-16212, 'l'=>-15640, 'm'=>-15165, 'n'=>-14922, 
        'o'=>-14914, 'p'=>-14630, 'q'=>-14149, 'r'=>-14090, 's'=>-13318, 't'=>-12838, 'u'=>-12556, 
        'v'=>-12149, 'w'=>-11459, 'x'=>-11356, 'y'=>-11201, 'z'=>-10640
    );
    
    // 常用汉字直接映射表
    private $directPinyin = array(
        '手' => 'shou',
        '机' => 'ji',
        '数' => 'shu',
        '码' => 'ma',
        '电' => 'dian',
        '脑' => 'nao',
        '办' => 'ban',
        '公' => 'gong',
        '用' => 'yong',
        '品' => 'pin',
        '家' => 'jia',
        '居' => 'ju',
        '日' => 'ri',
        '用' => 'yong',
        '汽' => 'qi',
        '车' => 'che',
        '配' => 'pei',
        '件' => 'jian',
        '食' => 'shi',
        '品' => 'pin',
        '饮' => 'yin',
        '料' => 'liao',
        '服' => 'fu',
        '装' => 'zhuang',
        '美' => 'mei',
        '妆' => 'zhuang',
        '母' => 'mu',
        '婴' => 'ying',
        '玩' => 'wan',
        '具' => 'ju',
        '运' => 'yun',
        '动' => 'dong',
        '户' => 'hu',
        '外' => 'wai',
        '图' => 'tu',
        '书' => 'shu',
        '音' => 'yin',
        '像' => 'xiang',
        '健' => 'jian',
        '康' => 'kang'
    );
    
    /**
     * 将中文转换为拼音
     * @param string $string 要转换的中文字符串
     * @param string $delimiter 拼音之间的分隔符
     * @return string 转换后的拼音
     */
    public function convert($string, $delimiter = '') {
        // 将字符串转换为UTF-8编码
        $string = iconv('UTF-8', 'UTF-8//IGNORE', $string);
        $output = '';
        $len = mb_strlen($string, 'UTF-8');
        
        // 记录日志
        error_log("开始转换拼音，输入: " . $string . ", 长度: " . $len);
        
        // 逐字转换
        for ($i = 0; $i < $len; $i++) {
            $char = mb_substr($string, $i, 1, 'UTF-8');
            
            // 检查是否为ASCII字符（非中文）
            if (ord($char) < 128) {
                $output .= $char;
                continue;
            }
            
            // 先查找直接映射表
            if (isset($this->directPinyin[$char])) {
                $output .= $this->directPinyin[$char];
                error_log("字符: $char 使用直接映射: " . $this->directPinyin[$char]);
            } else {
                // 回退到复杂算法
                $pinyin = $this->char_to_pinyin($char);
                $output .= $pinyin;
                error_log("字符: $char 使用复杂算法: $pinyin");
            }
        }
        
        error_log("拼音转换完成: " . $output);
        return $output;
    }
    
    /**
     * 将单个字符转换为拼音
     * @param string $char 要转换的单个字符
     * @return string 转换后的拼音
     */
    private function char_to_pinyin($char) {
        // 如果是ASCII字符，直接返回
        if (ord($char) < 128) {
            return $char;
        }
        
        // 获取字符的Unicode码点
        $code = $this->utf8_to_unicode($char);
        
        // 匹配拼音（使用更简单的首字母匹配）
        foreach ($this->firstkey as $letter => $value) {
            if ($code >= $value) {
                return $letter;
            }
        }
        
        // 无法匹配，返回原字符
        return $char;
    }
    
    /**
     * 将UTF-8字符转换为Unicode编码
     * @param string $char UTF-8字符
     * @return int Unicode编码值
     */
    private function utf8_to_unicode($char) {
        $utf8_bytes = strlen($char);
        $unicode = 0;
        
        if ($utf8_bytes == 1) {
            $unicode = ord($char);
        } elseif ($utf8_bytes == 2) {
            $unicode = ((ord($char[0]) & 0x1F) << 6) + (ord($char[1]) & 0x3F);
        } elseif ($utf8_bytes == 3) {
            $unicode = ((ord($char[0]) & 0x0F) << 12) + ((ord($char[1]) & 0x3F) << 6) + (ord($char[2]) & 0x3F);
        } elseif ($utf8_bytes == 4) {
            $unicode = ((ord($char[0]) & 0x07) << 18) + ((ord($char[1]) & 0x3F) << 12) + ((ord($char[2]) & 0x3F) << 6) + (ord($char[3]) & 0x3F);
        }
        
        return $unicode;
    }
    
    /**
     * 获取中文首字母
     * @param string $string 要获取首字母的中文字符串
     * @return string 中文首字母
     */
    public function get_first_letter($string) {
        $string = iconv('UTF-8', 'UTF-8//IGNORE', $string);
        $first_char = mb_substr($string, 0, 1, 'UTF-8');
        
        // 如果是ASCII字符，直接返回
        if (ord($first_char) < 128) {
            return strtoupper($first_char);
        }
        
        // 先查找直接映射表
        if (isset($this->directPinyin[$first_char])) {
            return strtoupper(substr($this->directPinyin[$first_char], 0, 1));
        }
        
        // 获取字符的UTF-8编码值
        $utf8_code = $this->utf8_to_unicode($first_char);
        
        // 在首字母表中查找对应的字母
        foreach ($this->firstkey as $key => $value) {
            if ($utf8_code >= $value) {
                return strtoupper($key);
            }
        }
        
        // 如果没有找到对应的首字母，返回#
        return '#';
    }
} 