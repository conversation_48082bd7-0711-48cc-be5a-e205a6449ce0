<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>泊头生活网 - 发布信息</title>
    <meta name="keywords" content="泊头生活网,泊头信息网,泊头信息港,泊头生活信息网站" />
    <meta name="description" content="泊头生活网(泊头信息网)，河北泊头生活信息网站。" />
    <link rel="stylesheet" href="/template/pc/css/common.css">
    <link rel="stylesheet" href="/template/pc/css/post.css">
    <link rel="stylesheet" href="/static/font-awesome/css/all.min.css">
    <script type="text/javascript" src="/template/pc/js/m.js"></script>
    <script src="/template/pc/js/jquery.min.js"></script>

</head>
<body>
    <!-- 顶部 -->
    {include file="header.htm"}

    <!-- 主内容区域 -->
    <div class="yui-content yui-1200">
        <div class="content-wrap">
            <!-- 左侧表单区域 -->
            <div class="left-column">
                <div class="post-title">
                    <h3>发布信息</h3>
                    <div class="current-category">
                        <span class="category-name">{$category.name}</span>
                        <a href="post.php" class="reselect-link">[重选栏目]</a>
                    </div>
                </div>
                
                <form id="post-form" action="/post.php{if isset($category) && !empty($category)}?category_id={$category.id}{/if}" method="post" enctype="multipart/form-data" novalidate>
                    {if isset($category) && !empty($category)}
                    <input type="hidden" name="category_id" value="{$category.id}">
                    {/if}

                    {if !empty($error_message)}
                    <div class="error-message">
                        <i class="fas fa-exclamation-circle"></i>{$error_message}
                    </div>
                    {/if}

                    <div class="form-panel">
                        <!-- 选择地区 -->
                        <div class="form-group required">
                            <label for="region_id" class="form-label">所在地区</label>
                            <div class="form-right">
                                <select name="region_id" id="region_id" class="form-select" required>
                                    <option value="">请选择地区</option>
                                    {loop $regions $province}
                                    <optgroup label="{$province.name}">
                                        {loop $province.children $city}
                                        <option value="{$city.id}" {if isset($post.region_id) && $post.region_id == $city.id}selected{/if}>{$city.name}</option>
                                        {/loop}
                                    </optgroup>
                                    {/loop}
                                </select>
                                <div class="field-error" id="region_id_error"><i class="fas fa-exclamation-circle"></i>请选择所在地区</div>
                                <div class="field-success"><i class="fas fa-check-circle"></i></div>
                                <div class="field-focus"><i class="fas fa-info-circle"></i></div>
                                <div class="field-focus-tooltip">请选择您所在的地区</div>
                            </div>
                        </div>
                        
                        <!-- 信息有效期 -->
                        <div class="form-group required">
                            <label for="expire_days" class="form-label">有效期</label>
                            <div class="form-right">
                                <select name="expire_days" id="expire_days" class="form-select" required>
                                    <option value="7">7天</option>
                                    <option value="15">15天</option>
                                    <option value="30" selected>30天</option>
                                    <option value="60">60天</option>
                                    <option value="90">90天</option>
                                </select>
                                <div class="field-error" id="expire_days_error"><i class="fas fa-exclamation-circle"></i>请选择有效期</div>
                                <div class="field-success"><i class="fas fa-check-circle"></i></div>
                                <div class="field-focus"><i class="fas fa-info-circle"></i></div>
                                <div class="field-focus-tooltip">请选择信息的有效期限</div>
                            </div>
                        </div>
                        
                        <!-- 标题 -->
                        <div class="form-group required">
                            <label for="title" class="form-label">标题</label>
                            <div class="form-right">
                                <input type="text" name="title" id="title" class="form-input" placeholder="请输入标题" value="{if isset($post.title)}{$post.title}{/if}" required>
                                <div class="field-error" id="title_error"><i class="fas fa-exclamation-circle"></i>请输入标题</div>
                                <div class="field-success"><i class="fas fa-check-circle"></i></div>
                                <div class="field-focus"><i class="fas fa-info-circle"></i></div>
                                <div class="field-focus-tooltip">请输入简洁明了的标题</div>
                            </div>
                        </div>
                        
                        <!-- 内容 -->
                        <div class="form-group required">
                            <label for="content" class="form-label">详细内容</label>
                            <div class="form-right">
                                <textarea name="content" id="content" class="form-textarea" placeholder="请输入详细内容" required>{if isset($post.content)}{$post.content}{/if}</textarea>
                                <div class="field-error" id="content_error"><i class="fas fa-exclamation-circle"></i>请输入详细内容</div>
                                <div class="field-success"><i class="fas fa-check-circle"></i></div>
                                <div class="field-focus"><i class="fas fa-info-circle"></i></div>
                                <div class="field-focus-tooltip">请详细描述信息内容，越详细越好</div>
                            </div>
                        </div>

                        <!-- 图片上传 -->
                        <div class="form-group">
                            <label class="form-label">上传图片</label>
                            <div class="form-right">
                                <div class="image-upload">
                                    <div class="upload-area">
                                        <input type="file" id="image_uploads" name="images[]" accept="image/*" multiple class="hidden-input">
                                        <label for="image_uploads" class="upload-btn">
                                            <i class="fas fa-plus"></i>
                                            <span>添加图片</span>
                                        </label>
                                        <div class="image-hint">最多可上传9张（选填）<br><span class="warning">请上传非侵权图片和字体图，否则后果自负</span></div>
                                    </div>
                                    <div class="image-previews" id="image-previews"></div>
                                </div>
                            </div>
                        </div>

                        <!-- 联系人 -->
                        <div class="form-group required">
                            <label for="contact_name" class="form-label">联系人</label>
                            <div class="form-right">
                                <input type="text" name="contact_name" id="contact_name" class="form-input" placeholder="请输入联系人姓名" value="{if isset($post.contact_name)}{$post.contact_name}{/if}" required>
                                <div class="field-error" id="contact_name_error"><i class="fas fa-exclamation-circle"></i>请输入联系人姓名</div>
                                <div class="field-success"><i class="fas fa-check-circle"></i></div>
                                <div class="field-focus"><i class="fas fa-info-circle"></i></div>
                                <div class="field-focus-tooltip">请填写能够联系到您的真实姓名</div>
                            </div>
                        </div>
                        
                        <!-- 联系电话 -->
                        <div class="form-group required">
                            <label for="contact_mobile" class="form-label">联系电话</label>
                            <div class="form-right">
                                <input type="tel" name="contact_mobile" id="contact_mobile" class="form-input" placeholder="请输入手机号码" value="{if isset($post.contact_mobile)}{$post.contact_mobile}{/if}" required>
                                <div class="field-error" id="contact_mobile_error"><i class="fas fa-exclamation-circle"></i>请输入正确的手机号码</div>
                                <div class="field-success"><i class="fas fa-check-circle"></i></div>
                                <div class="field-focus"><i class="fas fa-info-circle"></i></div>
                                <div class="field-focus-tooltip">请填写您的11位手机号码</div>
                            </div>
                        </div>

                        <!-- 详细地址 -->
                        <div class="form-group">
                            <label for="contact_address" class="form-label">详细地址</label>
                            <div class="form-right">
                                <input type="text" name="contact_address" id="contact_address" class="form-input" placeholder="请输入详细地址（选填）" value="{if isset($post.contact_address)}{$post.contact_address}{/if}">
                                <div class="field-success"><i class="fas fa-check-circle"></i></div>
                                <div class="field-focus"><i class="fas fa-info-circle"></i></div>
                                <div class="field-focus-tooltip">请填写详细的地址信息（选填）</div>
                            </div>
                        </div>

                        <!-- 微信号 -->
                        <div class="form-group">
                            <label for="contact_weixin" class="form-label">微信号</label>
                            <div class="form-right weixin-group">
                                <input type="text" name="contact_weixin" id="contact_weixin" class="form-input" placeholder="微信号（选填）" value="{if isset($post.contact_weixin)}{$post.contact_weixin}{/if}">
                                <div class="field-success"><i class="fas fa-check-circle"></i></div>
                                <div class="field-focus"><i class="fas fa-info-circle"></i></div>
                                <div class="field-focus-tooltip">请填写您的微信号（选填）</div>
                                <label class="weixin-checkbox">
                                    <input type="checkbox" id="weixin_same"> 与手机相同
                                </label>
                            </div>
                        </div>
                        
                        <!-- 管理密码 -->
                        <div class="form-group required">
                            <label for="password" class="form-label">管理密码</label>
                            <div class="form-right">
                                <input type="password" name="password" id="password" class="form-input" placeholder="请设置管理密码" required>
                                <div class="field-error" id="password_error"><i class="fas fa-exclamation-circle"></i>请设置管理密码</div>
                                <div class="field-success"><i class="fas fa-check-circle"></i></div>
                                <div class="field-focus"><i class="fas fa-info-circle"></i></div>
                                <div class="field-focus-tooltip">请设置6位以上的密码，用于后续管理</div>
                                <div class="form-hint">设置密码用于后续修改和删除信息</div>
                            </div>
                        </div>
                        
                        <!-- 提交按钮 -->
                        <div class="submit-group">
                            <button type="submit" class="submit-button" id="submit-btn">提交发布</button>
                        </div>
                        
                        <!-- 发布须知 -->
                        <div class="posting-notice">
                            <h4 class="notice-title">
                                <i class="fas fa-exclamation-circle"></i>
                                <span>发布须知</span>
                                <i class="fas fa-chevron-down toggle-icon"></i>
                            </h4>
                            <div class="notice-content">
                                <p>①每天限发1条信息,三天内不允许发重复信息</p>
                                <p>②采用信息审核制,须经审核才会显示,应遵守法律法规</p>
                                <p>③后台审核信息时可能会对部分字词句进行调整</p>
                                <p class="highlight">④如有图片，请上传自己拍摄的图片，盗图或使用字体侵权风险自担</p>
                                <p>⑤招聘不得限定男女性别,涉嫌违法一律不予通过</p>
                                <p>⑥如继续发布提交信息,视为您已知晓并同意该协议</p>
                                <p>⑦请认真阅读本页"发布须知"，以及<a href="/help/statement.html" target="_blank" class="agreement-link">网站声明</a>、<a href="/help/review.html" target="_blank" class="agreement-link">审核条例</a>文档内容</p>
                                <p class="highlight">⑧点击"提交发布"即代表你已阅读并同意我们的<a href="/help/service.html" target="_blank" class="agreement-link">服务条款</a>!</p>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            
            <!-- 右侧提示区域 -->
            <div class="right-column">
                <div class="side-panel">
                    <div class="panel-title">发布流程</div>
                    <div class="panel-content">
                        <div class="process-list">
                            <div class="process-item">
                                <span class="process-num">1</span>
                                <span class="process-text">选择合适的信息栏目</span>
                            </div>
                            <div class="process-item">
                                <span class="process-num">2</span>
                                <span class="process-text">填写详细的信息内容</span>
                            </div>
                            <div class="process-item">
                                <span class="process-num">3</span>
                                <span class="process-text">提交并等待审核</span>
                            </div>
                            <div class="process-item">
                                <span class="process-num">4</span>
                                <span class="process-text">信息发布成功</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="side-panel">
                    <div class="panel-title">温馨提示</div>
                    <div class="panel-content">
                        <ul class="tips-list">
                            <li>请选择最符合您信息内容的栏目</li>
                            <li>正确选择栏目更容易被目标用户看到</li>
                            <li>若无合适栏目可联系客服反馈</li>
                            <li>信息审核时间为工作时间内1-2小时</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载中遮罩 -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <div class="loading-text">正在提交中...</div>
        </div>
    </div>
    
    <!-- 提交成功提示 -->
    <div class="success-overlay" id="success-overlay">
        <div class="success-message">
            <i class="fas fa-check-circle"></i>
            <div class="success-text">提交成功！</div>
            <div class="success-subtext">您的信息已提交，正在等待审核</div>
            <button class="success-btn" id="success-btn">确定</button>
        </div>
    </div>

    <!-- 底部 -->
    {include file="footer.htm"}
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery-validate/1.19.3/localization/messages_zh.min.js"></script>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // 表单验证
        const form = document.querySelector('form');
        const loadingOverlay = document.getElementById('loading-overlay');
        const successOverlay = document.getElementById('success-overlay');
        const successBtn = document.getElementById('success-btn');
        
        // 发布须知折叠功能
        const noticeTitle = document.querySelector('.notice-title');
        const noticeContent = document.querySelector('.notice-content');
        const toggleIcon = document.querySelector('.toggle-icon');
        
        noticeTitle.addEventListener('click', function() {
            noticeContent.classList.toggle('collapsed');
            toggleIcon.classList.toggle('rotate');
        });
        
        // jQuery表单验证
        $(document).ready(function() {
            // 自定义验证方法 - 手机号
            $.validator.addMethod("isMobile", function(value, element) {
                var mobile = /^1\d{10}$/;
                return this.optional(element) || (mobile.test(value));
            }, "请输入正确的手机号码");
            
            // 验证配置
            $("#post-form").validate({
                errorClass: "field-error show",
                errorElement: "div",
                // 错误信息位置
                errorPlacement: function(error, element) {
                    error.prepend('<i class="fas fa-exclamation-circle"></i>');
                    error.insertAfter(element);
                },
                // 高亮显示错误字段
                highlight: function(element, errorClass) {
                    var $element = $(element);
                    $element.addClass("error").removeClass("valid");
                    // 隐藏成功图标和焦点图标
                    $element.next(".field-error").siblings(".field-success, .field-focus").removeClass("show");
                },
                // 移除错误高亮
                unhighlight: function(element, errorClass) {
                    var $element = $(element);
                    $element.removeClass("error");
                    
                    // 如果字段有值，显示成功图标
                    if ($element.val()) {
                        $element.addClass("valid");
                        $element.next(".field-error").siblings(".field-success").addClass("show");
                    }
                },
                // 成功处理
                success: function(label, element) {
                    var $element = $(element);
                    $element.addClass("valid").removeClass("error");
                    // 显示成功图标
                    $element.next(".field-error").siblings(".field-success").addClass("show");
                    $element.next(".field-error").siblings(".field-focus").removeClass("show");
                },
                // 验证规则
                rules: {
                    region_id: "required",
                    expire_days: "required",
                    title: "required",
                    content: "required",
                    contact_name: "required",
                    contact_mobile: {
                        required: true,
                        isMobile: true
                    },
                    password: "required"
                },
                // 错误信息
                messages: {
                    region_id: "请选择所在地区",
                    expire_days: "请选择有效期",
                    title: "请输入标题",
                    content: "请输入详细内容",
                    contact_name: "请输入联系人姓名",
                    contact_mobile: {
                        required: "请输入手机号码",
                        isMobile: "请输入正确的手机号码"
                    },
                    password: "请设置管理密码"
                },
                // 提交处理
                submitHandler: function(form) {
                    // 显示加载中
                    loadingOverlay.classList.add('show');
                    
                    // 创建FormData对象
                    const formData = new FormData(form);
                    
                    // 使用fetch提交表单
                    fetch(form.action, {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.text())
                    .then(() => {
                        loadingOverlay.classList.remove('show');
                        successOverlay.classList.add('show');
                    })
                    .catch(error => {
                        loadingOverlay.classList.remove('show');
                        alert('提交失败，请重试');
                    });
                    
                    return false; // 阻止默认表单提交
                }
            });
            
            // 添加焦点状态处理
            $("#post-form input, #post-form select, #post-form textarea").on("focus", function() {
                var $this = $(this);
                
                // 清除所有字段的焦点状态
                $("#post-form .field-focus").removeClass("show");
                
                // 如果字段没有错误状态，显示焦点图标及提示
                if (!$this.hasClass("error")) {
                    $this.siblings(".field-focus").addClass("show");
                    $this.siblings(".field-success").removeClass("show");
                }
            }).on("blur", function() {
                var $this = $(this);
                
                // 移除焦点状态
                $this.siblings(".field-focus").removeClass("show");
                
                // 如果字段有值且验证通过，显示成功图标
                if ($this.val() && !$this.hasClass("error")) {
                    $this.addClass("valid");
                    $this.siblings(".field-success").addClass("show");
                }
            });
            
            // 初始化时为已有值且非必填的字段显示成功图标
            $("#post-form input:not([required]), #post-form select:not([required]), #post-form textarea:not([required])").each(function() {
                var $this = $(this);
                if ($this.val()) {
                    $this.addClass("valid");
                    $this.siblings(".field-success").addClass("show");
                }
            });
            
            // 初始化时对已有值的必填字段进行验证
            $("#post-form input[required], #post-form select[required], #post-form textarea[required]").each(function() {
                var $this = $(this);
                if ($this.val()) {
                    // 触发验证
                    $this.valid();
                }
            });
        });
        
        // 图片上传预览
        const input = document.getElementById('image_uploads');
        const preview = document.getElementById('image-previews');
        
        input.addEventListener('change', function() {
            while(preview.firstChild) {
                preview.removeChild(preview.firstChild);
            }
            
            if (this.files.length > 9) {
                alert('最多只能上传9张图片');
                this.value = '';
                return;
            }
            
            Array.from(this.files).forEach((file, index) => {
                if (!file.type.match('image.*')) return;
                
                if (file.size > 2 * 1024 * 1024) {
                    alert('图片 ' + file.name + ' 超过2MB，请重新选择');
                    this.value = '';
                    return;
                }
                
                const reader = new FileReader();
                reader.onload = function(e) {
                    const div = document.createElement('div');
                    div.className = 'image-item';
                    div.draggable = true;
                    div.dataset.index = index;
                    
                    const img = document.createElement('img');
                    img.src = e.target.result;
                    
                    const removeBtn = document.createElement('span');
                    removeBtn.className = 'remove-image';
                    removeBtn.textContent = '×';
                    removeBtn.onclick = () => div.remove();
                    
                    div.appendChild(img);
                    div.appendChild(removeBtn);
                    preview.appendChild(div);
                    
                    // 添加拖拽事件处理
                    enableDragSort(div);
                };
                reader.readAsDataURL(file);
            });
        });
        
        // 图片拖拽排序功能
        function enableDragSort(item) {
            item.addEventListener('dragstart', handleDragStart);
            item.addEventListener('dragover', handleDragOver);
            item.addEventListener('dragenter', handleDragEnter);
            item.addEventListener('dragleave', handleDragLeave);
            item.addEventListener('drop', handleDrop);
            item.addEventListener('dragend', handleDragEnd);
        }
        
        let draggedItem = null;
        
        function handleDragStart(e) {
            draggedItem = this;
            this.classList.add('dragging');
            e.dataTransfer.effectAllowed = 'move';
            e.dataTransfer.setData('text/html', this.innerHTML);
        }
        
        function handleDragOver(e) {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'move';
            return false;
        }
        
        function handleDragEnter(e) {
            this.classList.add('drag-over');
        }
        
        function handleDragLeave(e) {
            this.classList.remove('drag-over');
        }
        
        function handleDrop(e) {
            e.stopPropagation();
            e.preventDefault();
            
            if (draggedItem !== this) {
                // 交换位置
                const allItems = Array.from(document.querySelectorAll('.image-item'));
                const draggedIndex = allItems.indexOf(draggedItem);
                const targetIndex = allItems.indexOf(this);
                
                if (draggedIndex < targetIndex) {
                    preview.insertBefore(draggedItem, this.nextSibling);
                } else {
                    preview.insertBefore(draggedItem, this);
                }
                
                // 更新索引
                allItems.forEach((item, i) => {
                    item.dataset.index = i;
                });
            }
            
            return false;
        }
        
        function handleDragEnd(e) {
            const items = document.querySelectorAll('.image-item');
            items.forEach(item => {
                item.classList.remove('dragging');
                item.classList.remove('drag-over');
            });
            draggedItem = null;
        }
        
        // 初始化所有已有图片项的拖拽功能
        document.querySelectorAll('.image-item').forEach(item => {
            enableDragSort(item);
        });
        
        // 微信号与手机号同步
        const mobileInput = document.getElementById('contact_mobile');
        const wechatInput = document.getElementById('contact_weixin');
        const wechatSame = document.getElementById('weixin_same');
        
        wechatSame.addEventListener('change', function() {
            if (this.checked) {
                wechatInput.value = mobileInput.value;
                wechatInput.disabled = true;
            } else {
                wechatInput.disabled = false;
            }
        });
        
        mobileInput.addEventListener('input', function() {
            if (wechatSame.checked) {
                wechatInput.value = this.value;
            }
        });
        
        // 成功提示框确定按钮
        successBtn.addEventListener('click', function() {
            window.location.href = '/';
        });
    });
    </script>
</body>
</html>
