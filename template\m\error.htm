<!DOCTYPE html>
<?php
// 获取主题设置
$theme = isset($_COOKIE['site_theme']) ? $_COOKIE['site_theme'] : 'orange';
$theme_class = 'theme-' . $theme;
?>
<html lang="zh-CN" class="<?php echo $theme_class; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>出错了 - <?php echo $site_name; ?></title>
    <link rel="stylesheet" href="/static/font-awesome/css/all.min.css">
    <link rel="stylesheet" href="/static/css/themes.css">
    <link rel="stylesheet" href="/template/m/css/common.css">
    <style>
        body {background:#f5f5f5;margin:0;padding:0;font-family:system-ui,-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,sans-serif;display:flex;flex-direction:column;min-height:100vh}
        
        .message-container {display:flex;align-items:flex-start;justify-content:center;width:100%;padding:20px;box-sizing:border-box;margin-top:60px}
        .message-panel {width:100%;max-width:320px;background:#fff;border-radius:2px;padding:25px 20px;text-align:center;box-shadow:none;border:1px solid #eee}
        .error-icon {margin-bottom:20px}
        .error-icon i {font-size:70px;color:#ff4d4f}
        .error-title {margin:0 0 15px;font-size:22px;font-weight:600;color:#333}
        .error-message {margin-bottom:25px;color:#666;line-height:1.5;font-size:15px}
        .btn {display:block;width:100%;padding:12px 0;background-color:var(--primary-color);color:white;text-decoration:none;border-radius:0;font-size:15px;font-weight:500;text-align:center;border:none;cursor:pointer}
        .btn:hover {opacity:0.95}
        .btn-secondary {background-color:#f5f5f5;color:#666;margin-top:10px}
        
        footer {width:100%;text-align:center;padding:15px 0;font-size:12px;color:#999;position:fixed;bottom:0}
    </style>
</head>
<body>
    <div class="message-container">
        <div class="message-panel">
            <div class="error-icon"><i class="fas fa-times-circle"></i></div>
            <h1 class="error-title">操作失败</h1>
            <div class="error-message"><?php echo $error; ?></div>
            <a href="{$redirect_url}" class="btn">返回上一页</a>
            <a href="/" class="btn btn-secondary">返回首页</a>
        </div>
    </div>

    <footer>
        <p>© <?php echo date('Y'); ?> <?php echo $site_name; ?> 版权所有</p>
    </footer>
    
    <?php if (!empty($redirect_url)): ?>
    <script>
        // 3秒后自动跳转
        setTimeout(function() {
            window.location.href = "<?php echo $redirect_url; ?>";
        }, 3000);
    </script>
    <?php endif; ?>
</body>
</html>