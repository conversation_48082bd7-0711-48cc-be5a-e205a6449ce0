<!DOCTYPE html>
<?php
// 获取主题设置
$theme = isset($_COOKIE['site_theme']) ? $_COOKIE['site_theme'] : 'orange';
$theme_class = 'theme-' . $theme;
?>
<html lang="zh-CN" class="<?php echo $theme_class; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>操作成功 - <?php echo $site_name; ?></title>
    <link rel="stylesheet" href="/static/font-awesome/css/all.min.css">
    <link rel="stylesheet" href="/static/css/themes.css">
    <link rel="stylesheet" href="/template/m/css/common.css">
    <style>
        body {background:#f5f5f5;margin:0;padding:0;font-family:system-ui,-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,sans-serif;display:flex;flex-direction:column;min-height:100vh}
        
        .message-container {display:flex;align-items:flex-start;justify-content:center;width:100%;padding:20px;box-sizing:border-box;margin-top:60px}
        .message-panel {width:100%;max-width:320px;background:#fff;border-radius:2px;padding:25px 20px;text-align:center;box-shadow:none;border:1px solid #eee}
        .success-icon {margin-bottom:20px}
        .success-icon i {font-size:70px;color:var(--primary-color)}
        .success-title {margin:0 0 15px;font-size:22px;font-weight:600;color:#333}
        .success-message {margin-bottom:25px;color:#666;line-height:1.5;font-size:15px}
        .countdown {font-size:14px;color:#999;margin-bottom:15px}
        .btn {display:block;width:100%;padding:12px 0;background-color:var(--primary-color);color:white;text-decoration:none;border-radius:0;font-size:15px;font-weight:500;text-align:center;border:none;cursor:pointer}
        .btn:hover {opacity:0.95}
        .btn-secondary {background-color:#f5f5f5;color:#666;margin-top:10px}
        
        footer {width:100%;text-align:center;padding:15px 0;font-size:12px;color:#999;position:fixed;bottom:0}
    </style>
</head>
<body>
    <div class="message-container">
        <div class="message-panel">
            <div class="success-icon"><i class="fas fa-check-circle"></i></div>
            <h1 class="success-title">操作成功</h1>
            <div class="success-message"><?php echo $success_message; ?></div>
            
            <?php if (isset($redirect_url) && !empty($redirect_url)): ?>
            <div class="countdown">页面将在 <span id="second">3</span> 秒后自动跳转</div>
            <a href="<?php echo $redirect_url; ?>" class="btn" id="redirect-btn">立即跳转</a>
            <a href="javascript:history.back();" class="btn btn-secondary">返回上一页</a>
            <?php else: ?>
            <a href="/" class="btn">返回首页</a>
            <a href="javascript:history.back();" class="btn btn-secondary">返回上一页</a>
            <?php endif; ?>
        </div>
    </div>

    <footer>
        <p>© <?php echo date('Y'); ?> <?php echo $site_name; ?> 版权所有</p>
    </footer>

    <?php if (isset($redirect_url) && !empty($redirect_url)): ?>
    <script>
        var second = 3;
        var timer = null;
        
        function countDown() {
            second--;
            if (second <= 0) {
                clearInterval(timer);
                window.location.href = '<?php echo $redirect_url; ?>';
                return;
            }
            document.getElementById('second').innerText = second;
        }
        
        timer = setInterval(countDown, 1000);
    </script>
    <?php endif; ?>
</body>
</html> 