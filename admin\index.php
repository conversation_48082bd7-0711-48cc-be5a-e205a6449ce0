<?php
// 定义安全常量
define('IN_BTMPS', true);
/**
 * 后台管理系统首页
 */
require_once('../include/common.inc.php');

// 检查管理员是否登录
if (!isset($_SESSION['admin']) || $_SESSION['admin']['is_login'] !== true) {
    header("Location: login.php");
    exit;
}

// 获取站点统计数据
$stats = array();

// 获取信息总数
$sql = "SELECT COUNT(*) as total FROM posts";
$result = $db->query($sql);
$row = $db->fetch_array($result);
$stats['info_total'] = $row ? $row['total'] : 0;

// 获取用户总数（统计posts表中不同的user_id，排除游客user_id=0）
$sql = "SELECT COUNT(DISTINCT user_id) as total FROM posts WHERE user_id > 0";
$result = $db->query($sql);
$row = $db->fetch_array($result);
$stats['user_total'] = $row ? $row['total'] : 0;

// 获取今日发布信息数
$sql = "SELECT COUNT(*) as total FROM posts WHERE TO_DAYS(FROM_UNIXTIME(created_at)) = TO_DAYS(NOW())";
$result = $db->query($sql);
$row = $db->fetch_array($result);
$stats['today_info'] = $row ? $row['total'] : 0;

// 获取待审核信息数
$sql = "SELECT COUNT(*) as total FROM posts WHERE status = 0";
$result = $db->query($sql);
$row = $db->fetch_array($result);
$stats['pending_info'] = $row ? $row['total'] : 0;

// 获取未处理举报数
$sql = "SELECT COUNT(*) as total FROM reports WHERE status = 0";
$result = $db->query($sql);
$row = $db->fetch_array($result);
$stats['pending_reports'] = $row ? $row['total'] : 0;

// 获取系统信息
$system_info = array(
    'os' => PHP_OS,
    'php_version' => PHP_VERSION,
    'mysql_version' => $db->version(),
    'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? '',
    'upload_max_filesize' => ini_get('upload_max_filesize'),
    'max_execution_time' => ini_get('max_execution_time') . '秒',
    'memory_limit' => ini_get('memory_limit'),
);

// 修改模板目录为admin/template目录
$tpl->setTemplateDir(dirname(__FILE__) . '/template/');

// 引入后台公共函数
require_once(dirname(__FILE__) . '/include/admin.fun.php');

// 设置公共模板变量
set_admin_template_vars($tpl, 'index', '控制台');

// 分配页面特定变量
$tpl->assign('stats', $stats);
$tpl->assign('system_info', $system_info);

// 显示后台首页模板
$tpl->display('index.htm'); 