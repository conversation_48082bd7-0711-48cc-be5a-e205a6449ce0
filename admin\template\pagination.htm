{if $pagination.total_pages > 1}
<div class="pagination-container">
    <ul class="pagination">
        {if $pagination.current_page > 1}
        <li class="page-item">
            <a class="page-link" href="{$pagination.first_link}" title="首页">&laquo;</a>
        </li>
        <li class="page-item">
            <a class="page-link" href="{$pagination.previous_link}" title="上一页">&lsaquo;</a>
        </li>
        {else}
        <li class="page-item disabled">
            <span class="page-link">&laquo;</span>
        </li>
        <li class="page-item disabled">
            <span class="page-link">&lsaquo;</span>
        </li>
        {/if}

        {foreach $pagination.page_links as $page => $link}
        <li class="page-item {if $page == $pagination.current_page}active{/if}">
            {if $page == $pagination.current_page}
            <span class="page-link">{$page}</span>
            {else}
            <a class="page-link" href="{$link}">{$page}</a>
            {/if}
        </li>
        {/foreach}

        {if $pagination.current_page < $pagination.total_pages}
        <li class="page-item">
            <a class="page-link" href="{$pagination.next_link}" title="下一页">&rsaquo;</a>
        </li>
        <li class="page-item">
            <a class="page-link" href="{$pagination.last_link}" title="末页">&raquo;</a>
        </li>
        {else}
        <li class="page-item disabled">
            <span class="page-link">&rsaquo;</span>
        </li>
        <li class="page-item disabled">
            <span class="page-link">&raquo;</span>
        </li>
        {/if}
    </ul>
    <div class="pagination-info">
        共 {$pagination.total_items} 条记录，每页 {$pagination.items_per_page} 条，共 {$pagination.total_pages} 页，当前第 {$pagination.current_page} 页
    </div>
</div>
{/if}