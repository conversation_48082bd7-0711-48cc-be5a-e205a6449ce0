<!-- 配置UEditor -->
<script type="text/javascript">
    // 确保UEditor能够找到自己的根路径
    window.UEDITOR_HOME_URL = "/admin/static/ueditor/";
</script>

<!-- 引入UEditor -->
<script type="text/javascript" src="/admin/static/ueditor/ueditor.config.js"></script>
<script type="text/javascript" src="/admin/static/ueditor/ueditor.all.min.js"></script>
<script type="text/javascript" src="/admin/static/js/ueditor-enhance.js"></script>

<!-- UEditor初始化脚本 -->
<script type="text/javascript">
    // 页面加载完成后初始化UEditor
    document.addEventListener('DOMContentLoaded', function() {
        var ue = UE.getEditor('editor', {
            initialFrameWidth: '100%',  // 设置编辑器宽度为100%
            initialFrameHeight: 500,    // 增加编辑器高度到500px
            toolbars: [
                ['fullscreen', 'source', '|', 'undo', 'redo', '|',
                'bold', 'italic', 'underline', 'fontborder', 'strikethrough', 'superscript', 'subscript', 'removeformat', 'formatmatch', 'autotypeset', 'blockquote', 'pasteplain', '|', 'forecolor', 'backcolor', 'insertorderedlist', 'insertunorderedlist', 'selectall', 'cleardoc', '|',
                'rowspacingtop', 'rowspacingbottom', 'lineheight', '|',
                'customstyle', 'paragraph', 'fontfamily', 'fontsize', '|',
                'directionalityltr', 'directionalityrtl', 'indent', '|',
                'justifyleft', 'justifycenter', 'justifyright', 'justifyjustify', '|',
                'link', 'unlink', '|', 'imagenone', 'imageleft', 'imageright', 'imagecenter', '|',
                'simpleupload', 'insertimage', 'emotion', 'insertvideo', '|',
                'horizontal', 'date', 'time', 'spechars', '|',
                'inserttable', 'deletetable', 'insertparagraphbeforetable', 'insertrow', 'deleterow', 'insertcol', 'deletecol', 'mergecells', 'mergeright', 'mergedown', 'splittocells', 'splittorows', 'splittocols', '|',
                'preview']
            ],
            enableAutoSave: true,      // 启用自动保存
            saveInterval: 60000,        // 自动保存间隔，单位为毫秒
            zIndex: 9999,              // 编辑器层级
            autoHeightEnabled: false,  // 是否自动高度
            // 图片相关配置
            imageScaleEnabled: true,   // 启用图片拉伸缩放
            imagePopup: true,          // 启用图片操作浮层
            // 自定义CSS样式注入到编辑器内容区域
            iframeCssUrl: '/admin/static/css/ueditor-content.css',
            // 图片插入时的默认样式
            imageInsertAlign: 'center',  // 图片插入对齐方式：none, left, right, center
            // 粘贴图片时自动上传
            enablePasteUpload: true,
            // 拖拽上传
            enableDragUpload: true
        });

        // 编辑器加载完成后的回调
        ue.ready(function() {
            console.log('UEditor 编辑器加载完成');

            // 注入图片响应式样式到编辑器内容区域
            var editorDoc = ue.getDocument();
            if (editorDoc) {
                var style = editorDoc.createElement('style');
                style.type = 'text/css';
                style.innerHTML = `
                    img {
                        max-width: 100% !important;
                        height: auto !important;
                        display: block;
                        margin: 10px auto;
                        border-radius: 4px;
                        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                        cursor: pointer;
                        transition: all 0.3s ease;
                    }
                    img:hover {
                        box-shadow: 0 4px 16px rgba(0,0,0,0.15);
                        transform: scale(1.02);
                    }
                    img[align="left"] {
                        float: left !important;
                        margin: 10px 15px 10px 0 !important;
                        display: inline !important;
                    }
                    img[align="right"] {
                        float: right !important;
                        margin: 10px 0 10px 15px !important;
                        display: inline !important;
                    }
                    img[align="center"] {
                        display: block !important;
                        margin: 15px auto !important;
                        float: none !important;
                    }
                    p:after {
                        content: "";
                        display: table;
                        clear: both;
                    }
                `;
                editorDoc.head.appendChild(style);
            }

            // 监听图片插入事件，确保新插入的图片应用响应式样式
            ue.addListener('afterInsertImage', function(type, imgObjs) {
                if (imgObjs && imgObjs.length > 0) {
                    for (var i = 0; i < imgObjs.length; i++) {
                        var img = imgObjs[i];
                        if (img && img.style) {
                            // 移除固定宽高，应用响应式样式
                            img.style.maxWidth = '100%';
                            img.style.height = 'auto';
                            img.style.display = 'block';
                            img.style.margin = '10px auto';
                            img.style.borderRadius = '4px';
                            img.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
                            img.style.cursor = 'pointer';
                            img.style.transition = 'all 0.3s ease';

                            // 移除可能存在的固定尺寸属性
                            img.removeAttribute('width');
                            img.removeAttribute('height');
                        }
                    }
                }
            });
        });
    });
</script> 