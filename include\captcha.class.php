<?php
/**
 * 验证码类
 * 用于生成和验证验证码
 */

if (!defined('IN_BTMPS')) {
    exit('Access Denied');
}

class Captcha {
    // 验证码图片宽度
    private $width = 100;
    
    // 验证码图片高度
    private $height = 50;
    
    // 验证码长度
    private $codeLength = 4;
    
    // 验证码字符集
    private $chars = 'abcdefghjkmnpqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ23456789';
    
    // 验证码存储在session中的键名
    private $sessionKey = 'verify_code';
    
    // 干扰线数量
    private $noiseLines = 4;
    
    // 干扰点数量
    private $noisePixels = 50;
    
    // 字体文件
    private $fontFile;
    
    // 字体大小
    private $fontSize = 30;
    
    /**
     * 构造函数，可以设置验证码配置
     * 
     * @param array $config 配置参数
     */
    public function __construct($config = array()) {
        // 设置默认字体路径 - 优先查找常见系统字体
        $fontPaths = array(
            // Windows 常见字体
            'C:/Windows/Fonts/arial.ttf',
            'C:/Windows/Fonts/simhei.ttf',         // 黑体
            'C:/Windows/Fonts/simkai.ttf',         // 楷体
            'C:/Windows/Fonts/msyh.ttf',           // 微软雅黑
            'C:/Windows/Fonts/simsun.ttc',         // 宋体
            
            // Linux 常见字体
            '/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf',
            '/usr/share/fonts/truetype/liberation/LiberationSans-Bold.ttf',
            '/usr/share/fonts/liberation/LiberationSans-Regular.ttf',
            
            // Mac 常见字体
            '/Library/Fonts/Arial.ttf',
            '/Library/Fonts/Verdana.ttf',
            
            // 自定义字体目录
            dirname(dirname(__FILE__)) . '/static/fonts/arial.ttf',
            dirname(dirname(__FILE__)) . '/static/fonts/verdana.ttf'
        );
        
        // 查找可用字体
        $fontFound = false;
        foreach ($fontPaths as $fontPath) {
            if (file_exists($fontPath)) {
                $this->fontFile = $fontPath;
                $fontFound = true;
                break;
            }
        }
        
        // 如果没有找到字体，就不使用TTF
        if (!$fontFound) {
            $this->fontFile = null;
        }
        
        // 设置配置参数
        if (isset($config['width'])) {
            $this->width = $config['width'];
        }
        
        if (isset($config['height'])) {
            $this->height = $config['height'];
        }
        
        if (isset($config['codeLength'])) {
            $this->codeLength = $config['codeLength'];
        }
        
        if (isset($config['chars'])) {
            $this->chars = $config['chars'];
        }
        
        if (isset($config['sessionKey'])) {
            $this->sessionKey = $config['sessionKey'];
        }
        
        if (isset($config['noiseLines'])) {
            $this->noiseLines = $config['noiseLines'];
        }
        
        if (isset($config['noisePixels'])) {
            $this->noisePixels = $config['noisePixels'];
        }
        
        if (isset($config['fontFile']) && file_exists($config['fontFile'])) {
            $this->fontFile = $config['fontFile'];
        }
        
        if (isset($config['fontSize'])) {
            $this->fontSize = $config['fontSize'];
        }
    }
    
    /**
     * 生成验证码并输出图片
     *
     * @return void
     */
    public function createImage() {
        // 启动会话（如果尚未启动）
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        // 创建图像
        $image = imagecreatetruecolor($this->width, $this->height);
        if (!$image) {
            throw new Exception('无法创建图像资源');
        }

        // 设置背景色为白色
        $bg_color = imagecolorallocate($image, 255, 255, 255);
        if ($bg_color === false) {
            imagedestroy($image);
            throw new Exception('无法分配背景颜色');
        }
        imagefill($image, 0, 0, $bg_color);

        // 添加彩色背景 - 使用渐变色
        $this->addGradientBackground($image);

        // 添加干扰线
        $this->addNoiseLines($image);

        // 添加干扰点
        $this->addNoisePixels($image);

        // 生成验证码
        $code = $this->generateCode();

        // 存储验证码到session
        $_SESSION[$this->sessionKey] = strtolower($code);

        // 计算每个字符的位置
        $codeLength = strlen($code);
        $spacing = $this->width / ($codeLength + 1);

        // 使用TrueType字体绘制文字
        if (function_exists('imagettftext') && $this->fontFile && file_exists($this->fontFile)) {
            for ($i = 0; $i < $codeLength; $i++) {
                // 使用深色文字 - 增加对比度
                $textColor = imagecolorallocate($image, mt_rand(0, 80), mt_rand(0, 80), mt_rand(0, 80));

                // 随机旋转角度 - 角度减小以增加可读性
                $angle = mt_rand(-15, 15);

                // 随机位置
                $x = (int)($spacing * ($i + 1) - mt_rand(0, 8));
                $y = (int)($this->height / 1.3 + mt_rand(-8, 8));

                // 绘制字符
                // 先绘制阴影，增加可读性
                $shadowColor = imagecolorallocate($image, 180, 180, 180);
                imagettftext(
                    $image,
                    $this->fontSize,
                    $angle,
                    $x + 1,
                    $y + 1,
                    $shadowColor,
                    $this->fontFile,
                    $code[$i]
                );

                // 绘制主字符
                imagettftext(
                    $image,
                    $this->fontSize,
                    $angle,
                    $x,
                    $y,
                    $textColor,
                    $this->fontFile,
                    $code[$i]
                );
            }
        } else {
            // 如果不支持TrueType字体，则使用内置字体
            for ($i = 0; $i < $codeLength; $i++) {
                $textColor = imagecolorallocate($image, mt_rand(0, 80), mt_rand(0, 80), mt_rand(0, 80));

                $x = (int)(($this->width / $codeLength) * $i + mt_rand(8, 15));
                $y = (int)($this->height / 2 + mt_rand(-8, 8));

                // 使用最大内置字体(5)
                imagestring($image, 5, $x, $y - 10, $code[$i], $textColor);
            }
        }

        // 添加一些轻微的扭曲效果 - 减小扭曲程度以提高可读性
        // $this->distortImage($image, 1, 25); // 暂时禁用扭曲以提高兼容性

        // 输出图片
        header('Content-Type: image/png');
        header('Cache-Control: no-cache, no-store, must-revalidate');
        header('Pragma: no-cache');
        header('Expires: 0');

        imagepng($image);
        imagedestroy($image);
    }
    
    /**
     * 添加渐变背景
     *
     * @param resource $image 图像资源
     */
    private function addGradientBackground($image) {
        // 检查图像资源是否有效
        if (!is_resource($image) && !($image instanceof GdImage)) {
            return;
        }

        // 创建浅色渐变背景
        $width = $this->width;
        $height = $this->height;

        // 渐变的起始颜色
        $r1 = 245; $g1 = 245; $b1 = 250;
        // 渐变的结束颜色
        $r2 = 250; $g2 = 250; $b2 = 245;

        // 绘制渐变
        for($i = 0; $i < $width; $i++) {
            // 计算当前位置的颜色
            $r = (int)($r1 + ($r2 - $r1) * ($i / $width));
            $g = (int)($g1 + ($g2 - $g1) * ($i / $width));
            $b = (int)($b1 + ($b2 - $b1) * ($i / $width));

            $color = imagecolorallocate($image, $r, $g, $b);
            if ($color !== false) {
                imageline($image, $i, 0, $i, $height, $color);
            }
        }
    }
    
    /**
     * 添加干扰线
     *
     * @param resource $image 图像资源
     */
    private function addNoiseLines($image) {
        // 检查图像资源是否有效
        if (!is_resource($image) && !($image instanceof GdImage)) {
            return;
        }

        for ($i = 0; $i < $this->noiseLines; $i++) {
            $lineColor = imagecolorallocate(
                $image,
                mt_rand(180, 220),
                mt_rand(180, 220),
                mt_rand(180, 220)
            );

            if ($lineColor !== false) {
                // 曲线干扰
                $x1 = mt_rand(0, $this->width - 1);
                $y1 = mt_rand(0, $this->height - 1);
                $x2 = mt_rand(0, $this->width - 1);
                $y2 = mt_rand(0, $this->height - 1);

                imageline($image, $x1, $y1, $x2, $y2, $lineColor);
            }
        }
    }

    /**
     * 添加干扰点
     *
     * @param resource $image 图像资源
     */
    private function addNoisePixels($image) {
        // 检查图像资源是否有效
        if (!is_resource($image) && !($image instanceof GdImage)) {
            return;
        }

        for ($i = 0; $i < $this->noisePixels; $i++) {
            $pixelColor = imagecolorallocate(
                $image,
                mt_rand(180, 220),
                mt_rand(180, 220),
                mt_rand(180, 220)
            );

            if ($pixelColor !== false) {
                imagesetpixel(
                    $image,
                    mt_rand(0, $this->width - 1),
                    mt_rand(0, $this->height - 1),
                    $pixelColor
                );
            }
        }
    }
    
    /**
     * 为图像添加扭曲效果
     *
     * @param resource $image 图像资源
     * @param int $amp 波纹强度
     * @param int $period 波纹周期
     */
    private function distortImage($image, $amp = 2, $period = 25) {
        // 检查图像资源是否有效
        if (!is_resource($image) && !($image instanceof GdImage)) {
            return;
        }

        // 添加轻微的波纹扭曲
        $tempImage = imagecreatetruecolor($this->width, $this->height);
        if (!$tempImage) {
            return;
        }

        $bg = imagecolorallocate($tempImage, 255, 255, 255);
        if ($bg === false) {
            imagedestroy($tempImage);
            return;
        }
        imagefill($tempImage, 0, 0, $bg);

        // 创建扭曲效果
        for ($x = 0; $x < $this->width; $x++) {
            for ($y = 0; $y < $this->height; $y++) {
                // 正弦波 X 方向
                $newX = (int)($x + sin($y / $period) * $amp);

                // 余弦波 Y 方向
                $newY = (int)($y + cos($x / $period) * $amp);

                // 边界检查
                if ($newX < 0 || $newX >= $this->width || $newY < 0 || $newY >= $this->height) {
                    continue;
                }

                $color = imagecolorat($image, $newX, $newY);
                if ($color !== false) {
                    imagesetpixel($tempImage, $x, $y, $color);
                }
            }
        }

        // 复制扭曲后的图像
        imagecopy($image, $tempImage, 0, 0, 0, 0, $this->width, $this->height);
        imagedestroy($tempImage);
    }
    
    /**
     * 生成随机验证码
     * 
     * @return string 生成的验证码
     */
    private function generateCode() {
        $code = '';
        $chars_length = strlen($this->chars) - 1;
        
        for ($i = 0; $i < $this->codeLength; $i++) {
            $code .= $this->chars[mt_rand(0, $chars_length)];
        }
        
        return $code;
    }
    
    /**
     * 获取当前验证码（用于测试）
     * 
     * @return string 当前验证码
     */
    public function getCode() {
        return isset($_SESSION[$this->sessionKey]) ? $_SESSION[$this->sessionKey] : '';
    }
    
    /**
     * 验证输入的验证码是否正确
     * 
     * @param string $input 用户输入的验证码
     * @param boolean $case_sensitive 是否大小写敏感，默认为false
     * @return boolean 验证结果
     */
    public function verify($input, $case_sensitive = false) {
        if (!isset($_SESSION[$this->sessionKey])) {
            return false;
        }
        
        $savedCode = $_SESSION[$this->sessionKey];
        
        if (!$case_sensitive) {
            $savedCode = strtolower($savedCode);
            $input = strtolower($input);
        }
        
        return $savedCode === $input;
    }
    
    /**
     * 创建图片并返回完整URL
     * 
     * @param string $url 验证码生成脚本的URL
     * @return string 验证码图片URL
     */
    public static function getImageUrl($url = 'captcha.php') {
        // 添加随机参数防止缓存
        return $url . '?t=' . time();
    }
    
    /**
     * 重置验证码
     * 
     * @return void
     */
    public function reset() {
        if (isset($_SESSION[$this->sessionKey])) {
            unset($_SESSION[$this->sessionKey]);
        }
    }
} 