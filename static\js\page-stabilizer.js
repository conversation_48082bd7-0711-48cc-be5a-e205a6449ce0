/**
 * 页面稳定器 - 减少页面加载时的晃动
 */
document.addEventListener('DOMContentLoaded', function() {
    // 预设关键元素高度
    presetHeights();
    
    // 添加图片加载优化
    optimizeImageLoading();
    
    // 添加内容区域的可见性控制
    controlContentVisibility();
});

// 预设元素高度，防止晃动
function presetHeights() {
    // 对列表项设置最小高度
    document.querySelectorAll('.post-item').forEach(function(item) {
        if (!item.style.minHeight) {
            item.style.minHeight = '90px';
        }
    });
    
    document.querySelectorAll('.simple-item').forEach(function(item) {
        if (!item.style.minHeight) {
            item.style.minHeight = '42px';
        }
    });
    
    // 设置页面关键部分的最小高度
    const viewportHeight = window.innerHeight;
    const contentArea = document.getElementById('image-list') || document.getElementById('text-list');
    if (contentArea && !contentArea.style.minHeight) {
        contentArea.style.minHeight = (viewportHeight * 0.6) + 'px';
    }
}

// 优化图片加载
function optimizeImageLoading() {
    // 使用传统方式加载图片，确保图片正常显示
    document.querySelectorAll('.post-image img').forEach(function(img) {
        // 添加loaded类，确保图片可见
        img.classList.add('loaded');
        img.style.opacity = '1';
        
        // 确保图片背景透明
        img.style.backgroundColor = 'transparent';
        
        // 如果图片加载失败，显示默认图片
        img.onerror = function() {
            if (!img.src.includes('no-image.png')) {
                img.src = '/static/images/no-image.png';
            }
        };
    });
}

// 控制内容区域的可见性，减少布局重排
function controlContentVisibility() {
    // 存储当前视图首选项
    const preferredView = localStorage.getItem('preferred_view') || 'image';
    
    // 设置默认显示内容
    const imageList = document.getElementById('image-list');
    const textList = document.getElementById('text-list');
    
    if (imageList && textList) {
        if (preferredView === 'image') {
            imageList.style.display = 'block';
            textList.style.display = 'none';
        } else {
            imageList.style.display = 'none';
            textList.style.display = 'block';
        }
    }
    
    // 减少CSS动画导致的布局重排
    document.addEventListener('DOMContentLoaded', function() {
        // 添加class来触发过渡，而不是直接修改样式
        document.body.classList.add('content-loaded');
    });
}