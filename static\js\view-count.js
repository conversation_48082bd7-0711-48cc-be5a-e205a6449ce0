/**
 * 浏览次数实时更新脚本
 * 用于详情页面的浏览次数实时显示和更新
 */

(function() {
    'use strict';
    
    // 配置参数
    var config = {
        updateInterval: 30000, // 30秒更新一次浏览次数显示
        updateDelay: 2000,     // 页面加载2秒后开始更新浏览次数
        apiUrl: '/api/view_count.php',
        viewCountSelector: '.view-count, #view-count, [data-view-count]'
    };
    
    // 当前页面的信息ID
    var currentPostId = null;
    
    // 更新定时器
    var updateTimer = null;
    
    // 是否已经更新过浏览次数
    var hasUpdated = false;
    
    /**
     * 初始化浏览次数管理
     */
    function init() {
        // 从页面获取当前信息ID
        currentPostId = getCurrentPostId();
        
        if (!currentPostId) {
            console.log('ViewCount: 未找到信息ID，跳过浏览次数管理');
            return;
        }
        
        console.log('ViewCount: 初始化浏览次数管理，信息ID:', currentPostId);
        
        // 延迟更新浏览次数（避免影响页面加载速度）
        setTimeout(function() {
            updateViewCount();
        }, config.updateDelay);
        
        // 定期更新浏览次数显示
        startPeriodicUpdate();
        
        // 页面离开时的处理
        setupPageUnloadHandler();
    }
    
    /**
     * 从页面获取当前信息ID
     */
    function getCurrentPostId() {
        // 方法1: 从URL参数获取
        var urlParams = new URLSearchParams(window.location.search);
        var id = urlParams.get('id');
        
        if (id) {
            return parseInt(id);
        }
        
        // 方法2: 从页面元素获取
        var postElement = document.querySelector('[data-post-id]');
        if (postElement) {
            return parseInt(postElement.getAttribute('data-post-id'));
        }
        
        // 方法3: 从全局变量获取
        if (typeof window.postId !== 'undefined') {
            return parseInt(window.postId);
        }
        
        // 方法4: 从URL路径解析（如 /category/123.html）
        var pathMatch = window.location.pathname.match(/\/(\d+)\.html$/);
        if (pathMatch) {
            return parseInt(pathMatch[1]);
        }
        
        return null;
    }
    
    /**
     * 更新浏览次数
     */
    function updateViewCount() {
        if (!currentPostId || hasUpdated) {
            return;
        }
        
        // 发送AJAX请求更新浏览次数
        var xhr = new XMLHttpRequest();
        xhr.open('GET', config.apiUrl + '?action=update&id=' + currentPostId, true);
        xhr.onreadystatechange = function() {
            if (xhr.readyState === 4) {
                if (xhr.status === 200) {
                    try {
                        var response = JSON.parse(xhr.responseText);
                        if (response.success) {
                            hasUpdated = true;
                            updateViewCountDisplay(response.view_count);
                            console.log('ViewCount: 浏览次数更新成功，当前:', response.view_count);
                        } else {
                            console.error('ViewCount: 更新失败:', response.message);
                        }
                    } catch (e) {
                        console.error('ViewCount: 解析响应失败:', e);
                    }
                } else {
                    console.error('ViewCount: 请求失败，状态码:', xhr.status);
                }
            }
        };
        xhr.send();
    }
    
    /**
     * 获取当前浏览次数（不更新）
     */
    function getCurrentViewCount() {
        if (!currentPostId) {
            return;
        }
        
        var xhr = new XMLHttpRequest();
        xhr.open('GET', config.apiUrl + '?action=get&id=' + currentPostId, true);
        xhr.onreadystatechange = function() {
            if (xhr.readyState === 4 && xhr.status === 200) {
                try {
                    var response = JSON.parse(xhr.responseText);
                    if (response.success) {
                        updateViewCountDisplay(response.view_count);
                    }
                } catch (e) {
                    console.error('ViewCount: 获取浏览次数失败:', e);
                }
            }
        };
        xhr.send();
    }
    
    /**
     * 更新页面上的浏览次数显示
     */
    function updateViewCountDisplay(viewCount) {
        var elements = document.querySelectorAll(config.viewCountSelector);
        
        for (var i = 0; i < elements.length; i++) {
            var element = elements[i];
            
            // 如果元素有data-format属性，使用格式化显示
            var format = element.getAttribute('data-format');
            var displayText = viewCount;
            
            if (format) {
                displayText = format.replace('{count}', viewCount);
            } else {
                // 默认格式化（添加千分位分隔符）
                displayText = formatNumber(viewCount);
            }
            
            // 更新显示内容
            if (element.tagName === 'INPUT') {
                element.value = displayText;
            } else {
                element.textContent = displayText;
            }
            
            // 添加更新动画效果
            element.classList.add('view-count-updated');
            setTimeout(function() {
                element.classList.remove('view-count-updated');
            }, 1000);
        }
    }
    
    /**
     * 格式化数字（添加千分位分隔符）
     */
    function formatNumber(num) {
        return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    }
    
    /**
     * 开始定期更新浏览次数显示
     */
    function startPeriodicUpdate() {
        updateTimer = setInterval(function() {
            getCurrentViewCount();
        }, config.updateInterval);
    }
    
    /**
     * 停止定期更新
     */
    function stopPeriodicUpdate() {
        if (updateTimer) {
            clearInterval(updateTimer);
            updateTimer = null;
        }
    }
    
    /**
     * 设置页面离开时的处理
     */
    function setupPageUnloadHandler() {
        // 页面可见性变化时的处理
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                // 页面隐藏时停止更新
                stopPeriodicUpdate();
            } else {
                // 页面显示时恢复更新
                startPeriodicUpdate();
                // 立即获取一次最新的浏览次数
                getCurrentViewCount();
            }
        });
        
        // 页面离开时的处理
        window.addEventListener('beforeunload', function() {
            stopPeriodicUpdate();
        });
    }
    
    /**
     * 公开API
     */
    window.ViewCount = {
        init: init,
        update: updateViewCount,
        getCurrent: getCurrentViewCount,
        setPostId: function(id) {
            currentPostId = parseInt(id);
        }
    };
    
    // 页面加载完成后自动初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
    
})();

// CSS样式（可以移到单独的CSS文件中）
var style = document.createElement('style');
style.textContent = `
.view-count-updated {
    transition: color 0.3s ease;
    color: #007bff !important;
}

.view-count {
    font-weight: bold;
}

@keyframes viewCountPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.view-count-updated {
    animation: viewCountPulse 0.5s ease-in-out;
}
`;
document.head.appendChild(style);
