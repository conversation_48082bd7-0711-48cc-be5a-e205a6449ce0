{include file="header.htm"} 

<style>
.table-responsive {
    overflow-x: auto;
}
.info-actions {
    display: flex;
    flex-wrap: nowrap;
    justify-content: flex-end;
    gap: 5px;
    min-width: 120px;
}
.info-actions .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    white-space: nowrap;
    text-decoration: none !important;
}
.info-actions .btn-group {
    display: inline-block;
}
a, a:hover, a:focus, a:active {
    text-decoration: none !important;
}
.nav-link {
    text-decoration: none !important;
}
.nav-link.active {
    background-color: #3490dc;
    color: #fff;
}

/* 淡色按钮样式 */
.btn-light-primary {
    background-color: #e6f0ff;
    color: #1b68ff;
    border: 1px solid #cce0ff;
}
.btn-light-primary:hover {
    background-color: #d1e3ff;
    color: #0056b3;
}
.btn-light-warning {
    background-color: #fff8e6;
    color: #ffa500;
    border: 1px solid #ffe6b3;
}
.btn-light-warning:hover {
    background-color: #fff0d1;
    color: #cc8400;
}
.btn-light-danger {
    background-color: #ffe6e6;
    color: #ff3333;
    border: 1px solid #ffb3b3;
}
.btn-light-danger:hover {
    background-color: #ffd1d1;
    color: #cc0000;
}
.btn-light-info {
    background-color: #e6f7ff;
    color: #00aaff;
    border: 1px solid #b3e0ff;
}
.btn-light-info:hover {
    background-color: #d1f0ff;
    color: #0088cc;
}
.btn-light-success {
    background-color: #e6ffe6;
    color: #00aa00;
    border: 1px solid #b3ffb3;
}
.btn-light-success:hover {
    background-color: #d1ffd1;
    color: #008800;
}
.btn-light-secondary {
    background-color: #f0f0f0;
    color: #666666;
    border: 1px solid #dddddd;
}
.btn-light-secondary:hover {
    background-color: #e0e0e0;
    color: #444444;
}

/* 固定表格列宽 */
.table {
    width: 100%;
    table-layout: fixed;
    white-space: nowrap;
}
.table th {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.table td {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
/* 设置每列的固定宽度 */
.table .col-checkbox { width: 40px; }
.table .col-id { width: 60px; }
.table .col-post-id { width: 80px; }
.table .col-post-title { width: 250px; }
.table .col-type { width: 100px; }
.table .col-content { width: 250px; }
.table .col-contact { width: 120px; }
.table .col-time { width: 120px; }
.table .col-status { width: 80px; }
.table .col-actions { width: 180px; }

/* 分页样式 */
.simple-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 5px;
}
.pagination-btn {
    display: inline-block;
    padding: 5px 12px;
    background: #fff;
    border: 1px solid #ddd;
    color: #333;
    text-decoration: none;
    border-radius: 3px;
    transition: all 0.2s;
}
.pagination-btn:hover {
    background: #f8f9fa;
    border-color: #ccc;
}
.pagination-btn.active {
    background: #1b68ff;
    color: white;
    border-color: #1b68ff;
}
.pagination-btn.disabled {
    color: #aaa;
    background: #f8f8f8;
    cursor: not-allowed;
}

/* 选项卡样式 */
.nav-tabs {
    display: flex;
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 20px;
    background-color: #f8f9fa;
    border-radius: 6px 6px 0 0;
    padding: 0;
    overflow-x: auto;
}

.nav-tab {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: #666;
    text-decoration: none;
    border-bottom: 3px solid transparent;
    transition: all 0.2s;
    white-space: nowrap;
    font-weight: 500;
    font-size: 14px;
    position: relative;
}

.nav-tab:hover {
    color: #1b68ff;
    background-color: #f0f8ff;
    text-decoration: none;
}

.nav-tab.active {
    color: #1b68ff;
    background-color: #fff;
    border-bottom-color: #1b68ff;
    font-weight: 600;
}

.nav-tab .badge {
    margin-left: 8px;
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 10px;
}

/* 状态标签样式 */
.badge {
    display: inline-block;
    padding: 0.25em 0.6em;
    font-size: 75%;
    font-weight: 500;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.25rem;
}

.badge-success {
    color: #fff;
    background-color: #28a745;
}

.badge-secondary {
    color: #fff;
    background-color: #6c757d;
}

.badge-danger {
    color: #fff;
    background-color: #dc3545;
}

.badge-warning {
    color: #212529;
    background-color: #ffc107;
}

.badge-info {
    color: #fff;
    background-color: #17a2b8;
}

.badge-light {
    color: #495057;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
}

/* 筛选表单样式 */
.filter-form {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 20px;
}

.filter-form-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-form-item label {
    font-weight: 600;
    color: #333;
    white-space: nowrap;
    margin: 0;
    font-size: 14px;
}

.filter-form-item .form-control {
    min-width: 120px;
    height: 32px;
    padding: 4px 8px;
    font-size: 14px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.filter-form-item .form-control:focus {
    border-color: #1b68ff;
    box-shadow: 0 0 0 0.2rem rgba(27, 104, 255, 0.25);
}

/* 按钮样式 */
.btn {
    display: inline-block;
    padding: 6px 12px;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.5;
    text-align: center;
    text-decoration: none;
    vertical-align: middle;
    cursor: pointer;
    border: 1px solid transparent;
    border-radius: 4px;
    transition: all 0.15s ease-in-out;
}

.btn-light-primary {
    background-color: #e6f0ff;
    color: #1b68ff;
    border: 1px solid #cce0ff;
}

.btn-light-primary:hover {
    background-color: #d1e3ff;
    color: #0056b3;
    text-decoration: none;
}

.btn-light-secondary {
    background-color: #f0f0f0;
    color: #666666;
    border: 1px solid #dddddd;
}

.btn-light-secondary:hover {
    background-color: #e0e0e0;
    color: #444444;
    text-decoration: none;
}

/* 移除链接下划线 */
a, a:hover, a:focus, a:active {
    text-decoration: none !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .nav-tabs {
        flex-direction: column;
    }

    .nav-tab {
        border-bottom: 1px solid #e9ecef;
        border-radius: 0;
    }

    .nav-tab.active {
        border-bottom-color: #e9ecef;
        border-left: 3px solid #1b68ff;
    }

    .filter-form {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .filter-form-item {
        flex-direction: column;
        align-items: stretch;
        gap: 5px;
    }

    .filter-form-item label {
        text-align: left;
    }
}
</style>

<!-- 消息提示 -->
{if $message}
<div class="alert alert-success">{$message}</div>
{/if}

<!-- 举报管理 -->
<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <div>
            <h3 class="card-title">举报列表</h3>
        </div>
    </div>
    
    <div class="card-body">
        <!-- 状态选项卡 -->
        <div class="nav-tabs">
            <a href="report.php?status=0" class="nav-tab {if !isset($status) || $status == 0}active{/if}">
                <i class="fas fa-exclamation-circle"></i>
                <span>未处理</span>
                {if isset($status_counts.pending) && $status_counts.pending > 0}
                <span class="badge badge-danger">{$status_counts.pending}</span>
                {/if}
            </a>
            <a href="report.php?status=1" class="nav-tab {if isset($status) && $status == 1}active{/if}">
                <i class="fas fa-check-circle"></i>
                <span>已处理</span>
            </a>
            <a href="report.php?status=-1" class="nav-tab {if isset($status) && $status == -1}active{/if}">
                <i class="fas fa-list"></i>
                <span>全部举报</span>
            </a>
        </div>

        <!-- 高级筛选表单 -->
        <form action="report.php" method="get">
            <input type="hidden" name="status" value="{if isset($status)}{$status}{else}0{/if}">
            <div class="filter-form">
                <div class="filter-form-item">
                    <label>举报类型:</label>
                    <select name="type" class="form-control" style="min-width: 150px;">
                        <option value="">全部类型</option>
                        <option value="虚假信息" {if isset($type) && $type == '虚假信息'}selected{/if}>虚假信息</option>
                        <option value="诈骗信息" {if isset($type) && $type == '诈骗信息'}selected{/if}>诈骗信息</option>
                        <option value="违法信息" {if isset($type) && $type == '违法信息'}selected{/if}>违法信息</option>
                        <option value="广告信息" {if isset($type) && $type == '广告信息'}selected{/if}>广告信息</option>
                        <option value="违规信息" {if isset($type) && $type == '违规信息'}selected{/if}>违规信息</option>
                        <option value="其他问题" {if isset($type) && $type == '其他问题'}selected{/if}>其他问题</option>
                    </select>
                </div>

                <div class="filter-form-item">
                    <label>信息ID:</label>
                    <input type="number" name="post_id" value="{if isset($post_id)}{$post_id}{/if}" placeholder="输入ID..." class="form-control" style="width: 100px;">
                </div>

                <div class="filter-form-item">
                    <label>关键词:</label>
                    <input type="text" name="keyword" value="{if isset($keyword)}{$keyword}{/if}" placeholder="搜索举报内容..." class="form-control" style="min-width: 200px;">
                </div>

                <div class="filter-form-item">
                    <button type="submit" class="btn btn-light-primary">
                        <i class="fas fa-search"></i> 筛选
                    </button>
                    <a href="report.php?status={if isset($status)}{$status}{else}0{/if}" class="btn btn-light-secondary">
                        <i class="fas fa-undo"></i> 重置
                    </a>
                </div>
            </div>
        </form>
        
        <!-- 举报列表 -->
        <form id="reportForm" action="report.php?action=batch_delete" method="post">
            <div class="table-responsive">
                <table class="table table-vcenter table-bordered table-hover">
                    <thead>
                        <tr>
                            <th class="col-checkbox"><input type="checkbox" id="selectAll"></th>
                            <th class="col-id">ID</th>
                            <th class="col-post-id">信息ID</th>
                            <th class="col-post-title">信息标题</th>
                            <th class="col-type">举报类型</th>
                            <th class="col-content">举报内容</th>
                            <th class="col-contact">联系方式</th>
                            <th class="col-time">提交时间</th>
                            <th class="col-status">状态</th>
                            <th class="col-actions" style="text-align: right;">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {if !$reports}
                        <tr>
                            <td colspan="10" class="text-center">暂无举报数据</td>
                        </tr>
                        {else}
                        {foreach $reports as $report}
                        <tr>
                            <td>
                                <input type="checkbox" name="report_ids[]" class="report-checkbox" value="{$report.id}">
                            </td>
                            <td>{$report.id}</td>
                            <td>{$report.post_id}</td>
                            <td title="{$report.post_title}" style="font-weight: 500;">
                                {if $report.post_id > 0 && $report.category_pinyin != ''}
                                <a href="../{$report.category_pinyin}/{$report.post_id}.html" target="_blank" style="color: #333; text-decoration: none; overflow: hidden; text-overflow: ellipsis; display: block;">
                                    {$report.post_title}
                                </a>
                                {else}
                                <span style="color: #999;">{$report.post_title}</span>
                                {/if}
                            </td>
                            <td>
                                {$report.type}
                            </td>
                            <td title="{$report.content}">
                                <div style="max-width: 250px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
                                    {$report.content}
                                </div>
                            </td>
                            <td>{$report.tel|default:'--'}</td>
                            <td>{$report.created_at}</td>
                            <td>
                                <a href="report.php?action=toggle_status&id={$report.id}" class="status-toggle" title="点击切换状态">
                                    {if $report.status == 0}
                                        <span class="badge badge-warning">未处理</span>
                                    {else}
                                        <span class="badge badge-success">已处理</span>
                                    {/if}
                                </a>
                            </td>
                            <td>
                                <div class="info-actions">
                                    <a href="report.php?action=detail&id={$report.id}" class="btn btn-sm btn-light-primary">详情</a>
                                    {if $report.post_id > 0}
                                    <a href="info.php?action=edit&id={$report.post_id}" target="_blank" class="btn btn-sm btn-light-info">查看信息</a>
                                    {/if}
                                    <a href="report.php?action=delete&id={$report.id}" class="btn btn-sm btn-light-danger" onclick="return confirm('确定要删除这条举报记录吗？')">删除</a>
                                </div>
                            </td>
                        </tr>
                        {/foreach}
                        {/if}
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <div style="margin-top: 20px; display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap;">
                <!-- 左侧全选和批量删除 -->
                <div style="margin-bottom: 15px;">
                    <button type="submit" id="batchDeleteBtn" class="btn btn-sm btn-light-danger" style="height: 32px; line-height: 1; padding: 0 12px;">批量删除</button>
                </div>
                
                <!-- 分页 -->
                <div style="flex: 1; text-align: right;">
                    {if $pagination.total_pages > 1}
                    <div>
                        <div class="simple-pagination" style="justify-content: flex-end;">
                            {if $pagination.current_page > 1}
                            <a href="{$pagination.previous_link}" class="pagination-btn">上一页</a>
                            {else}
                            <span class="pagination-btn disabled">上一页</span>
                            {/if}
                            
                            {foreach $pagination.page_links as $page => $link}
                            <a href="{$link}" class="pagination-btn {if $page == $pagination.current_page}active{/if}">{$page}</a>
                            {/foreach}
                            
                            {if $pagination.current_page < $pagination.total_pages}
                            <a href="{$pagination.next_link}" class="pagination-btn">下一页</a>
                            {else}
                            <span class="pagination-btn disabled">下一页</span>
                            {/if}
                            
                            <span style="margin-left: 10px; color: #6c757d; font-size: 14px;">共 {$pagination.total_pages} 页</span>
                        </div>
                    </div>
                    {/if}
                </div>
            </div>
        </form>
    </div>
</div>

{include file="footer.htm"} 

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 全选/取消全选
    var selectAll = document.getElementById('selectAll');
    var checkboxes = document.getElementsByClassName('report-checkbox');
    
    // 顶部全选按钮事件
    selectAll.addEventListener('change', function() {
        for (var i = 0; i < checkboxes.length; i++) {
            checkboxes[i].checked = selectAll.checked;
        }
    });
    
    // 当单个checkbox改变时，检查是否需要更新全选框状态
    for (var i = 0; i < checkboxes.length; i++) {
        checkboxes[i].addEventListener('change', function() {
            var allChecked = true;
            for (var j = 0; j < checkboxes.length; j++) {
                if (!checkboxes[j].checked) {
                    allChecked = false;
                    break;
                }
            }
            // 同步顶部全选按钮
            selectAll.checked = allChecked;
        });
    }
    
    // 批量删除提交前确认
    var reportForm = document.getElementById('reportForm');
    if (reportForm) {
        reportForm.addEventListener('submit', function(e) {
            var checkedBoxes = document.querySelectorAll('input[name="report_ids[]"]:checked');
            if (checkedBoxes.length === 0) {
                e.preventDefault();
                alert('请至少选择一条记录进行删除');
                return false;
            }
            
            if (!confirm('确定要删除选中的 ' + checkedBoxes.length + ' 条举报记录吗？')) {
                e.preventDefault();
                return false;
            }
            
            return true;
        });
    }
});
</script> 