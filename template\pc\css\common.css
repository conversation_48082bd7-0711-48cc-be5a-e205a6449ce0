@charset "utf-8";
/*全局样式*/
body{
    overflow: scroll;
    background-color: #F7F7F7; 
    /* background-image: url("../images/chun.gif"); */
    background-repeat: no-repeat;
    background-position: center top;
    background-attachment: fixed;
}
body,p,b,form,input,label,h1,h2,h3,h4,div,ul,li,dl,dt,dd,ol,button{margin:0;padding:0;}
body{font: 12px/26px Arial,Hiragino Sans GB,STHeiti,Helvetica Neue,Helvetica,Microsoft Yahei,WenQuanYi Micro Hei,sans-serif;color:#333;height:101%;word-break: break-word;}
ul,ol,li{list-style:none;}
em, cite{font-style:normal;}
img{border:none;vertical-align:top;}
a{text-decoration:none;color:#333;outline: none;-webkit-transition: all 0.1s}
a:hover{color:#f05a14;}
input{vertical-align:middle;}
.jiange{ padding: 0 30px;}
input[type=checkbox]{ margin-left: 10px; margin-right: 2px;}


.mt10{margin-top:10px;}
.mt20{margin-top:20px;}
.ml10{margin-left:10px;}
.mb10{margin-bottom: 10px;}
/* 加节日背景需要把pd10=0 */
.pd10{ padding: 10px;} 
.pd20{ padding: 20px;}
.pt10{ padding: 10px;}
.pl10{ padding-left: 10px;}

/* 直接子元素全部左浮动 */
.yui-c-box>div{ float: left;}

/* 定义背景 */
.color{color:#EE3131}
.bg{ background-color:darkgoldenrod}
.bg-blue{ background-color: blue;}
.bg-red{ background-color: red;}
.bg-white{ background-color: white;}
/* 浮动定义 */
.yui-left{ float: left;}
.yui-right{ float: right;}
.yui-clear{ clear: both;}

/* 文本定义 */
.yui-text-right{ text-align: right;}

.yui-xian{ border-bottom: 1px #ebebeb solid; height: 2px;}

/********************* 公共样式写在上面 *******************/

/* 头部 */
/* 加节日背景需要把左右width=1220 左右10px */
.yui-1200{ width: 1200px; margin:0 auto; /* padding: 0 10px;  background-color: white;*/ }

.yui-top{ 
    height: 36px; 
    background: #F8F8F8;
    line-height: 36px; 
    border-bottom: 1px solid rgba(229, 229, 229, 0.3);
    box-shadow: none;
}
.yui-top-left{ 
    width: 600px; 
    display: flex;
    align-items: center;
    padding-left: 5px;
}
.yui-top-left a {
    margin-right: 15px;
    font-size: 13px;
    color: #333;
    transition: all 0.3s;
    text-decoration: none;
    text-shadow: 0 1px 1px rgba(255,255,255,0.7);
}
.yui-top-left a:hover {
    color: #EE3131;
}
.yui-top-right { 
    width: 580px; 
    display: flex;
    align-items: center;
    justify-content: flex-end;
    height: 36px;
}
.yui-top-right a,
.yui-top-right .yui-top-dropdown {
    margin-left: 0;
    display: inline-block;
}
.yui-top-right a {
    font-size: 13px;
    color: #333;
    transition: all 0.3s;
    text-decoration: none;
    padding: 0 15px;
    line-height: 36px;
    display: inline-block;
    height: 36px;
    text-shadow: 0 1px 1px rgba(255,255,255,0.7);
}
.yui-top-right a:hover {
    background-color: rgba(255,255,255,0.8);
    color: #EE3131;
    text-shadow: none;
}

/* 顶部下拉菜单 */
.yui-top-dropdown {
    position: relative;
    display: inline-block;
    margin-left: 15px;
}

.yui-top-dropdown-btn {
    font-size: 13px;
    color: #333;
    cursor: pointer;
    padding: 0 15px;
    position: relative;
    transition: all 0.3s;
    height: 36px;
    display: inline-block;
    line-height: 36px;
    text-shadow: 0 1px 1px rgba(255,255,255,0.7);
}

.yui-top-dropdown-btn:after {
    content: '';
    display: inline-block;
    width: 0;
    height: 0;
    margin-left: 5px;
    vertical-align: middle;
    border-top: 4px solid #666;
    border-right: 4px solid transparent;
    border-left: 4px solid transparent;
    transition: all 0.3s;
}

.yui-top-dropdown:hover .yui-top-dropdown-btn {
    background-color: rgba(255,255,255,0.8);
    color: #EE3131;
    text-shadow: none;
}

.yui-top-dropdown:hover .yui-top-dropdown-btn:after {
    transform: rotate(180deg);
    border-top-color: #EE3131;
}

.yui-top-dropdown-menu {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    display: none;
    min-width: 120px;
    background-color: #fff;
    border: 1px solid #eee;
    border-radius: 4px;
    box-shadow: 0 6px 12px rgba(0,0,0,0.1);
}

.yui-top-right .yui-top-dropdown-menu {
    left: auto;
    right: 0;
    transform: none;
}

.yui-top-dropdown:hover .yui-top-dropdown-menu {
    display: block;
}

.yui-top-dropdown-menu li {
    line-height: 36px;
    border-bottom: 1px solid #f5f5f5;
}

.yui-top-dropdown-menu li:last-child {
    border-bottom: none;
}

.yui-top-dropdown-menu li a {
    display: block;
    padding: 0 15px;
    font-size: 13px;
    color: #666;
    white-space: nowrap;
}

.yui-top-dropdown-menu li a:hover {
    background-color: #f9f9f9;
    color: #EE3131;
}

/* 页面切换导航 */
.page-switch-nav {
    background: transparent;
    border-bottom: 2px solid #EE3131;
    padding: 10px 0;
    margin-bottom: 10px;
}
.page-switch-nav .yui-1200 {
    display: flex;
    align-items: center;
}
.page-switch-nav a {
    padding: 5px 20px;
    margin-right: 15px;
    font-size: 14px;
    color: #333;
    text-decoration: none;
    border-radius: 3px;
    transition: all 0.3s;
}
.page-switch-nav a:hover,
.page-switch-nav a.active {
    background: #EE3131;
    color: #fff;
    box-shadow: 0 2px 8px rgba(238, 49, 49, 0.2);
    transform: translateY(-1px);
}

/* 主区域 */ 
/* 加节日背景需要把左右pd=0 */
.yui-content{ padding: 0px; overflow: hidden;} 

/* .yui-content-bg{ background: url("../images/bg_reg.jpg") no-repeat;} */

/* 面包屑 */

.head_fenleilink{ height:25px; line-height:25px; margin:0px auto; margin-top:10px; margin-bottom:10px; font-size:13px;  color:#999999; position:relative;}
.head_fenleilink_nr{width:550px; height:25px; position:absolute; left:20px;}
.head_fenleilink_nr a{ color:#3366cc}
.head_fenleilink_ico{width:15px; height:16px; position:absolute; background:url(../images/headlink.gif) no-repeat; top:4px;}

/* logo row */
.yui-header{ 
    padding-top: 10px;
    background: white;
    overflow: hidden;

}
.yui-logo{ width: 217px; height: 60px;}
.yui-logo img { margin-top: 15px;}
.yui-cimg{ margin-left: 10px; width: 203px; height: 79px; overflow: hidden; background:url(../images/ctip.png) 10px 5px no-repeat;;}
.yui-title{ margin-left: 30px; line-height: 60px; font-size: 22px;  border-left: 1px solid #f2f2f2; padding-left: 30px; margin-top: 20px;}
.yui-fh-index{ line-height: 90px; margin-right: 30px; font-size: 16px;}
/* .yui-bottom-boder{ border-bottom: 1px solid #d6d6d6;} */
/* form-seleft */
.yui-form{
    height: 80px;
    margin-left: 20px;
    width: 560px;
    position: relative;
}
.yui-select{
    position: relative;
    width: 530px;
    height: 40px;
    margin-top: 20px;
    border: 2px solid #3092d5;
    border-radius: 3px;
    display: flex;
    align-items: center;
}
.mod_select{
    position: relative;
    width: 80px;
    height: 40px;
    border-right: 1px solid #f2f2f2;
    background: #fff;
    flex-shrink: 0;
    z-index: 1050;
}
.mod_select .select_box{
    position: relative;
    width: 80px;
    height: 40px;
    cursor: pointer;
}
.mod_select .select_box .select_txt{
    display: flex;
    width: 80px;
    height: 40px;
    line-height: 40px;
    font-size: 14px;
    color: #333;
    justify-content: flex-start;
    align-items: center;
    padding-left: 15px;
    padding-right: 15px;
}
.mod_select .select_box .select_txt:after{
    content:'';
    position:absolute;
    right:8px;
    top:50%;
    margin-top:-2px;
    width:0;
    height:0;
    border-style:solid;
    border-width:5px 5px 0 5px;
    border-color:#999 transparent transparent transparent;
    transition:all 0.3s;
}
.mod_select .select_box:hover .select_txt:after{
    transform:rotate(180deg);
}
.mod_select .select_box:hover .option,
.mod_select .select_box:focus .option,
.mod_select .select_box:active .option{
    display: block !important;
}
.mod_select .select_box .option{
    display: none;
    position: absolute;
    top: 40px;
    left: -2px;
    width: 80px;
    background-color: #fff;
    border: 2px solid #EE3131;
    border-top: none;
    z-index: 1050;
}
.mod_select .select_box .option li{
    padding: 0;
    font-size: 14px;
    line-height: 36px;
    cursor: pointer;
    color: #666;
    text-align: center;
}
.mod_select .select_box .option li:hover{
    background-color: #f5f5f5;
    color: #EE3131;
}
.yui-select .import{
    width: 346px;
    height: 40px;
    border: none;
    outline: none;
    font-size: 14px;
    background: #fff;
    padding: 0 10px;
}
.yui-select .btn-search{
    position: absolute;
    right: 0;
    top: 0;
    width: 100px;
    height: 40px;
    color: #fff;
    background-color: #3092d5;
    border: 0;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
}
.yui-select .btn-search:hover{
    background-color: #2980b9;
}
.yui-select .btn-search:disabled{
    background-color: #6c757d !important;
    cursor: not-allowed !important;
}
/* 热门搜索词 */
.yui-select-bottom-text{line-height: 30px;}
.yui-select-bottom-text>span{ margin-right: 20px;}

/* 发布按钮 */
.yui-fabu{ width: 160px; height: 80px; float: right; margin-right: 5px;}
.yui-fabu button{ 
    background-color: #ff5722; 
    border: none; 
    width: 160px; 
    height: 43px; 
    margin-top: 20px; 
    color: #FFF; 
    font-size: 14px;
    border-radius: 3px;
}
.yui-fabu button:hover{
    background-color: #ff7043;
}
.yui-fabu button a{ 
    color: #FFF;
    display: block;
    width: 100%;
    height: 100%;
    line-height: 43px;
    text-decoration: none;
}
/* 主导航 */
.yui-nav{height:45px;line-height:45px;background-color:#3092d5;border-radius:2px; margin-top: 10px;}
.yui-nav ul li{float:left;line-height:45px;height:45px;position:relative;}
.yui-nav ul li a{display:block;height:45px;line-height:45px;text-align:center;color:#fff;text-decoration:none;font-size:16px;padding:0 15px;}
.yui-nav ul li dl{position:absolute;left:0;top:45px;z-index: 999; width:139px;background-color:#fff;display:none;color:#666;box-shadow:0 3px 8px #666;border-radius:0 0 4px 4px;overflow:hidden;}
.yui-nav ul li dl dd{height:40px;}
.yui-nav ul li dl dd a{display:block;height:40px;line-height:40px;text-align:center;color:#666;border-bottom:1px solid #f2f2f2;}
.yui-nav ul li dl dd a:hover{background:#f2f2f2;color:#666;}
.yui-nav ul li:hover{background:#247BB5;transition:all 0.3s ease;}
.yui-nav ul li:hover > a{color:#fff !important;}
.yui-nav .nav-cur{background:#247BB5;}
.yui-nav .nav-cur > a{color:#fff;}

/* 导航条下广告条 */
.mo-box{ margin-top: 10px;}
.mo-box a{ margin-bottom: 6px; display: block;}
.mo-box img{ width: 1200px;}

/* 首页第一屏 */

/* h3 板块标题 */
.yui-h-title { 
    padding: 10px;
    margin-bottom: 0;
    border-bottom: 2px solid #f5f5f5;
    line-height: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}
.yui-h-title:after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 50px;
    height: 2px;
    background: #EE3131;
    transition: width 0.3s ease;
}
.yui-h-title:hover:after {
    width: 80px;
}
.yui-h-title h3 {
    font-size: 16px;
    color: #333;
    margin: 0;
    font-weight: 600;
    position: relative;
    padding-left: 10px;
}
.yui-h-title h3:before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 16px;
    background: #EE3131;
    border-radius: 2px;
}
.yui-h-title span { 
    font-size: 12px;
}
.yui-h-title span a { 
    color: #999;
    transition: all 0.3s ease;
    padding: 4px 8px;
    border-radius: 3px;
}
.yui-h-title span a:hover { 
    color: #EE3131;
    background: #fff5f5;
}
.yui-small-list ul{ display: block;  margin-top: 10px;}
.yui-small-list ul li{ line-height: 30px; margin-left: 5px;overflow: hidden; height: 30px; text-overflow: ellipsis; /* 添加省略号 */}


.yui-small-list ul li a{ font-size: 14px; color: #266392;}
/* h3 板块标题样式二 */
.yui-h-title2{/* border-bottom: 1px solid #EE3131; */height: 35px; line-height: 35px;/* padding-left: 5px; */}
/* .yui-h-title2 ul li{ float: left; height: 33px; padding: 0 10px; font-size: 16px; border: 1px solid #EE3131; border-bottom: 2px solid #fff;color: #EE3131; margin-right: 5px;} */
.yui-h-title2 ul li{ float: left; height: 33px; /* padding: 0 10px; */font-size: 16px; /* border: 1px solid #EE3131; border-bottom: 2px solid #fff;color: #EE3131; */ margin-right: 5px; font-weight: 700;}

/* 发布条例与提示 */
.infoquote { margin: 10px 0px;  padding:8px 13px; background: rgb(248, 245, 218); border: 1px solid rgb(255, 198, 134) ; overflow: hidden; font-size: 14px;line-height: 25px; color: #888;}
.infoquote p{color: #EE3131;}
.infoquote a{ color: blue;}
.fabu_tiaoli { height: 60px;font-size: 13px; line-height: 20px; padding-left: 15px;padding-top: 10px; padding-bottom: 10px;color: rgb(0, 0, 0);margin: 10px 30px; border-width: 1px; border-style: solid;  border-color: rgb(255, 198, 134);border-image: initial; background: rgb(248, 245, 218);}
.notice{ height: auto!important; overflow: hidden;border: 1px solid rgb(255, 198, 134) ;background: rgb(248, 245, 218); padding: 10px; color: #333;}



.aboutMain{text-align:left;overflow:hidden;}
.aboutMainl{float:left;width:190px;height:auto;background-color:#FFF;padding:0;}
.aboutMainl ul li{float:left;width:190px;height:50px;line-height:50px;text-align:center;border-bottom:1px #EEE solid;font-size:16px;}
.aboutMainr{float:right;width:968px;height:auto;background-color:#FFF;padding:0 15px 15px;}
.aboutMainrt{float:left;width:900px;height:50px;line-height:50px;border-bottom:1px #EEE solid;font-weight:700;font-size: 16px; color: #EE3131;display: inline;}
.aboutMainrc{min-height:500px;overflow:hidden;float:left;height:auto;line-height:30px;padding-top:10px;color:#333;}
.aboutMainrc p{font-size:14px;color:#333;}
.aboutMainl ul .aboutOver{font-weight:700;border-top:1px #EEE solid;border-bottom:1px #EEE solid;font-size:16px;}
.aboutMainl ul .aboutOver a{color:#EE3131;}


.yui-dy{  background: white; border-radius: 5px; line-height: 30px; overflow: hidden;font-size:15px; }
.yui-dy h1{font-size: 20px; line-height: 35px; text-align: center; margin-bottom: 20px; margin-top: 20px;}
.yui-dy-qt{ text-align: center; border-bottom: 1px #f2f2f2 solid; height: 30px; padding-bottom: 10px; margin-bottom: 10px;}
.yui-dy-qt span{ margin-right: 30px; font-size: 14px; color: #888;}
.yui-dy-next{ color: #888; height: 50px; margin-top: 30px;}
.yui-dy-next a{color: #266392;}

/* footer 美化 */
.yui-footer {
    margin-top: 10px;
    padding: 20px 0;
    background-color: #f7f7f7;
}

.yui-footer .yui-1200 {
    border-top: 2px solid #EE3131;
}

.yui-footer .footer-content {
    padding: 20px;
    border-radius: 5px;
    width: 100%;
    box-sizing: border-box;
    margin: 0 auto;
}

.yui-footer .footer-nav {
    text-align: center;
    margin-bottom: 15px;
    display: flex;
    justify-content: center;
}

.yui-footer p {
    text-align: center; 
    line-height: 20px;
    margin-bottom: 6px;
    font-size: 12px;
    color: #666;
    text-shadow: none;
}

.yui-footer .footer-nav a {
    margin: 0 15px;
    color: #555;
    font-size: 13px;
    position: relative;
    font-weight: 500;
    padding: 3px 5px;
    border-radius: 3px;
    transition: all 0.3s ease;
}

.yui-footer .footer-nav a:hover {
    color: #EE3131;
    background-color: transparent;
    text-decoration: none;
}

.yui-footer .footer-nav a:not(:last-child):after {
    content: '|';
    position: absolute;
    right: -15px;
    color: #ddd;
}

.yui-footer .footer-disclaimer {
    line-height: 18px;
    color: #999;
    margin-bottom: 8px;
}

.yui-footer .footer-copyright {
    margin-top: 8px;
    color: #999;
    line-height: 18px;
}

.yui-footer p:last-child {
    margin-bottom: 0;
}

.yui-footer p a { 
    margin: 0 8px;
    color: #666;
    text-decoration: none;
    transition: all 0.3s;
    display: inline-block;
    position: relative;
    text-shadow: none;
    font-size: 12px;
}



.yui-footer p a:hover {
    color: #EE3131;
}

.yui-footer p a.db_link {
    font-weight: bold;
    color: #555;
}

/* 特殊链接样式 */
.yui-footer p a[rel="nofollow"] {
    margin: 0 5px;
    color: #888;
}

.yui-footer p a[rel="nofollow"]:hover {
    color: #EE3131;
}

.yui-footer p a[rel="nofollow"]:after {
    content: none;
}

/* 友情链接样式 */
.friend-links {
    margin-bottom: 20px;
    padding: 15px 0;
    border-bottom: 1px solid #eee;
}

.friend-links-title {
    display: inline-block;
    font-size: 13px;
    color: #666;
    font-weight: 500;
    margin-right: 10px;
    vertical-align: top;
    line-height: 26px;
}

.friend-links-list {
    display: inline-block;
    width: calc(100% - 80px);
    line-height: 26px;
}

.friend-links-list a {
    display: inline-block;
    margin: 0 15px 5px 0;
    color: #555;
    font-size: 12px;
    text-decoration: none;
    padding: 2px 8px;
    border-radius: 3px;
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.friend-links-list a:hover {
    color: #EE3131;
    background-color: #f8f8f8;
    border-color: #EE3131;
    text-decoration: none;
}

/* 响应式友情链接 */
@media (max-width: 768px) {
    .friend-links-title {
        display: block;
        margin-bottom: 8px;
        margin-right: 0;
    }

    .friend-links-list {
        display: block;
        width: 100%;
    }

    .friend-links-list a {
        margin: 0 10px 5px 0;
        font-size: 11px;
        padding: 1px 6px;
    }
}

 /* ========== 简洁页面模板样式（可在其他页面复用）开始 ========== */
        /* 简洁头部样式 */
        .simple-header {
            width: 1180px;
            margin: 0 auto;
            background-color: #fff;
            padding:10px;
            margin-bottom: 10px;
           
        }
    

        .simple-header-inner {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .simple-logo {
            display: flex;
            align-items: center;
        }

        .simple-logo img {
            height: 45px;
            margin-right: 15px;
        }

        .logo-title-group {
            display: flex;
            align-items: center;
        }

        .title-separator {
            height: 24px;
            width: 1px;
            background-color: #ddd;
            margin: 0 15px;
            display: inline-block;
        }

        .simple-title {
            font-size: 20px;
            color: #555;
            font-weight: normal;
            margin: 0;
        }

        .simple-back {
            font-size: 14px;
            color: #555;
            text-decoration: none;
        }

        .simple-back:hover {
            color: #EE3131;
        }

        /* 简洁内容区域 */
        .simple-content {
            width: 1200px;
            margin: 20px auto;
            background: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
            min-height: 400px;
        }

        /* 简洁页脚样式 */
        .simple-footer {
            width: 1200px;
            margin: 20px auto;
            background-color: #fff;
            padding: 20px 0;
            border-top: 1px solid #eee;
        }

        .simple-footer-inner {
            text-align: center;
        }

        .simple-footer-links {
            margin-bottom: 10px;
        }

        .simple-footer-links a {
            color: #555;
            text-decoration: none;
            margin: 0 10px;
            font-size: 14px;
        }

        .simple-footer-links a:hover {
            color: #EE3131;
        }

        .simple-footer-copyright {
            color: #999;
            font-size: 12px;
            line-height: 1.6;
        }
        /* ========== 简洁页面模板样式（可在其他页面复用）结束 ========== */

/* 暂无信息样式 */
.no-info-message {
    padding: 60px 0;
    margin: 20px 0;
    text-align: center;
}

.no-info-icon {
    display: block;
    width: 48px;
    height: 48px;
    margin: 0 auto 15px;
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23ccc"><path d="M20 6h-4V4c0-1.1-.9-2-2-2h-4c-1.1 0-2 .9-2 2v2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2zM10 4h4v2h-4V4zm10 16H4V8h16v12z"/><path d="M12 17.5a5.5 5.5 0 1 1 0-11 5.5 5.5 0 0 1 0 11zm0-9.5a4 4 0 1 0 0 8 4 4 0 0 0 0-8z"/></svg>') no-repeat center;
}

.no-info-message p {
    color: #999;
    font-size: 14px;
    margin: 0;
}