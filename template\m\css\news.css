/* 移动端新闻样式 */

/* 面包屑导航 */
.breadcrumb {
    background: #fff;
    border-bottom: 1px solid #eee;
    padding: 10px 0;
    font-size: 13px;
}

.breadcrumb .container {
    padding: 0 15px;
}

.breadcrumb a {
    color: #666;
    text-decoration: none;
}

.breadcrumb a:hover {
    color: #007bff;
}

.breadcrumb .separator {
    margin: 0 8px;
    color: #ccc;
}

.breadcrumb .current {
    color: #333;
}

/* 新闻分类导航 */
.news-nav {
    background: #fff;
    border-bottom: 1px solid #eee;
    padding: 10px 0;
}

.news-nav .container {
    padding: 0 15px;
}

.nav-scroll {
    display: flex;
    overflow-x: auto;
    white-space: nowrap;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.nav-scroll::-webkit-scrollbar {
    display: none;
}

.nav-scroll a {
    display: inline-block;
    padding: 8px 16px;
    margin-right: 10px;
    background: #f8f9fa;
    color: #666;
    text-decoration: none;
    border-radius: 20px;
    font-size: 14px;
    white-space: nowrap;
}

.nav-scroll a:hover,
.nav-scroll a.active {
    background: #007bff;
    color: #fff;
}

/* 新闻列表 */
.news-list {
    background: #f5f5f5;
    padding: 10px 0;
}

.news-list .container {
    padding: 0 15px;
}

.news-item {
    background: #fff;
    margin-bottom: 10px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    display: flex;
    padding: 15px;
}

/* 简化新闻列表项 */
.news-item-simple {
    background: #fff;
    margin-bottom: 1px;
    border-bottom: 1px solid #f0f0f0;
}

.news-item-simple:last-child {
    border-bottom: none;
}

.news-row {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    justify-content: space-between;
}

.news-title-simple {
    flex: 1;
    margin-right: 15px;
    min-width: 0;
    display: flex;
    align-items: flex-start;
}

.news-title-simple a {
    color: #333;
    text-decoration: none;
    font-size: 15px;
    line-height: 1.4;
    flex: 1;
    min-width: 0;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-break: break-all;
}

.news-title-simple a:hover {
    color: #007bff;
}

.news-tags {
    display: flex;
    flex-shrink: 0;
    margin-left: 6px;
    align-items: flex-start;
    gap: 4px;
}

.news-title-simple .tag {
    display: inline-block;
    padding: 2px 6px;
    font-size: 11px;
    border-radius: 3px;
    white-space: nowrap;
    flex-shrink: 0;
}

.news-title-simple .tag.top {
    background: #ff4757;
    color: #fff;
}

.news-title-simple .tag.rec {
    background: #ffa502;
    color: #fff;
}

.news-time-simple {
    color: #999;
    font-size: 12px;
    white-space: nowrap;
    flex-shrink: 0;
}

.news-thumb {
    width: 100px;
    height: 75px;
    margin-right: 15px;
    flex-shrink: 0;
    border-radius: 6px;
    overflow: hidden;
}

.news-thumb img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.news-content {
    flex: 1;
    min-width: 0;
}

.news-content.no-thumb {
    margin-left: 0;
}

.news-title {
    font-size: 16px;
    font-weight: 500;
    line-height: 1.4;
    margin-bottom: 8px;
    display: flex;
    align-items: flex-start;
}

.news-title a {
    color: #333;
    text-decoration: none;
    flex: 1;
    min-width: 0;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.news-title a:hover {
    color: #007bff;
}

.news-title .tag {
    display: inline-block;
    padding: 2px 6px;
    font-size: 11px;
    border-radius: 3px;
    margin-left: 6px;
    vertical-align: top;
}

.news-title .tag.top {
    background: #ff4757;
    color: #fff;
}

.news-title .tag.rec {
    background: #ffa502;
    color: #fff;
}

.news-desc {
    color: #666;
    font-size: 14px;
    line-height: 1.4;
    margin-bottom: 8px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.news-meta {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #999;
    flex-wrap: wrap;
}

.news-meta span {
    margin-right: 15px;
    display: flex;
    align-items: center;
}

.news-meta i {
    margin-right: 4px;
    font-size: 11px;
}

/* 空列表 */
.empty-list {
    text-align: center;
    padding: 60px 20px;
    background: #fff;
    border-radius: 8px;
}

.empty-icon {
    font-size: 48px;
    color: #ddd;
    margin-bottom: 15px;
}

.empty-text {
    color: #999;
    font-size: 14px;
}

/* 分页 */
.pagination {
    padding: 20px 0;
}

.pagination .container {
    padding: 0 15px;
}

/* 新闻详情页样式 */
.news-detail {
    background: #fff;
    padding: 20px 0;
}

.news-detail .container {
    padding: 0 15px;
}

.news-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.news-detail .news-title {
    font-size: 20px;
    font-weight: 600;
    line-height: 1.4;
    color: #333;
    margin-bottom: 15px;
}

.news-detail .news-meta {
    display: flex;
    flex-wrap: wrap;
    font-size: 13px;
    color: #666;
}

.news-detail .news-meta span {
    margin-right: 20px;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
}

.news-detail .news-meta i {
    margin-right: 5px;
    color: #999;
}

.news-summary {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 20px;
    border-left: 4px solid #007bff;
}

.news-summary p {
    color: #666;
    font-size: 15px;
    line-height: 1.6;
    margin: 0;
}

.news-content {
    font-size: 16px;
    line-height: 1.8;
    color: #333;
    margin-bottom: 30px;
}

.news-actions {
    display: flex;
    justify-content: center;
    padding: 20px 0;
    border-top: 1px solid #eee;
    border-bottom: 1px solid #eee;
    margin-bottom: 20px;
}

.action-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px 20px;
    cursor: pointer;
}

.action-item:hover {
    color: #007bff;
}

.action-item i {
    font-size: 20px;
    margin-bottom: 5px;
}

.action-item span {
    font-size: 12px;
}

/* 相关新闻 */
.related-news {
    background: #f8f9fa;
    padding: 20px 0;
}

.related-news .container {
    padding: 0 15px;
}

.section-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
    padding-left: 10px;
    border-left: 4px solid #007bff;
}

.related-list {
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
}

.related-item {
    border-bottom: 1px solid #eee;
}

.related-item:last-child {
    border-bottom: none;
}

.related-item a {
    display: block;
    padding: 15px;
    text-decoration: none;
    color: #333;
}

.related-item a:hover {
    background: #f8f9fa;
}

.related-title {
    font-size: 15px;
    line-height: 1.4;
    margin-bottom: 5px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.related-time {
    font-size: 12px;
    color: #999;
}

/* 新闻首页样式 */
.hot-news {
    background: #fff;
    margin-bottom: 10px;
}

.hot-news .container {
    padding: 15px;
}

.section-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
}

.section-title i {
    margin-right: 8px;
    color: #007bff;
}

.hot-list {
    display: flex;
    flex-direction: column;
}

.hot-item {
    display: flex;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
}

.hot-item:last-child {
    border-bottom: none;
}

.hot-thumb {
    width: 80px;
    height: 60px;
    margin-right: 12px;
    flex-shrink: 0;
    border-radius: 4px;
    overflow: hidden;
}

.hot-thumb img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.hot-content {
    flex: 1;
    min-width: 0;
}

.hot-content.no-thumb {
    margin-left: 0;
}

.hot-title {
    font-size: 15px;
    font-weight: 500;
    line-height: 1.3;
    margin-bottom: 6px;
    display: flex;
    align-items: flex-start;
}

.hot-title a {
    color: #333;
    text-decoration: none;
    flex: 1;
    min-width: 0;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.hot-title a:hover {
    color: #007bff;
}

.hot-desc {
    color: #666;
    font-size: 13px;
    line-height: 1.3;
    margin-bottom: 6px;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.hot-meta {
    display: flex;
    align-items: center;
    font-size: 11px;
    color: #999;
}

.hot-meta span {
    margin-right: 12px;
    display: flex;
    align-items: center;
}

.hot-meta i {
    margin-right: 3px;
}

.latest-news {
    background: #f5f5f5;
    padding: 10px 0;
}

.latest-news .container {
    padding: 0 15px;
}

.latest-news .section-title {
    background: #fff;
    margin: 0 -15px 10px;
    padding: 15px;
    border-radius: 0;
}

.more-news {
    text-align: center;
    padding: 20px 0;
}

.btn-more {
    display: inline-flex;
    align-items: center;
    padding: 10px 20px;
    background: #007bff;
    color: #fff;
    text-decoration: none;
    border-radius: 20px;
    font-size: 14px;
}

.btn-more:hover {
    background: #0056b3;
}

.btn-more i {
    margin-left: 6px;
    font-size: 12px;
}

.empty-news {
    background: #fff;
    padding: 60px 0;
}

.empty-news .container {
    padding: 0 15px;
    text-align: center;
}

/* 响应式调整 */
@media (max-width: 480px) {
    .news-item {
        padding: 12px;
    }

    .news-thumb {
        width: 80px;
        height: 60px;
        margin-right: 12px;
    }

    .news-title {
        font-size: 15px;
    }

    .news-desc {
        font-size: 13px;
    }

    .news-detail .news-title {
        font-size: 18px;
    }

    .news-content {
        font-size: 15px;
    }

    .hot-thumb {
        width: 70px;
        height: 50px;
        margin-right: 10px;
    }

    .hot-title {
        font-size: 14px;
    }

    .hot-desc {
        font-size: 12px;
    }
}
