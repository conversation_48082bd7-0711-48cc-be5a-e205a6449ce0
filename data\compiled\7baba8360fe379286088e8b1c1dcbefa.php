<?php if(null !== ($posts ?? null) && !empty($posts)): ?>
    <?php if(null !== ($posts ?? null) && is_array($posts)): foreach($posts as $post): ?>
    <div class="post-item <?php if(null !== ($post ?? null) && is_array($post) && array_key_exists('is_top_category', $post) && !empty($post['is_top_category']) || null !== ($post ?? null) && is_array($post) && array_key_exists('is_top_subcategory', $post) && !empty($post['is_top_subcategory'])): ?>is-top<?php endif; ?>">
        <div class="post-image">
            <?php if(null !== ($post ?? null) && is_array($post) && array_key_exists('image_url', $post) && !empty($post['image_url'])): ?>
            <img src="<?php echo (isset($post['image_url'])) ? $post['image_url'] : ""; ?>" alt="<?php echo (isset($post['title'])) ? $post['title'] : ""; ?>">
            <?php else: ?>
            <img src="/static/images/no-image.png" alt="无图片">
            <?php endif; ?>
        </div>
        <div class="post-content">
            <?php 
            $days = getRemainingDaysInt($post['expired_at']);
            if (!empty($post['expired_at']) && $days <= 0):
            ?>
            <a href="/<?php echo (isset($category['pinyin'])) ? $category['pinyin'] : ""; ?>/<?php echo (isset($post['id'])) ? $post['id'] : ""; ?>.html" class="post-title post-expired">
                <?php if(null !== ($post ?? null) && is_array($post) && array_key_exists('is_top_category', $post) && !empty($post['is_top_category']) || null !== ($post ?? null) && is_array($post) && array_key_exists('is_top_subcategory', $post) && !empty($post['is_top_subcategory'])): ?><span class="top-tag">顶</span><?php endif; ?>
                <?php echo (isset($post['title'])) ? $post['title'] : ""; ?>
            </a>
            <?php else: ?>
            <a href="/<?php echo (isset($category['pinyin'])) ? $category['pinyin'] : ""; ?>/<?php echo (isset($post['id'])) ? $post['id'] : ""; ?>.html" class="post-title">
                <?php if(null !== ($post ?? null) && is_array($post) && array_key_exists('is_top_category', $post) && !empty($post['is_top_category']) || null !== ($post ?? null) && is_array($post) && array_key_exists('is_top_subcategory', $post) && !empty($post['is_top_subcategory'])): ?><span class="top-tag">顶</span><?php endif; ?>
                <?php echo (isset($post['title'])) ? $post['title'] : ""; ?>
            </a>
            <?php endif; ?>
            <div class="post-info">
                <div class="post-meta">
                    <?php if(null !== ($post ?? null) && is_array($post) && array_key_exists('region_name', $post) && !empty($post['region_name'])): ?>
                    <div class="post-region"><?php echo (isset($post['region_name'])) ? $post['region_name'] : ""; ?></div>
                    <?php endif; ?>
                    <div class="post-expire"><?php 
                        if (!empty($post['expired_at'])) {
                            $days = getRemainingDaysInt($post['expired_at']);
                            echo $days > 0 ? '剩余'.$days.'天' : '已过期';
                        } else {
                            echo '长期';
                        }
                    ?></div>
                </div>
                <div class="post-time"><?php echo null !== ((null !== ($post ?? null)) ? ($post['updated_at']) : null) ? friendlyTime((null !== ($post ?? null)) ? ($post['updated_at']) : null) : ""; ?></div>
            </div>
        </div>
    </div>
    <?php endforeach; endif; ?>
<?php endif; ?>
