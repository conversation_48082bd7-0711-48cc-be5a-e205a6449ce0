<?php
/**
 * 信息详情页面 - 性能优化版
 */

// 定义安全常量
define('IN_BTMPS', true);

// 引入公共文件
require_once './include/common.inc.php';

// 获取帖子ID
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;

// 如果ID不存在，显示404
if (!$id) {
    header("HTTP/1.0 404 Not Found");
    include_404_page();
    exit;
}

// 获取缓存设置
$cache_enable = isset($config['cache_enable']) ? intval($config['cache_enable']) : 1;
$cache_post_time = isset($config['cache_post']) ? intval($config['cache_post']) : 1800; // 详情页缓存时间从配置读取

// 尝试从缓存获取信息详情（区分PC版和手机版）
$template_suffix = TEMPLATE_DIR; // pc, m, wx, app
$cache_key = "detail_{$id}_{$template_suffix}";

$cached_data = false;
if ($cache_enable && $cache_post_time > 0) {
    $cached_data = cache_get($cache_key);
}

if ($cached_data !== false) {
    // 从缓存中获取数据
    $post = $cached_data['post'];
    $images = $cached_data['images'];
    $relatedPosts = $cached_data['related_posts'];
    $categoryInfo = $cached_data['category_info'];
} else {
    // 缓存不存在，从数据库获取
    $post = getPostDetail($id);

    // 如果帖子不存在或已删除，显示404
    if (!$post) {
        header("HTTP/1.0 404 Not Found");
        include_404_page();
        exit;
    }

    // 获取信息图片（使用缓存）
    $images = getCachedPostImages($id);

    // 获取相关信息（使用缓存）
    $relatedPosts = getCachedRelatedPosts($post['category_id'], $id);

    // 获取分类信息（使用全局缓存）
    $categoryInfo = getCategoryInfo($post['category_id']);

    // 如果启用缓存，将数据缓存起来（不包含浏览次数）
    if ($cache_enable && $cache_post_time > 0) {
        $cache_data = array(
            'post' => $post,
            'images' => $images,
            'related_posts' => $relatedPosts,
            'category_info' => $categoryInfo,
            'cached_at' => time()
        );
        cache_set($cache_key, $cache_data, $cache_post_time);
    }
}

// 异步更新浏览次数（不影响页面加载速度）
updateViewCountAsync($id);

// 实时获取当前浏览次数（单独查询，不缓存）
$current_view_count = getCurrentViewCount($id);
$post['view_count'] = $current_view_count;

// 获取是否已过期
$post['is_expired'] = isset($post['expired_at']) && $post['expired_at'] > 0 && $post['expired_at'] <= time();

// 处理文章内容中的换行
if (isset($post['content'])) {
    $post['content'] = nl2br($post['content']);
}

// 获取网站名称和URL
$site_name = isset($config['site_name']) ? $config['site_name'] : '分类信息网';
$site_url = isset($__template_vars['site_url']) ? $__template_vars['site_url'] : 'http://localhost';

// 添加SEO优化数据
$post['meta_title'] = $post['title'] . ' - ' . ($categoryInfo['name'] ?? '') . ' - ' . $site_name;
$post['meta_description'] = mb_substr(strip_tags($post['content'] ?? $post['description'] ?? ''), 0, 160);
$post['meta_keywords'] = $post['title'] . ',' . ($categoryInfo['name'] ?? '') . ',' . $site_name;

// 添加结构化数据（JSON-LD）
$structured_data = array(
    '@context' => 'https://schema.org',
    '@type' => 'Article',
    'headline' => $post['title'],
    'description' => $post['meta_description'],
    'datePublished' => date('c', $post['created_at']),
    'dateModified' => date('c', $post['updated_at']),
    'author' => array(
        '@type' => 'Person',
        'name' => $post['contact_name'] ?? '匿名用户'
    ),
    'publisher' => array(
        '@type' => 'Organization',
        'name' => $site_name,
        'url' => $site_url
    )
);

// 如果有图片，添加到结构化数据
if (!empty($images) && is_array($images)) {
    $structured_data['image'] = array();
    foreach ($images as $image) {
        if (!empty($image['thumb_path'])) {
            $image_url = $image['thumb_path'];
            if (substr($image_url, 0, 1) !== '/') {
                $image_url = '/' . $image_url;
            }
            $structured_data['image'][] = $site_url . $image_url;
        }
    }
}

// 分配变量到模板
assign('post', $post);
assign('images', $images);
assign('related_posts', $relatedPosts);
assign('category_info', $categoryInfo);
assign('structured_data', json_encode($structured_data, JSON_UNESCAPED_UNICODE));
assign('current_page', 'view');

// 确定使用哪个模板
$template_file = 'view.htm'; // 默认模板

// 检查当前终端类型
$isMobile = (TEMPLATE_DIR === 'm');

// 如果分类设置了自定义详情模板，则尝试使用
if ($categoryInfo && !empty($categoryInfo['detail_template'])) {
    // 根据当前终端类型确定模板路径
    $template_path = TEMPLATE_PATH . TEMPLATE_DIR . '/' . $categoryInfo['detail_template'];

    // 检查模板文件是否存在
    if (file_exists($template_path)) {
        // 模板文件存在，使用自定义模板
        $template_file = $categoryInfo['detail_template'];
    } else {
        // 自定义模板文件不存在，使用默认模板
        // 记录日志，便于排查问题（可选）
        error_log("详情模板文件不存在: " . $template_path . "，使用默认模板");
    }
}

// 使用当前终端类型显示模板
display($template_file, $isMobile);

