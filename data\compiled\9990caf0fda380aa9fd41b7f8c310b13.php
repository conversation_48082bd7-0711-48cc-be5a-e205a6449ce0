<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><?php echo (isset($post['title'])) ? $post['title'] : ""; ?> - <?php echo $site_name ?? ""; ?></title>
    <meta name="keywords" content="<?php echo (isset($post['title'])) ? $post['title'] : ""; ?>">
    <meta name="description" content="{$post.content|strip_tags|mb_substr:0:150}">
    <link rel="stylesheet" href="/template/pc/css/common.css">
    <link rel="stylesheet" href="/template/pc/css/view.css?v=1.0.1">
    <script type="text/javascript" src="/template/pc/js/m.js"></script>
 
    <style>
    /* 联系方式区域样式 */
    .contact-info {
        margin: 15px 0;
        border-top: 1px solid #e0e0e0;
        padding-top: 15px;
    }

    .contact-item {
        margin-bottom: 8px;
        line-height: 24px;
        font-size: 14px;
    }

    .contact-label {
        display: inline-block;
        width: 60px;
        color: #666;
    }

    .contact-value {
        color: #333;
        font-weight: bold;
    }

    .expired-text {
        color: #ff4d4f;
        font-weight: normal;
    }

    /* 电话号码图片样式 */
    .phone-image {
        height: auto;
        max-height: 28px;
        vertical-align: middle;
        margin-left: 0;
    }

    /* 联系认证标签 */
    .contact-verify {
        margin: 10px 0;
    }

    .verify-tag {
        display: inline-block;
        margin-right: 10px;
        font-size: 12px;
        color: #666;
    }

    .verify-tag img {
        vertical-align: middle;
        margin-right: 5px;
        width: 16px;
        height: 16px;
    }

    .user-icon {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 448 512'%3E%3Cpath fill='%23666' d='M224 256c70.7 0 128-57.3 128-128S294.7 0 224 0 96 57.3 96 128s57.3 128 128 128zm89.6 32h-16.7c-22.2 10.2-46.9 16-72.9 16s-50.6-5.8-72.9-16h-16.7C60.2 288 0 348.2 0 422.4V464c0 26.5 21.5 48 48 48h352c26.5 0 48-21.5 48-48v-41.6c0-74.2-60.2-134.4-134.4-134.4z'%3E%3C/path%3E%3C/svg%3E");
    }

    .phone-icon {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'%3E%3Cpath fill='%23666' d='M493.4 24.6l-104-24c-11.3-2.6-22.9 3.3-27.5 13.9l-48 112c-4.2 9.8-1.4 21.3 6.9 28l60.6 49.6c-36 76.7-98.9 140.5-177.2 177.2l-49.6-60.6c-6.8-8.3-18.2-11.1-28-6.9l-112 48C3.9 366.5-2 378.1.6 389.4l24 104C27.1 504.2 36.7 512 48 512c256.1 0 464-207.5 464-464 0-11.2-7.7-20.9-18.6-23.4z'%3E%3C/path%3E%3C/svg%3E");
    }

    .wechat-icon {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 576 512'%3E%3Cpath fill='%23666' d='M385.2 167.6c6.4 0 12.6.3 18.8 1.1C387.4 90.3 303.3 32 207.7 32 100.5 32 13 104.8 13 197.4c0 53.4 29.3 97.5 77.9 131.6l-19.3 58.6 68-34.1c24.4 4.8 43.8 9.7 68.2 9.7 6.2 0 12.1-.3 18.3-.8-4-12.9-6.2-26.6-6.2-40.8-.1-84.9 72.9-154 165.3-154zm-104.5-52.9c14.5 0 24.2 9.7 24.2 24.4 0 14.5-9.7 24.2-24.2 24.2-14.8 0-29.3-9.7-29.3-24.2.1-14.7 14.6-24.4 29.3-24.4zm-136.4 48.6c-14.5 0-29.3-9.7-29.3-24.2 0-14.8 14.8-24.4 29.3-24.4 14.8 0 24.4 9.7 24.4 24.4 0 14.6-9.6 24.2-24.4 24.2zM563 319.4c0-77.9-77.9-141.3-165.4-141.3-92.7 0-165.4 63.4-165.4 141.3S305 460.7 397.6 460.7c19.3 0 38.9-5.1 58.6-9.9l53.4 29.3-14.8-48.6C534 402.1 563 363.2 563 319.4zm-219.1-24.5c-9.7 0-19.3-9.7-19.3-19.6 0-9.7 9.7-19.3 19.3-19.3 14.8 0 24.4 9.7 24.4 19.3 0 10-9.7 19.6-24.4 19.6zm107.1 0c-9.7 0-19.3-9.7-19.3-19.6 0-9.7 9.7-19.3 19.3-19.3 14.5 0 24.4 9.7 24.4 19.3.1 10-9.9 19.6-24.4 19.6z'%3E%3C/path%3E%3C/svg%3E");
    }

    .address-icon {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 384 512'%3E%3Cpath fill='%23666' d='M172.268 501.67C26.97 291.031 0 269.413 0 192 0 85.961 85.961 0 192 0s192 85.961 192 192c0 77.413-26.97 99.031-172.268 309.67-9.535 13.774-29.93 13.773-39.464 0zM192 272c44.183 0 80-35.817 80-80s-35.817-80-80-80-80 35.817-80 80 35.817 80 80 80z'%3E%3C/path%3E%3C/svg%3E");
    }

    .phone-call-btn {
        display: inline-block;
        background-color: #25D366;
        color: white;
        padding: 2px 8px;
        border-radius: 3px;
        font-size: 12px;
        text-decoration: none;
        margin-left: 5px;
    }
    </style>
</head>
<body>
    <div class="yui-top yui-1200">
    <div class="yui-top-center">
        <div class="yui-top-left yui-left">
            <a href="/">网站首页</a>
            <a href="#">移动版</a>
            <a href="#">微信公众号</a>
            <a href="#">快速发布</a>
        </div>

        <div class="yui-top-right yui-right yui-text-right">
            <a href="#">登录</a><a href="#">注册</a><div class="yui-top-dropdown">
                <span class="yui-top-dropdown-btn">会员中心</span>
                <ul class="yui-top-dropdown-menu">
                    <li><a href="#">我的信息</a></li>
                    <li><a href="#">我的收藏</a></li>
                    <li><a href="#">账号设置</a></li>
                </ul>
            </div><div class="yui-top-dropdown">
                <span class="yui-top-dropdown-btn">商家中心</span>
                <ul class="yui-top-dropdown-menu">
                    <li><a href="#">商家入驻</a></li>
                    <li><a href="#">商家管理</a></li>
                    <li><a href="#">营销推广</a></li>
                </ul>
            </div><div class="yui-top-dropdown">
                <span class="yui-top-dropdown-btn">网站导航</span>
                <ul class="yui-top-dropdown-menu">
                    <li><a href="#">关于我们</a></li>
                    <li><a href="#">联系我们</a></li>
                    <li><a href="#">使用帮助</a></li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- 简洁头部 -->
<div class="simple-header">
    <div class="simple-header-inner">
        <div class="logo-title-group">
            <div class="simple-logo">
                <a href="/"><img src="/template/pc/images/logo.png" alt="泊头生活网"></a>
            </div>
            <span class="title-separator"></span>
            <h1 class="simple-title">选择栏目</h1>
        </div>
        <a href="/" class="simple-back">返回网站首页</a>
    </div>
</div>
    
    <!-- 广告栏 -->
  <!--  <div class="mo-box yui-1200">
        <a href="#" target="_blank" title="广告位"><img src="images/2022112309104176804697_1240_90.png"></a>
    </div> -->
    
    <div class="yui-clear"></div>
    
    <!-- 面包屑导航 -->
	<div class="yui-content yui-1200">
		<!-- 面包屑导航 -->
		<div class="breadcrumb-container">
			<div class="breadcrumb">
				<a href="/">首页</a>
				<span class="separator">></span>
                <?php if($post['parent_category_id'] > 0): ?>
				<a href="/<?php echo (isset($post['parent_category_pinyin'])) ? $post['parent_category_pinyin'] : ""; ?>/"><?php echo (isset($post['parent_category_name'])) ? $post['parent_category_name'] : ""; ?></a>
				<span class="separator">></span>
                <?php endif; ?>
				<a href="/<?php echo (isset($post['category_pinyin'])) ? $post['category_pinyin'] : ""; ?>/"><?php echo (isset($post['category_name'])) ? $post['category_name'] : ""; ?></a>
                <span class="separator">></span>
                <?php echo (isset($post['title'])) ? $post['title'] : ""; ?>
			</div>
		</div>
	</div>
   <!-- 面包屑导航 -->

    <!-- 主内容区域 -->
    <div class="yui-content yui-1200">
        <div class="yui-list bg">
            <!-- 预留 -->
        </div>
        <div class="yui-clear"></div>
        
        <div class="yui-info">
            <!-- 列表左侧 -->
            <div class="yui-info-left bg-white pd20" style="position: relative;">
                <!-- 信息标题 -->
                <div class="yui-info-title">
                    <h1 class=""><?php echo (isset($post['title'])) ? $post['title'] : ""; ?></h1>
                    <div class="info-tags">
                        <span class="info-tag main-area"><a href="/<?php echo (isset($post['category_pinyin'])) ? $post['category_pinyin'] : ""; ?>/"><?php echo (isset($post['category_name'])) ? $post['category_name'] : ""; ?></a></span>
                        <span class="info-tag sub-area"><?php echo (isset($post['region_name'])) ? $post['region_name'] : ""; ?></span>
                        <!-- <span class="info-tag main-category">招聘求职</span>
                        <span class="info-tag sub-category">建筑/房产/装修/物业</span> -->
                    </div>
                </div>
                <?php 
                if (!empty($post['expired_at'])) {
                    $days = getRemainingDaysInt($post['expired_at']);
                    $guoqi = $days > 0 ? $days.'天有效' : '已过期';
                } else {
                    $guoqi = '长期';
                }
                 ?>
                <div class="yui-info-qt">
                    <p><span>信息编号：<?php echo (isset($post['id'])) ? $post['id'] : ""; ?></span>
                        <span>发布时间：<?php echo friendlyTime($post['updated_at']); ?></span>
                        <span>浏览次数：<?php echo (isset($post['view_count'])) ? $post['view_count'] : ""; ?></span>
                        <span>有效期：<?php echo $guoqi ?? ""; ?></span>
                        <span style="float: right;" class="manage-links">
                            <a href="javascript:void(0);" onclick="handleAction('refresh', <?php echo (isset($post['id'])) ? $post['id'] : ""; ?>);" style="color: #0066cc;">刷新</a>
                            <a href="javascript:void(0);" onclick="handleAction('edit', <?php echo (isset($post['id'])) ? $post['id'] : ""; ?>);" style="color: #009933;">修改</a>
                            <a href="javascript:void(0);" onclick="handleAction('delete', <?php echo (isset($post['id'])) ? $post['id'] : ""; ?>);" style="color: #cc0000;">删除</a>
                            <a href="/top.php?id=<?php echo (isset($post['id'])) ? $post['id'] : ""; ?>" style="color: red;">置顶</a>
                            <a href="javascript:void(0);" onclick="showReportModal(<?php echo (isset($post['id'])) ? $post['id'] : ""; ?>);" style="color: #ff6600;">举报</a>
                        </span>
                    </p>
                </div>
                <?php if(null !== ($images ?? null) && !empty($images)): ?>
                <div class="image-gallery">
                    <div class="gallery-grid">
                        <?php if(null !== ($images ?? null) && is_array($images)): foreach($images as $image): ?>
                        <div class="gallery-item">
                            <img src="/<?php echo (isset($image['thumb_path'])) ? $image['thumb_path'] : ""; ?>" 
                                 data-original="/<?php echo (isset($image['file_path'])) ? $image['file_path'] : ""; ?>" 
                                 alt="<?php echo (isset($post['title'])) ? $post['title'] : ""; ?>的图片">
                        </div>
                        <?php endforeach; endif; ?>
                    </div>
                </div>
                <div class="yui-clear"></div>
                <?php endif; ?>

                
                
                <div class="yui-content-wrap" style="position: relative;">
                    <?php if($post['is_expired'] == 1): ?><div class="expired-stamp"><img src="/template/pc/images/stamp.gif" alt="信息已失效"></div><?php endif; ?>
                    <?php echo (isset($post['content'])) ? $post['content'] : ""; ?>
                    <!-- <br>联系时，请告之从【<a href="https://www.botou.net/">泊头生活网</a>】看到的信息 -->
                </div>
                
                <!-- 联系方式区域 -->
                <div class="contact-info">
                    <?php 
                    // 判断信息是否过期
                    $isExpired = ($post['is_expired'] == 1);
                     ?>
                    
                    <div class="contact-item">
                        <span class="contact-label">联系人:</span>
                        <span class="contact-value"><?php echo (isset($post['contact_name'])) ? $post['contact_name'] : ""; ?></span>
                    </div>
                    
                    <div class="contact-item">
                        <span class="contact-label">电话:</span>
                        <span class="contact-value">
                            <?php if(!$isExpired): ?>
                                <!-- 使用图片显示电话号码 -->
                                <img src="/phone_image.php?phone=<?php echo (isset($post['contact_mobile'])) ? $post['contact_mobile'] : ""; ?>&size=13" alt="联系电话" class="phone-image">
                                <?php if(null !== ($post ?? null) && is_array($post) && array_key_exists('contact_mobile', $post) && !empty($post['contact_mobile'])): ?>
                                <?php 
                                $phoneLocation = getPhoneLocation($post['contact_mobile']);
                                 ?>
                                <?php if(null !== ($phoneLocation ?? null) && !empty($phoneLocation)): ?>
                                <span style="color: #999; font-size: 12px; margin-left: 8px; font-family: SimSun, '宋体'">号码归属地：<?php echo $phoneLocation ?? ""; ?></span>
                                <?php endif; ?>
                                <?php endif; ?>
                            <?php else: ?>
                                <span class="expired-text">信息已过期，电话隐藏!</span>
                            <?php endif; ?>
                        </span>
                    </div>
                    
                    <?php if(null !== ($post ?? null) && is_array($post) && array_key_exists('contact_weixin', $post) && !empty($post['contact_weixin'])): ?>
                    <div class="contact-item">
                        <span class="contact-label">微信:</span>
                        <span class="contact-value">
                            <?php if(!$isExpired): ?>
                                <?php echo (isset($post['contact_weixin'])) ? $post['contact_weixin'] : ""; ?>
                            <?php else: ?>
                                <span class="expired-text">信息已过期，微信隐藏!</span>
                            <?php endif; ?>
                        </span>
                    </div>
                    <?php endif; ?>
                    
                    <?php if(null !== ($post ?? null) && is_array($post) && array_key_exists('contact_address', $post) && !empty($post['contact_address'])): ?>
                    <div class="contact-item">
                        <span class="contact-label">地址:</span>
                        <span class="contact-value"><?php echo (isset($post['contact_address'])) ? $post['contact_address'] : ""; ?></span>
                    </div>
                    <?php endif; ?>
                </div>
                <!-- 联系方式区域结束 -->
                
                <div class="yui-clear"></div>

                
                <div class="infoquote" style="margin-bottom:25px;">
                    <p><span class="t">使用信息须知（必读）</span></p>
                    <p>①请认真阅读服务协议（www.botou.net/aboutus/shenmin.html），使用信息视为已知晓并同意该协议</p>
                    <p>②该信息（含图片）由用户自行发布，其真实性、准确性和合法性由信息发布者负责</p>
                    <p>③泊头生活网仅提供信息交流平台，不介入任何交易过程，不承担安全风险和法律责任</p>
                    <p>
                        <font color="#0000FF">④强烈建议：拒绝预付费用、选择见面交易、核验证照资质、签订交易合同</font>
                    </p>
                    <p>
                        <font color="#0000FF">⑤特别提示：提高警惕，谨防诈骗</font>
                    </p>
                </div>
            </div>
            <!-- 列表左侧 end -->

            <!-- 列表右侧 -->
            <div class="yui-info-right">
                <div class="bbs-hot">
                    <div class="yui-h-title">
                        <h3>公益广告</h3><span></span>
                    </div>
                    <div class="yui-img-list">
                        <a href=""><img src="/template/pc/images/16382506823782.png" /></a>
                    </div>
                </div>
                <div class="yui-clear"></div>
                <div class="bbs-hot">
                    <div class="yui-h-title">
                        <h3>最新信息</h3><span><a href="/">更多</a></span>
                    </div>
                    <div class="yui-small-list">
                        <ul>
                            <li><a href="/showinfo-61-42-0.html" target="_blank">泊头成人高考报名_工作取证两不误</a></li>
                            <li><a href="/showinfo-18-1538-0.html" target="_blank">出租空地</a></li>
                            <li><a href="/showinfo-28-1535-0.html" target="_blank">招聘工人</a></li>
                            <li><a href="/showinfo-40-1533-0.html" target="_blank">手机/电脑</a></li>
                            <li><a href="/showinfo-84-1532-0.html" target="_blank">手机/电脑全业务服务</a></li>
                            <li><a href="/showinfo-19-1497-0.html" target="_blank">招聘美容师，人事经理</a></li>
                            <li><a href="/showinfo-12-10-0.html" target="_blank">出售龙湾小区高层3室2厅1卫，豪华装修</a></li>
                            <li><a href="/showinfo-74-1496-0.html" target="_blank">脉冲除尘器</a></li>
                            <li><a href="/showinfo-93-1494-0.html" target="_blank">出自用小米120w氮化镓充电器</a></li>
                            <li><a href="/showinfo-19-41-0.html" target="_blank">招聘美工，电话销售</a></li>
                            <li><a href="/showinfo-13-38-0.html" target="_blank">泊头五金建材城西区出租三层门市</a></li>
                            <li><a href="/showinfo-13-37-0.html" target="_blank">整体出租门市楼</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <!-- 列表右侧 end -->
        </div>
    </div>
    
    <!-- 底部 -->
    <div class="yui-footer">
    <div class="yui-1200">
        <div class="footer-content bg-white">
            <!-- 友情链接区域 -->
          
            <p class="footer-nav">
                <a href="https://www.botou.net/" title="泊头生活网">网站首页</a>
                <a href="https://www.botou.net/aboutus/tuiguang.html" target="_blank">广告服务</a>
                <a href="https://www.botou.net/aboutus/shenmin.html" target="_blank">法律声明</a>
                <a href="https://www.botou.net/aboutus/about.html" target="_blank">网站介绍</a>
                <a href="https://www.botou.net/aboutus/contactus.html" target="_blank">联系我们</a>
                <a href="https://www.botou.net/aboutus/job.html" target="_blank">招聘信息</a>
            </p>
            <p class="footer-disclaimer">2本站信息均由网民发表,不代表本网站立场,如侵犯了您的权利请致电投诉</p>
            <p class="footer-disclaimer">客服电话： &nbsp; 客服邮箱：<font><EMAIL></font> <a href="http://cyberpolice.mps.gov.cn/wfjb/" target="_blank" rel="nofollow">网络违法犯罪举报网站</a></p>
            <p class="footer-copyright"><?php if($site_copyright): ?><?php echo $site_copyright ?? ""; ?><?php else: ?>Copyright © 2024 分类信息网站 All Rights Reserved<?php endif; ?></p>
            <?php if($site_icp): ?><p class="footer-copyright"><a href="https://beian.miit.gov.cn/" target="_blank" id="footericp" rel="nofollow"><?php echo $site_icp ?? ""; ?></a></p><?php endif; ?>
        </div>
    </div>
</div>

    <!-- 图片灯箱效果 -->
    <div id="image-lightbox" class="lightbox-overlay" style="display: none;">
        <div class="lightbox-container">
            <span class="lightbox-close" onclick="closeLightbox()">&times;</span>
            <img id="lightbox-img" class="lightbox-content" src="" alt="灯箱图片">
            <div class="lightbox-caption" id="lightbox-caption"></div>
        </div>
        <!-- 切换箭头移到overlay层，固定在视窗两侧 -->
        <a class="lightbox-prev" onclick="changeImage(-1)">&#10094;</a>
        <a class="lightbox-next" onclick="changeImage(1)">&#10095;</a>
    </div>

    <!-- 操作提示弹出层 -->
    <!-- 加载提示层 -->
    <div id="loading-modal" class="modal-overlay" style="display: none;">
        <div class="modal-content loading-modal">
            <div class="loading-spinner"></div>
            <div class="loading-text">正在处理...</div>
        </div>
    </div>

    <!-- 错误提示层 -->
    <div id="error-modal" class="modal-overlay" style="display: none;">
        <div class="modal-content error-modal">
            <div class="modal-icon error-icon">
                <i class="fas fa-times-circle"></i>
            </div>
            <div class="modal-title">操作失败</div>
            <div class="modal-message" id="error-message"></div>
            <div class="modal-buttons">
                <button class="modal-btn primary" onclick="hideErrorModal()">确定</button>
            </div>
        </div>
    </div>

    <!-- 成功提示层 -->
    <div id="success-modal" class="modal-overlay" style="display: none;">
        <div class="modal-content success-modal">
            <div class="modal-icon success-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="modal-title">操作成功</div>
            <div class="modal-message" id="success-message"></div>
            <div class="modal-buttons">
                <button class="modal-btn primary" onclick="hideSuccessModal()">确定</button>
            </div>
        </div>
    </div>
    
    <script type="text/javascript" src="/template/pc/js/jquery.min.js"></script>
    <script type="text/javascript" src="/template/pc/js/common.js"></script>
    <script>
        // 图片灯箱相关逻辑
        $(document).ready(function() {
            // 图片灯箱相关变量
            var lightbox = document.getElementById('image-lightbox');
            var lightboxContainer = lightbox.querySelector('.lightbox-container');
            var lightboxImg = document.getElementById('lightbox-img');
            var lightboxCaption = document.getElementById('lightbox-caption');
            var currentImageIndex = 0;
            var galleryImages = [];
            
            // 公开函数，使其可从HTML调用
            window.openLightbox = function(imgElement) {
                // 获取原图路径
                var originalSrc = imgElement.getAttribute('data-original');

                // 收集所有图库图片
                galleryImages = $('.gallery-item img').get();

                // 找到当前图片索引
                for (var i = 0; i < galleryImages.length; i++) {
                    if (galleryImages[i] === imgElement) {
                        currentImageIndex = i;
                        break;
                    }
                }

                // 设置图片源
                lightboxImg.src = originalSrc;

                // 添加页面计数
                updateCaption();

                // 根据图片数量决定是否显示切换箭头
                if (galleryImages.length <= 1) {
                    lightbox.classList.add('single-image');
                } else {
                    lightbox.classList.remove('single-image');
                }

                // 显示灯箱并禁止页面滚动
                lightbox.style.display = 'flex';
                document.body.style.overflow = 'hidden';

                // 添加动画效果
                setTimeout(function() {
                    lightboxContainer.style.opacity = '1';
                }, 10);
            };
            
            // 关闭灯箱
            window.closeLightbox = function() {
                lightbox.style.display = 'none';
                document.body.style.overflow = '';
            };
            
            // 更新说明文字
            function updateCaption() {
                lightboxCaption.innerHTML = "图片 " + (currentImageIndex + 1) + " / " + galleryImages.length;
            }
            
            // 切换图片
            window.changeImage = function(step) {
                currentImageIndex += step;
                
                // 循环浏览
                if (currentImageIndex >= galleryImages.length) {
                    currentImageIndex = 0;
                } else if (currentImageIndex < 0) {
                    currentImageIndex = galleryImages.length - 1;
                }
                
                // 更新图片
                var nextImageSrc = $(galleryImages[currentImageIndex]).attr('data-original');
                lightboxImg.src = nextImageSrc;
                updateCaption();
            };
            
            // 使用jQuery绑定事件
            $('.gallery-item img').on('click', function(e) {
                e.preventDefault();
                openLightbox(this);
                return false;
            });
            
            // 点击灯箱背景时关闭
            $(lightbox).click(function(e) {
                // 如果点击的是灯箱背景而不是内容容器，则关闭灯箱
                if (e.target === lightbox) {
                    closeLightbox();
                }
            });
            
            // 阻止点击容器时事件冒泡到背景
            $(lightboxContainer).click(function(e) {
                e.stopPropagation();
            });
            
            // 键盘导航
            $(document).keydown(function(e) {
                if (lightbox.style.display === 'flex') {
                    if (e.keyCode === 37) {
                        changeImage(-1);
                    } else if (e.keyCode === 39) {
                        changeImage(1);
                    } else if (e.keyCode === 27) {
                        closeLightbox();
                    }
                }
            });
            
            // 确保关闭按钮工作
            $('.lightbox-close').click(function(e) {
                e.preventDefault();
                closeLightbox();
                return false;
            });
        });

        // 弹出层控制函数
        function showLoadingModal(message = '正在处理...') {
            console.log('showLoadingModal 被调用:', message);
            const loadingModal = document.getElementById('loading-modal');
            const loadingText = document.querySelector('.loading-text');
            console.log('loading-modal 元素:', loadingModal);
            console.log('loading-text 元素:', loadingText);

            if (loadingModal) {
                loadingModal.style.display = 'flex';
                console.log('设置 loading-modal 显示');
            }
            if (loadingText) {
                loadingText.textContent = message;
                console.log('设置加载文本:', message);
            }
        }

        function hideLoadingModal() {
            document.getElementById('loading-modal').style.display = 'none';
        }

        function showErrorModal(message) {
            document.getElementById('error-message').textContent = message;
            document.getElementById('error-modal').style.display = 'flex';
        }

        function hideErrorModal() {
            document.getElementById('error-modal').style.display = 'none';
        }

        function showSuccessModal(message, callback = null) {
            console.log('showSuccessModal 被调用:', message, callback);
            const successMessage = document.getElementById('success-message');
            const successModal = document.getElementById('success-modal');
            console.log('success-message 元素:', successMessage);
            console.log('success-modal 元素:', successModal);

            if (successMessage) {
                successMessage.textContent = message;
                console.log('设置成功消息:', message);
            }
            if (successModal) {
                successModal.style.display = 'flex';
                console.log('显示成功弹出层');
            }

            // 如果有回调函数，保存起来
            if (callback) {
                window.successModalCallback = callback;
                console.log('保存回调函数');
            }
        }

        function hideSuccessModal() {
            document.getElementById('success-modal').style.display = 'none';

            // 执行回调函数
            if (window.successModalCallback) {
                window.successModalCallback();
                window.successModalCallback = null;
            }
        }

        // 点击遮罩层关闭弹出层
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('modal-overlay')) {
                if (e.target.id === 'error-modal') {
                    hideErrorModal();
                } else if (e.target.id === 'success-modal') {
                    hideSuccessModal();
                }
            }
        });
    </script>

    <!-- 添加密码验证弹窗 -->
    <div id="password-modal" class="password-modal">
        <div class="password-modal-content">
            <div class="modal-header">
                <h3>请输入信息管理密码</h3>
                <span class="close-modal" onclick="closePasswordModal()">&times;</span>
            </div>
            <div class="modal-body">
                <!-- 添加不同操作的说明文字 -->
                <div class="action-description" id="action-description">
                    <!-- 这里将根据不同操作动态显示说明文字 -->
                </div>
                <div class="password-input-group">
                    <input type="password" id="manage-password" placeholder="请输入发布信息时设置的管理密码" autocomplete="off">
                    <div class="error-message" id="password-error"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" onclick="closePasswordModal()" class="btn-cancel">取消</button>
                <button type="button" onclick="verifyPassword()" class="btn-submit">确认</button>
            </div>
        </div>
    </div>

    <!-- 举报弹出层 -->
    <div class="report-modal" id="reportModal" style="display: none;">
        <div class="report-modal-content">
            <div class="modal-header">
                <h3>举报信息</h3>
                <span class="modal-close" onclick="closeReportModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label>举报类型：</label>
                    <div class="report-types">
                        <label><input type="radio" name="report_type" value="1"> 虚假信息</label>
                        <label><input type="radio" name="report_type" value="2"> 诈骗信息</label>
                        <label><input type="radio" name="report_type" value="3"> 违法违规</label>
                        <label><input type="radio" name="report_type" value="4"> 其他问题</label>
                    </div>
                </div>
                <div class="form-group">
                    <label>详细说明（可选）：</label>
                    <textarea id="report-content" placeholder="请简要说明举报原因..." rows="3"></textarea>
                </div>
                <div class="error-message" id="report-error"></div>
            </div>
            <div class="modal-footer">
                <button type="button" onclick="closeReportModal()" class="btn-cancel">取消</button>
                <button type="button" onclick="submitReport()" class="btn-submit">提交举报</button>
            </div>
        </div>
    </div>

    <script type="text/javascript">
    (function() {
        let currentAction = '';
        let currentPostId = 0;
        
        // 将函数绑定到window对象，使其可全局访问
        window.handleAction = function(action, postId) {
            console.log('handleAction 被调用:', action, postId);
            // 检查是否已经验证过
            const verifiedKey = `verified_${postId}`;
            const verifiedTime = localStorage.getItem(verifiedKey);
            console.log('验证状态检查:', verifiedKey, verifiedTime);

            if (verifiedTime && (new Date().getTime() - parseInt(verifiedTime)) < 30 * 60 * 1000) {
                // 30分钟内已验证，直接执行操作
                console.log('已验证，直接执行操作');
                executeAction(action, postId);
            } else {
                // 未验证或验证已过期，显示密码弹窗
                console.log('未验证，显示密码弹窗');
                currentAction = action;
                currentPostId = postId;
                showPasswordModal();
            }
        };
        
        window.showPasswordModal = function() {
            console.log('showPasswordModal 被调用');
            const modal = document.getElementById('password-modal');
            console.log('password-modal 元素:', modal);

            if (modal) {
                modal.style.display = 'flex';
                console.log('设置密码弹窗显示');
            } else {
                console.error('找不到 password-modal 元素');
                return;
            }

            const input = document.getElementById('manage-password');
            console.log('manage-password 输入框:', input);
            if (input) {
                input.value = '';
            }

            const errorElement = document.getElementById('password-error');
            if (errorElement) {
                errorElement.style.display = 'none';
            }
            
            // 根据当前操作设置相应的说明文字
            const description = document.getElementById('action-description');
            switch(currentAction) {
                case 'refresh':
                    description.innerHTML = '<p>刷新信息：将更新当前信息的发布时间，对于已过期的信息会延长有效期30天。</p>';
                    break;
                case 'edit':
                    description.innerHTML = '<p>修改信息：您可以修改当前信息的标题、内容和联系方式等信息。</p>';
                    break;
                case 'delete':
                    description.innerHTML = '<p>删除信息：此操作将使信息不再显示在网站上，请确认是否继续。</p>';
                    break;
                default:
                    description.innerHTML = '<p>请输入信息管理密码以完成身份验证。</p>';
            }
            
            // 自动聚焦到密码输入框
            setTimeout(() => {
                input.focus();
            }, 100);
            // 禁止背景滚动
            document.body.style.overflow = 'hidden';
        };
        
        window.closePasswordModal = function() {
            console.log('closePasswordModal 被调用');
            const modal = document.getElementById('password-modal');
            console.log('关闭密码弹窗，modal元素:', modal);
            if (modal) {
                modal.style.display = 'none';
                console.log('密码弹窗已隐藏');
            }
            // 恢复背景滚动
            document.body.style.overflow = '';
        };
        
        window.verifyPassword = function() {
            const password = document.getElementById('manage-password').value.trim();
            if (!password) {
                showError('请输入管理密码');
                return;
            }
            
            // 显示加载状态
            const submitBtn = document.querySelector('.btn-submit');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = '验证中...';
            submitBtn.disabled = true;
            
            // 发送验证请求
            fetch('/manage.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: `action=verify&id=${currentPostId}&password=${password}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 验证成功，记录验证时间
                    localStorage.setItem(`verified_${currentPostId}`, new Date().getTime().toString());
                    closePasswordModal();
                    executeAction(currentAction, currentPostId);
                } else {
                    showError(data.message || '密码验证失败');
                }
            })
            .catch(error => {
                showError('验证请求失败，请重试');
                console.error('Error:', error);
            })
            .finally(() => {
                // 恢复按钮状态
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            });
        };
        
        function showError(message) {
            const errorElement = document.getElementById('password-error');
            errorElement.textContent = message;
            errorElement.style.display = 'block';
            // 只显示错误提示，不要重新载入动画效果
            // 移除了晃动效果，保持简洁
        }
        
        function executeAction(action, postId) {
            console.log('executeAction 被调用:', action, postId);
            switch(action) {
                case 'refresh':
                    console.log('执行刷新操作，调用 executeRefreshAction');
                    // 使用AJAX方式执行刷新操作，避免页面跳转
                    executeRefreshAction(postId);
                    break;
                case 'edit':
                    console.log('执行编辑操作，页面跳转');
                    window.location.href = `/manage.php?id=${postId}&action=edit`;
                    break;
                case 'delete':
                    console.log('执行删除操作');
                    // 检查是否是密码验证后的调用
                    const isAfterPasswordVerification = (currentAction === 'delete' && currentPostId === postId);
                    // 使用AJAX方式执行删除操作，避免页面跳转
                    executeDeleteAction(postId, !isAfterPasswordVerification); // 密码验证后不显示确认对话框
                    break;
            }
        }

        // 执行刷新操作的AJAX函数
        function executeRefreshAction(postId) {
            console.log('executeRefreshAction 被调用:', postId);
            // 显示加载状态
            console.log('显示加载弹出层');
            showLoadingModal('正在刷新信息...');

            // 发送AJAX请求，确保携带会话信息
            fetch(`/manage.php?id=${postId}&action=refresh&ajax=1`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                credentials: 'same-origin' // 确保携带cookie和会话信息
            })
            .then(response => {
                console.log('刷新响应状态:', response.status);
                return response.json();
            })
            .then(data => {
                try {
                    hideLoadingModal();
                    console.log('刷新响应数据:', data);

                    if (data.success) {
                        // 刷新成功，显示成功提示并刷新页面
                        console.log('刷新成功，准备显示成功提示');
                        showSuccessModal(data.message, () => {
                            console.log('成功提示回调被执行，准备刷新页面');
                            window.location.reload();
                        });
                    } else {
                        // 刷新失败，显示错误提示
                        console.log('刷新失败，准备显示错误提示');

                        // 如果是权限验证失败，清除localStorage并重新显示密码弹窗
                        if (data.message && data.message.includes('请输入管理密码')) {
                            console.log('权限验证失败，清除localStorage并重新显示密码弹窗');
                            localStorage.removeItem(`verified_${postId}`);
                            currentAction = 'refresh';
                            currentPostId = postId;
                            showPasswordModal();
                        } else {
                            // 其他错误，显示错误提示
                            showErrorModal(data.message);
                        }
                    }
                } catch (error) {
                    console.error('处理响应数据时出错:', error);
                    showErrorModal('处理响应时出现错误');
                }
            })
            .catch(error => {
                hideLoadingModal();
                console.error('刷新请求失败:', error);
                showErrorModal('网络请求失败，请稍后重试');
            });
        }

        // 执行删除操作的AJAX函数
        function executeDeleteAction(postId, showConfirm = true) {
            console.log('executeDeleteAction 被调用:', postId, 'showConfirm:', showConfirm);

            // 如果需要显示确认对话框
            if (showConfirm && !confirm('确定要删除此信息吗？删除后将不再显示。')) {
                return;
            }

            // 显示加载状态
            console.log('显示删除加载弹出层');
            showLoadingModal('正在删除信息...');

            // 发送AJAX请求，确保携带会话信息
            fetch(`/manage.php?id=${postId}&action=delete&ajax=1`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Content-Type': 'application/json'
                },
                credentials: 'same-origin' // 确保携带cookie和会话信息
            })
            .then(response => {
                console.log('删除响应状态:', response.status);
                return response.json();
            })
            .then(data => {
                hideLoadingModal();
                console.log('删除响应数据:', data);

                try {
                    if (data.success) {
                        // 删除成功，显示成功提示
                        console.log('删除成功，显示成功弹出层');
                        showSuccessModal(data.message || '信息删除成功', function() {
                            // 删除成功后跳转到首页
                            window.location.href = '/';
                        });
                    } else {
                        // 删除失败，检查是否需要密码验证
                        if (data.message && data.message.includes('密码')) {
                            // 需要密码验证，显示密码输入框
                            currentAction = 'delete';
                            currentPostId = postId;
                            showPasswordModal();
                        } else {
                            // 其他错误，显示错误提示
                            showErrorModal(data.message || '删除失败，请稍后重试');
                        }
                    }
                } catch (error) {
                    console.error('处理删除响应数据时出错:', error);
                    showErrorModal('处理响应时出现错误');
                }
            })
            .catch(error => {
                hideLoadingModal();
                console.error('删除请求失败:', error);
                showErrorModal('网络请求失败，请稍后重试');
            });
        }

        // 添加回车键支持
        document.getElementById('manage-password').addEventListener('keyup', function(event) {
            if (event.key === 'Enter') {
                verifyPassword();
            }
        });
        
        // 点击遮罩层关闭弹窗
        document.querySelector('.password-modal').addEventListener('click', function(event) {
            if (event.target === this) {
                closePasswordModal();
            }
        });

        // 举报相关函数
        window.showReportModal = function(postId) {
            window.currentReportPostId = postId;
            document.getElementById('reportModal').style.display = 'flex';
            // 清空表单
            document.querySelectorAll('input[name="report_type"]').forEach(radio => radio.checked = false);
            document.getElementById('report-content').value = '';
            document.getElementById('report-error').style.display = 'none';
        };

        window.closeReportModal = function() {
            document.getElementById('reportModal').style.display = 'none';
        };

        window.submitReport = function() {
            const reportType = document.querySelector('input[name="report_type"]:checked');
            const content = document.getElementById('report-content').value;
            const errorElement = document.getElementById('report-error');

            if (!reportType) {
                errorElement.textContent = '请选择举报类型';
                errorElement.style.display = 'block';
                return;
            }

            // 提交举报
            fetch('/api/report.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    post_id: window.currentReportPostId,
                    report_type: reportType.value,
                    content: content
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('举报提交成功，我们会尽快处理');
                    closeReportModal();
                } else {
                    errorElement.textContent = data.message || '举报提交失败';
                    errorElement.style.display = 'block';
                }
            })
            .catch(error => {
                errorElement.textContent = '网络错误，请稍后重试';
                errorElement.style.display = 'block';
            });
        };

        // 点击遮罩层关闭举报弹窗
        document.getElementById('reportModal').addEventListener('click', function(event) {
            if (event.target === this) {
                closeReportModal();
            }
        });

        // 添加现代化扁平化样式
        const style = document.createElement('style');
        style.textContent = `
            /* 弹窗基础样式 */
            .password-modal {
                display: none;
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.6);
                z-index: 1000;
                justify-content: center;
                align-items: center;
            }
            
            /* 弹窗内容容器 */
            .password-modal-content {
                background-color: #ffffff;
                border-radius: 0;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                width: 400px;
                max-width: 90%;
                overflow: hidden;
                animation: modalFadeIn 0.3s ease;
                transition: all 0.3s ease;
                border: none;
            }
            
            /* 弹窗头部 */
            .modal-header {
                padding: 16px 20px;
                background-color: #ffffff;
                border-bottom: 1px solid #eee;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            
            .modal-header h3 {
                margin: 0;
                color: #333;
                font-size: 16px;
                font-weight: 500;
            }
            
            .close-modal {
                font-size: 22px;
                color: #999;
                cursor: pointer;
                transition: color 0.2s;
            }
            
            .close-modal:hover {
                color: #333;
            }
            
            /* 弹窗主体 */
            .modal-body {
                padding: 20px;
            }
            
            /* 说明文字样式 */
            .action-description {
                margin-bottom: 20px;
                padding: 12px 15px;
                background-color: #f5f5f5;
                position: relative;
            }
            
            .action-description p {
                margin: 0;
                color: #333;
                font-size: 14px;
                line-height: 1.6;
            }
            
            /* 密码输入组 */
            .password-input-group {
                margin-bottom: 16px;
                position: relative;
            }
            
            .password-input-group input {
                width: 100%;
                padding: 10px 12px;
                border: 1px solid #ddd;
                border-radius: 0;
                font-size: 14px;
                transition: all 0.3s;
                box-sizing: border-box;
            }
            
            .password-input-group input:focus {
                border-color: #1890ff;
                outline: none;
            }
            
            .error-message {
                display: none;
                color: #f00;
                font-size: 13px;
                margin-top: 6px;
            }
            
            /* 弹窗底部 */
            .modal-footer {
                padding: 15px 20px;
                background-color: #f8f8f8;
                border-top: 1px solid #eee;
                display: flex;
                justify-content: flex-end;
                gap: 10px;
            }
            
            /* 按钮样式 */
            .modal-footer button {
                padding: 8px 16px;
                font-size: 14px;
                border-radius: 0;
                cursor: pointer;
                transition: all 0.3s;
            }
            
            .btn-cancel {
                background-color: #f5f5f5;
                color: #333;
                border: 1px solid #ddd;
            }
            
            .btn-cancel:hover {
                background-color: #e8e8e8;
            }
            
            .btn-submit {
                background-color: #1890ff;
                color: white;
                border: 1px solid #1890ff;
            }
            
            .btn-submit:hover {
                background-color: #40a9ff;
            }
            
            .btn-submit:disabled {
                background-color: #d9d9d9;
                border-color: #d9d9d9;
                color: #999;
                cursor: not-allowed;
            }
            
            /* 动画效果 */
            @keyframes modalFadeIn {
                from {
                    opacity: 0;
                    transform: translateY(-20px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
            
            /* 移除晃动动画，密码错误时只显示提示信息 */
            
            /* 针对不同操作类型的颜色 */
            .action-description.refresh {
                background-color: #f0f7ff;
                border-left: 3px solid #1890ff;
            }
            
            .action-description.edit {
                background-color: #f6ffed;
                border-left: 3px solid #52c41a;
            }
            
            .action-description.delete {
                background-color: #fff1f0;
                border-left: 3px solid #ff4d4f;
            }

            /* 举报弹出层样式 */
            .report-modal {
                display: none;
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 1000;
                align-items: center;
                justify-content: center;
            }

            .report-modal-content {
                background-color: white;
                border-radius: 8px;
                width: 90%;
                max-width: 450px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            }

            .report-types {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 8px;
                margin-top: 10px;
            }

            .report-types label {
                display: flex;
                align-items: center;
                padding: 6px 10px;
                border: 1px solid #ddd;
                border-radius: 4px;
                cursor: pointer;
                transition: all 0.2s;
                font-size: 13px;
            }

            .report-types label:hover {
                background-color: #f5f5f5;
                border-color: #007bff;
            }

            .report-types input[type="radio"] {
                margin-right: 6px;
            }

            .report-types label:has(input:checked) {
                background-color: #e7f3ff;
                border-color: #007bff;
                color: #007bff;
            }

            #report-content {
                width: 100%;
                padding: 10px;
                border: 1px solid #ddd;
                border-radius: 4px;
                resize: vertical;
                font-family: inherit;
                margin-top: 8px;
            }
        `;
        document.head.appendChild(style);
    })();
    </script>
</body>
</html>