<?php
// 定义安全常量
define('IN_BTMPS', true);
/**
 * 区域管理页面
 */
// 引入公共文件
require_once('../include/common.inc.php');

// 加载拼音转换类
require_once(INCLUDE_PATH . 'pinyin.class.php');

// 检查管理员是否登录
if (!isset($_SESSION['admin']) || $_SESSION['admin']['is_login'] !== true) {
    header("Location: login.php");
    exit;
}

// 当前页面
$current_page = 'region';

// 操作类型
$action = isset($_GET['action']) ? $_GET['action'] : 'list';

// 信息提示
$message = '';
$error = '';

// 修改模板目录为admin/template目录
$tpl->setTemplateDir(dirname(__FILE__) . '/template/');

// 处理区域操作
switch ($action) {
    // 区域列表
    case 'list':
        // 获取当前页码
        $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
        $page = max(1, $page);
        
        // 每页显示数量
        $per_page = 20;
        
        // 获取父区域ID（用于筛选）
        $parent_id = isset($_GET['parent_id']) ? intval($_GET['parent_id']) : 0; // 默认只显示一级区域
        
        // 获取关键词
        $keyword = isset($_GET['keyword']) ? trim($_GET['keyword']) : '';
        
        // 构建查询条件
        $where = "WHERE 1=1";
        $params = [];
        
        if ($parent_id > 0) {
            $where .= " AND parent_id = ?";
            $params[] = $parent_id;
        } else {
            $where .= " AND parent_id = 0"; // 默认只显示一级区域（省份）
        }
        
        if (!empty($keyword)) {
            $where .= " AND (name LIKE ? OR pinyin LIKE ?)";
            $params[] = "%{$keyword}%";
            $params[] = "%{$keyword}%";
        }
        
        // 查询总数
        $count_sql = "SELECT COUNT(*) as total FROM regions {$where}";
        $count_result = $db->query($count_sql, $params);
        $count_row = $db->fetch_array($count_result);
        $total_regions = $count_row['total'];
        
        // 计算分页
        $offset = ($page - 1) * $per_page;
        
        // 查询区域数据
        $sql = "SELECT * FROM regions {$where} ORDER BY sort_order ASC, id ASC LIMIT ?, ?";
        $params[] = $offset;
        $params[] = $per_page;
        
        $result = $db->query($sql, $params);
        $regions = [];
        
        while ($row = $db->fetch_array($result)) {
            // 添加额外信息
            $row['child_count'] = get_child_count($row['id']);
            $row['level_text'] = get_level_text($row['level']);
            $regions[] = $row;
        }
        
        // 生成分页数据
        $pagination = generate_pagination($total_regions, $page, $per_page, 5);
        
        // 构建分页链接
        $base_url = '?';
        $params = '';
        if ($parent_id > 0) {
            $params .= '&parent_id=' . $parent_id;
        }
        if (!empty($keyword)) {
            $params .= '&keyword=' . urlencode($keyword);
        }
        
        // 设置分页模板变量
        set_pagination_template_vars($pagination, $base_url, $params);
        
        // 如果是查看子区域，获取父区域信息
        $parent_region = null;
        if ($parent_id > 0) {
            $parent_sql = "SELECT * FROM regions WHERE id = ?";
            $parent_result = $db->query($parent_sql, [$parent_id]);
            if ($parent_result) {
                $parent_region = $db->fetch_array($parent_result);
            }
        }
        
        // 设置模板变量
        $tpl->assign('regions', $regions);
        $tpl->assign('parent_id', $parent_id);
        $tpl->assign('parent_region', $parent_region);
        $tpl->assign('keyword', $keyword);
        $tpl->assign('breadcrumb', '区域管理');
        break;
    
    // 添加区域
    case 'add':
        // 获取父区域ID参数
        $parent_id = isset($_GET['parent_id']) ? intval($_GET['parent_id']) : 0;
        
        // 获取父区域信息
        $parent_region = null;
        $level = 1; // 默认为省级
        
        if ($parent_id > 0) {
            $parent_sql = "SELECT * FROM regions WHERE id = ?";
            $parent_result = $db->query($parent_sql, [$parent_id]);
            if ($parent_result) {
                $parent_region = $db->fetch_array($parent_result);
                // 子区域的级别是父区域级别+1
                $level = $parent_region['level'] + 1;
            }
        }
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // 处理表单提交
            $result = save_region();
            if ($result['success']) {
                $message = '区域添加成功';
                
                // 获取选择的父区域ID，用于重定向
                $parent_id_for_redirect = isset($_POST['parent_id']) ? intval($_POST['parent_id']) : 0;
                
                // 重定向到列表页，如果设置了父区域则返回到对应的父区域列表
                header("Location: region.php?message=" . urlencode($message) . ($parent_id_for_redirect > 0 ? "&parent_id=" . $parent_id_for_redirect : ""));
                exit;
            } else {
                $error = $result['error'];
            }
        }
        
        // 获取所有省份作为下拉列表选项
        $provinces = [];
        if ($level == 1) {
            // 如果是添加省份，父级选项为空
        } else {
            // 获取所有省份
            $sql = "SELECT id, name FROM regions WHERE level = 1 ORDER BY sort_order DESC, id ASC";
            $result = $db->query($sql);
            while ($row = $db->fetch_array($result)) {
                $provinces[] = $row;
            }
        }
        
        $tpl->assign('provinces', $provinces);
        $tpl->assign('parent_region', $parent_region);
        $tpl->assign('parent_id', $parent_id);
        $tpl->assign('level', $level);
        $tpl->assign('level_text', get_level_text($level));
        break;
    
    // 编辑区域
    case 'edit':
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        if (!$id) {
            $error = '未指定区域ID';
            break;
        }
        
        // 获取区域信息
        $sql = "SELECT * FROM regions WHERE id = ?";
        $result = $db->query($sql, [$id]);
        if (!$result || $db->num_rows($result) == 0) {
            $error = '区域不存在';
            break;
        }
        
        $region = $db->fetch_array($result);
        $region['level_text'] = get_level_text($region['level']);
        
        // 获取父区域信息
        $parent_region = null;
        if ($region['parent_id'] > 0) {
            $parent_sql = "SELECT * FROM regions WHERE id = ?";
            $parent_result = $db->query($parent_sql, [$region['parent_id']]);
            if ($parent_result) {
                $parent_region = $db->fetch_array($parent_result);
            }
        }
        
        // 获取所有可选的父区域
        $provinces = [];
        if ($region['level'] > 1) {
            // 获取所有省份
            $sql = "SELECT id, name FROM regions WHERE level = 1 ORDER BY sort_order DESC, id ASC";
            $result = $db->query($sql);
            while ($row = $db->fetch_array($result)) {
                $provinces[] = $row;
            }
        }
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // 处理表单提交
            $result = save_region($id);
            if ($result['success']) {
                $message = '区域更新成功';
                
                // 获取当前区域的parent_id，用于重定向回正确的区域列表
                $region_data = get_region($id);
                $parent_id_for_redirect = ($region_data && $region_data['parent_id'] > 0) ? $region_data['parent_id'] : 0;
                
                // 重定向到列表页
                header("Location: region.php?message=" . urlencode($message) . "&parent_id=" . $parent_id_for_redirect);
                exit;
            } else {
                $error = $result['error'];
            }
        }
        
        $tpl->assign('region', $region);
        $tpl->assign('parent_region', $parent_region);
        $tpl->assign('provinces', $provinces);
        break;
    
    // 删除区域
    case 'delete':
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        if (!$id) {
            $error = '未指定区域ID';
            break;
        }
        
        // 获取当前parent_id，用于操作后返回相同视图
        $return_parent_id = isset($_GET['return_parent_id']) ? intval($_GET['return_parent_id']) : 0;
        
        // 检查区域是否有子区域
        $child_count = get_child_count($id);
        if ($child_count > 0) {
            $error = '该区域下有子区域，请先删除子区域';
            
            // 重定向到列表页显示错误
            header("Location: region.php?error=" . urlencode($error) . "&parent_id=" . $return_parent_id);
            exit;
        }
        
        // 删除区域
        $sql = "DELETE FROM regions WHERE id = ?";
        $result = $db->query($sql, [$id]);

        if ($result) {
            // 清理区域相关缓存
            if (function_exists('clearRegionCache')) {
                clearRegionCache();
            }

            $message = '区域删除成功';
            // 重定向到之前的视图
            header("Location: region.php?message=" . urlencode($message) . "&parent_id=" . $return_parent_id);
            exit;
        } else {
            $error = '删除区域失败';
            header("Location: region.php?error=" . urlencode($error) . "&parent_id=" . $return_parent_id);
            exit;
        }
        break;
    
    // 批量添加
    case 'batch':
        // 获取父区域ID参数
        $parent_id = isset($_GET['parent_id']) ? intval($_GET['parent_id']) : 0;
        
        // 获取父区域信息
        $parent_region = null;
        $level = 1; // 默认为省级
        
        if ($parent_id > 0) {
            $parent_sql = "SELECT * FROM regions WHERE id = ?";
            $parent_result = $db->query($parent_sql, [$parent_id]);
            if ($parent_result) {
                $parent_region = $db->fetch_array($parent_result);
                // 子区域的级别是父区域级别+1
                $level = $parent_region['level'] + 1;
            }
        }
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // 处理批量添加
            $result = batch_add_regions();
            if ($result['success']) {
                $message = '成功添加 ' . $result['count'] . ' 个区域';
                
                // 获取选择的父区域ID，用于重定向
                $parent_id_for_redirect = isset($_POST['parent_id']) ? intval($_POST['parent_id']) : 0;
                
                // 重定向到列表页
                header("Location: region.php?message=" . urlencode($message) . ($parent_id_for_redirect > 0 ? "&parent_id=" . $parent_id_for_redirect : ""));
                exit;
            } else {
                $error = $result['error'];
            }
        }
        
        // 获取所有省份作为下拉列表选项
        $provinces = [];
        if ($level == 1) {
            // 如果是添加省份，父级选项为空
        } else {
            // 获取所有省份
            $sql = "SELECT id, name FROM regions WHERE level = 1 ORDER BY sort_order DESC, id ASC";
            $result = $db->query($sql);
            while ($row = $db->fetch_array($result)) {
                $provinces[] = $row;
            }
        }
        
        $tpl->assign('provinces', $provinces);
        $tpl->assign('parent_region', $parent_region);
        $tpl->assign('parent_id', $parent_id);
        $tpl->assign('level', $level);
        $tpl->assign('level_text', get_level_text($level));
        break;
    
    // 保存排序
    case 'save_sort':
        // 检查是否是POST请求
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => '无效的请求方法']);
            exit;
        }

        try {
            // 获取原始POST数据
            $input = file_get_contents('php://input');
            $data = json_decode($input, true);
            
            // 检查数据格式
            if (json_last_error() !== JSON_ERROR_NONE) {
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => false, 
                    'message' => '数据格式错误: ' . json_last_error_msg(),
                    'input' => substr($input, 0, 100)
                ]);
                exit;
            }
            
            // 从解析后的数据获取排序信息和父级ID
            $sort_data = isset($data['sort_data']) ? $data['sort_data'] : [];
            $return_parent_id = isset($data['return_parent_id']) ? intval($data['return_parent_id']) : 0;
            
            if (empty($sort_data)) {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => '排序数据为空']);
                exit;
            }
            
            // 记录数据用于调试
            error_log("接收到排序数据，条数: " . count($sort_data));
            
            // 开始事务
            $db->beginTransaction();
            $updated = 0;

            // 更新每个区域的排序
            foreach ($sort_data as $item) {
                if (!isset($item['id']) || !isset($item['sort'])) {
                    continue;
                }
                
                $id = intval($item['id']);
                $sort = intval($item['sort']);
                
                if ($id > 0) {
                    // 更新排序
                    $sql = "UPDATE regions SET sort_order = ? WHERE id = ?";
                    $db->query($sql, [$sort, $id]);
                    $updated++;
                }
            }

            // 提交事务
            $db->commit();
            error_log("排序更新完成，更新了{$updated}个记录");
            
            // 返回JSON响应
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true, 
                'message' => "排序保存成功，已更新{$updated}个记录"
            ]);
            exit;
            
        } catch (Exception $e) {
            // 回滚事务
            if ($db->inTransaction()) {
                $db->rollback();
            }
            
            error_log("排序保存错误: " . $e->getMessage());
            
            // 返回JSON错误响应
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false, 
                'message' => "保存排序失败: " . $e->getMessage()
            ]);
            exit;
        }
        break;
        
    // 批量删除区域
    case 'batch_delete':
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // 获取选中的ID数组
            $region_ids = isset($_POST['region_ids']) ? $_POST['region_ids'] : [];
            $return_parent_id = isset($_POST['return_parent_id']) ? intval($_POST['return_parent_id']) : 0;
            
            if (empty($region_ids)) {
                $error = '未选择任何区域';
                header("Location: region.php?error=" . urlencode($error) . ($return_parent_id > 0 ? "&parent_id={$return_parent_id}" : ""));
                exit;
            }
            
            try {
                // 开始事务
                $db->beginTransaction();
                
                // 检查每个区域是否有子区域
                $invalid_ids = [];
                foreach ($region_ids as $id) {
                    $id = intval($id);
                    if ($id > 0) {
                        $child_count = get_child_count($id);
                        if ($child_count > 0) {
                            // 获取区域名称
                            $region = get_region($id);
                            $invalid_ids[] = $region ? $region['name'] : "ID: {$id}";
                        }
                    }
                }
                
                // 如果有带子区域的区域，不允许删除
                if (!empty($invalid_ids)) {
                    $db->rollback();
                    $error = '以下区域含有子区域，无法删除: ' . implode(', ', $invalid_ids);
                    header("Location: region.php?error=" . urlencode($error) . ($return_parent_id > 0 ? "&parent_id={$return_parent_id}" : ""));
                    exit;
                }
                
                // 删除所有选中的区域
                $placeholders = implode(',', array_fill(0, count($region_ids), '?'));
                $sql = "DELETE FROM regions WHERE id IN ({$placeholders})";
                
                $db->query($sql, $region_ids);
                
                // 提交事务
                $db->commit();
                
                $message = '批量删除成功';
                header("Location: region.php?message=" . urlencode($message) . ($return_parent_id > 0 ? "&parent_id={$return_parent_id}" : ""));
                exit;
            } catch (Exception $e) {
                // 回滚事务
                if ($db->inTransaction()) {
                    $db->rollback();
                }
                
                $error = '删除失败: ' . $e->getMessage();
                header("Location: region.php?error=" . urlencode($error) . ($return_parent_id > 0 ? "&parent_id={$return_parent_id}" : ""));
                exit;
            }
        } else {
            $error = '无效的请求方法';
            header("Location: region.php?error=" . urlencode($error));
            exit;
        }
        break;
        
    // 检查拼音是否存在
    case 'check_pinyin':
        header('Content-Type: application/json');
        $pinyin = isset($_GET['pinyin']) ? trim($_GET['pinyin']) : '';
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        
        if (empty($pinyin)) {
            echo json_encode(['valid' => false, 'message' => '拼音不能为空']);
            exit;
        }
        
        $exists = region_pinyin_exists($pinyin, $id);
        echo json_encode(['valid' => !$exists, 'message' => $exists ? '该拼音已被使用' : '']);
        exit;
        
    // 生成拼音
    case 'generate_pinyin':
        header('Content-Type: application/json');
        $name = isset($_GET['name']) ? trim($_GET['name']) : '';
        
        if (empty($name)) {
            echo json_encode(['pinyin' => '']);
            exit;
        }
        
        $pinyin = generate_region_pinyin($name);
        echo json_encode(['pinyin' => $pinyin]);
        exit;
        
    default:
        $error = '未知操作';
        break;
}

// 接收URL传递的消息
if (isset($_GET['message']) && empty($message)) {
    $message = $_GET['message'];
}

if (isset($_GET['error']) && empty($error)) {
    $error = $_GET['error'];
}

// 传递数据到模板
$tpl->assign('current_page', $current_page);
$tpl->assign('breadcrumb', '区域管理');
$tpl->assign('message', $message);
$tpl->assign('error', $error);
$tpl->assign('action', $action);
$tpl->assign('page_title', '区域管理');
$tpl->assign('admin', $_SESSION['admin']);

// 根据不同操作分配特定数据
switch ($action) {
    case 'list':
        $tpl->display('region_list.htm');
        break;
        
    case 'add':
        $tpl->display('region_edit.htm');
        break;
        
    case 'edit':
        $tpl->display('region_edit.htm');
        break;
        
    case 'batch':
        $tpl->display('region_batch.htm');
        break;
        
    default:
        $tpl->display('region_list.htm');
        break;
}

/**
 * 获取区域下的子区域数量
 */
function get_child_count($parent_id) {
    global $db;
    
    $sql = "SELECT COUNT(*) as count FROM regions WHERE parent_id = ?";
    $result = $db->query($sql, [$parent_id]);
    
    if ($result && $row = $db->fetch_array($result)) {
        return $row['count'];
    }
    
    return 0;
}

/**
 * 根据level获取级别文本
 */
function get_level_text($level) {
    switch ($level) {
        case 1:
            return '省/直辖市';
        case 2:
            return '市';
        case 3:
            return '区/县';
        default:
            return '未知';
    }
}

/**
 * 获取区域信息
 */
function get_region($id) {
    global $db;
    
    $sql = "SELECT * FROM regions WHERE id = ?";
    $result = $db->query($sql, [$id]);
    
    if ($result && $db->num_rows($result) > 0) {
        return $db->fetch_array($result);
    }
    
    return false;
}

/**
 * 检查拼音是否已存在
 */
function region_pinyin_exists($pinyin, $exclude_id = 0) {
    global $db;
    
    $sql = "SELECT id FROM regions WHERE pinyin = ?";
    $params = [$pinyin];
    
    if ($exclude_id > 0) {
        $sql .= " AND id != ?";
        $params[] = $exclude_id;
    }
    
    $result = $db->query($sql, $params);
    return $db->num_rows($result) > 0;
}

/**
 * 生成拼音
 */
function generate_region_pinyin($text) {
    // 检查是否包含中文字符
    if (!preg_match("/[\x{4e00}-\x{9fa5}]/u", $text)) {
        // 如果不包含中文，直接转为小写并返回
        $result = strtolower($text);
        $result = preg_replace('/[^a-z0-9]+/', '-', $result);
        $result = trim($result, '-');
        return $result;
    }
    
    $pinyin = new Pinyin();
    $result = $pinyin->convert($text);
    
    // 处理结果确保URL友好
    $result = strtolower($result);
    $result = preg_replace('/[^a-z0-9]+/', '-', $result);
    $result = trim($result, '-');
    
    // 如果生成的拼音为空或都是a，使用一个随机字符串
    if (empty($result) || preg_match('/^a+$/', $result)) {
        $result = 'region-' . substr(md5(uniqid()), 0, 8);
    }
    
    // 确保拼音唯一
    $original = $result;
    $counter = 1;
    
    while (region_pinyin_exists($result)) {
        $result = $original . '-' . $counter;
        $counter++;
    }
    
    return $result;
}

/**
 * 保存区域信息
 */
function save_region($id = 0) {
    global $db;
    
    // 获取表单数据
    $name = isset($_POST['name']) ? trim($_POST['name']) : '';
    $parent_id = isset($_POST['parent_id']) ? intval($_POST['parent_id']) : 0;
    $sort_order = isset($_POST['sort_order']) ? intval($_POST['sort_order']) : 0;
    $level = isset($_POST['level']) ? intval($_POST['level']) : 1;
    
    // 验证必填字段
    if (empty($name)) {
        return array('success' => false, 'error' => '请输入区域名称');
    }
    
    // 处理拼音
    $pinyin = isset($_POST['pinyin']) ? trim($_POST['pinyin']) : '';
    if (empty($pinyin)) {
        // 自动生成拼音
        $pinyin = generate_region_pinyin($name);
    }
    
    // 检查拼音是否已存在
    if (region_pinyin_exists($pinyin, $id)) {
        return array('success' => false, 'error' => "拼音'{$pinyin}'已存在，请修改");
    }
    
    // 检查是否为批量添加
    $is_batch = isset($_POST['is_batch']) && $_POST['is_batch'] == 1;
    if ($is_batch) {
        return batch_add_regions();
    }
    
    // 开始事务
    $db->beginTransaction();
    
    try {
        // 获取区域级别
        if ($parent_id > 0) {
            $parent_sql = "SELECT level FROM regions WHERE id = ?";
            $parent_result = $db->query($parent_sql, [$parent_id]);
            if ($parent_result && $row = $db->fetch_array($parent_result)) {
                $level = $row['level'] + 1;
            }
        } else {
            $level = 1; // 顶级区域为省级
        }
        
        // 执行数据库操作
        if ($id > 0) {
            // 更新现有区域
            $sql = "UPDATE regions SET name = ?, parent_id = ?, pinyin = ?, level = ?, sort_order = ? WHERE id = ?";
            $result = $db->query($sql, [$name, $parent_id, $pinyin, $level, $sort_order, $id]);
            
            if (!$result) {
                throw new Exception("更新区域失败");
            }
        } else {
            // 添加新区域
            $sql = "INSERT INTO regions (name, parent_id, pinyin, level, sort_order) VALUES (?, ?, ?, ?, ?)";
            $result = $db->query($sql, [$name, $parent_id, $pinyin, $level, $sort_order]);
            
            if (!$result) {
                throw new Exception("添加区域失败");
            }
        }
        
        // 提交事务
        $db->commit();

        // 清理区域相关缓存
        if (function_exists('clearRegionCache')) {
            clearRegionCache();
        }

        return array('success' => true);
    } catch (Exception $e) {
        // 回滚事务
        $db->rollback();
        return array('success' => false, 'error' => '保存区域失败: ' . $e->getMessage());
    }
}

/**
 * 批量添加区域
 */
function batch_add_regions() {
    global $db;
    
    // 获取提交的数据
    $parent_id = isset($_POST['parent_id']) ? intval($_POST['parent_id']) : 0;
    $regions_text = isset($_POST['regions']) ? trim($_POST['regions']) : '';
    
    // 验证数据
    if (empty($regions_text)) {
        return ['success' => false, 'error' => '区域列表不能为空'];
    }
    
    // 获取区域级别
    $level = 1; // 默认为省级
    if ($parent_id > 0) {
        $parent_sql = "SELECT level FROM regions WHERE id = ?";
        $parent_result = $db->query($parent_sql, [$parent_id]);
        if ($parent_result && $row = $db->fetch_array($parent_result)) {
            $level = $row['level'] + 1;
        }
    }
    
    // 将文本拆分为行
    $lines = explode("\n", str_replace("\r", "", $regions_text));
    $count = 0;
    
    // 开始事务
    $db->beginTransaction();
    
    try {
        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line)) {
                continue;
            }
            
            // 生成拼音
            $pinyin = generate_region_pinyin($line);
            
            // 插入区域数据
            $sql = "INSERT INTO regions (parent_id, name, pinyin, level, sort_order) VALUES (?, ?, ?, ?, 0)";
            if ($db->query($sql, [$parent_id, $line, $pinyin, $level])) {
                $count++;
            }
        }
        
        // 提交事务
        $db->commit();

        if ($count > 0) {
            // 清理区域相关缓存
            if (function_exists('clearRegionCache')) {
                clearRegionCache();
            }

            return ['success' => true, 'count' => $count];
        } else {
            return ['success' => false, 'error' => '批量添加区域失败'];
        }
    } catch (Exception $e) {
        // 回滚事务
        $db->rollback();
        return ['success' => false, 'error' => '批量添加区域失败: ' . $e->getMessage()];
    }
}

/**
 * 生成分页数据
 */
function generate_pagination($total_items, $current_page, $items_per_page, $show_pages) {
    // 计算总页数
    $total_pages = ceil($total_items / $items_per_page);
    
    // 确保当前页不超过总页数
    $current_page = min($current_page, $total_pages);
    $current_page = max(1, $current_page);
    
    // 计算起始和结束索引
    $start = ($current_page - 1) * $items_per_page + 1;
    $end = min($start + $items_per_page - 1, $total_items);
    
    // 计算显示的页码链接（前后各显示5个页码）
    $start_page = max(1, $current_page - $show_pages);
    $end_page = min($total_pages, $current_page + $show_pages);
    
    // 生成页码链接
    $page_links = [];
    for ($i = $start_page; $i <= $end_page; $i++) {
        $page_links[$i] = '?page=' . $i;
    }
    
    // 拼接分页数据
    $pagination = [
        'total_items' => $total_items,
        'items_per_page' => $items_per_page,
        'current_page' => $current_page,
        'total_pages' => $total_pages,
        'start' => $start,
        'end' => $end,
        'page_links' => $page_links,
        'first_link' => '?page=1',
        'last_link' => '?page=' . $total_pages,
        'previous_link' => str_replace('{page}', max(1, $current_page - 1), '?page={page}'),
        'next_link' => str_replace('{page}', min($total_pages, $current_page + 1), '?page={page}')
    ];
    
    return $pagination;
}

/**
 * 设置分页模板变量
 */
function set_pagination_template_vars($pagination, $base_url, $params) {
    global $tpl;
    
    // 处理分页链接
    foreach ($pagination['page_links'] as $page => $link) {
        $pagination['page_links'][$page] = $base_url . 'page=' . $page . $params;
    }
    
    // 添加参数到其他链接
    $pagination['first_link'] = $base_url . 'page=1' . $params;
    $pagination['last_link'] = $base_url . 'page=' . $pagination['total_pages'] . $params;
    $pagination['previous_link'] = $base_url . 'page=' . max(1, $pagination['current_page'] - 1) . $params;
    $pagination['next_link'] = $base_url . 'page=' . min($pagination['total_pages'], $pagination['current_page'] + 1) . $params;
    
    $tpl->assign('pagination', $pagination);
} 