<?php
/**
 * 缓存预热脚本
 * 用于定时任务自动生成首页、列表页、栏目页缓存
 * 
 * 使用方法：
 * 1. 定时任务访问：curl "http://yourdomain.com/cache_warmup.php?key=your_secret_key"
 * 2. 浏览器访问：http://www.fenlei.com/cache_warmup.php?key=123
 */

// 安全验证密钥（请修改为您自己的密钥）
$secret_key = '123';

// 验证访问权限
$provided_key = isset($_GET['key']) ? $_GET['key'] : (isset($argv[1]) ? str_replace('key=', '', $argv[1]) : '');
if ($provided_key !== $secret_key) {
    http_response_code(403);
    die('Access Denied: Invalid key');
}

// 设置执行时间限制
set_time_limit(300); // 5分钟

// 设置内存限制
ini_set('memory_limit', '256M');

// 引入系统文件
define('IN_BTMPS', true);
require_once './include/common.inc.php';

/**
 * 获取可用的模板目录
 */
function getAvailableTemplates() {
    $template_base_dir = dirname(__FILE__) . '/template/';
    $available_templates = array();

    // 检查各个模板目录是否存在
    $possible_templates = array('pc', 'm', 'wx', 'app');

    foreach ($possible_templates as $template) {
        $template_dir = $template_base_dir . $template;
        if (is_dir($template_dir)) {
            $available_templates[] = $template;
        }
    }

    // 如果没有找到任何模板目录，至少返回pc作为默认
    if (empty($available_templates)) {
        $available_templates[] = 'pc';
    }

    return $available_templates;
}

// 开始预热
$start_time = microtime(true);
$log = array();
$stats = array(
    'success' => 0,
    'failed' => 0,
    'total' => 0
);

// 输出开始信息
echo "缓存预热开始...\n";
$log[] = date('Y-m-d H:i:s') . " - 缓存预热开始";

try {
    // 1. 预热首页缓存
    warmupHomePage();
    
    // 2. 预热栏目列表页缓存
    warmupCategoryPages();

    // 3. 预热热门详情页缓存
    warmupPopularDetailPages();

    // 4. 清理过期缓存
    cleanExpiredCache();
    
} catch (Exception $e) {
    $log[] = date('Y-m-d H:i:s') . " - 预热失败: " . $e->getMessage();
    echo "预热失败: " . $e->getMessage() . "\n";
}

// 计算执行时间
$end_time = microtime(true);
$execution_time = round(($end_time - $start_time), 2);

// 输出结果
echo "\n缓存预热完成!\n";
echo "执行时间: {$execution_time}秒\n";
echo "成功: {$stats['success']} 个\n";
echo "失败: {$stats['failed']} 个\n";
echo "总计: {$stats['total']} 个\n";

$log[] = date('Y-m-d H:i:s') . " - 缓存预热完成，耗时 {$execution_time}秒";
$log[] = date('Y-m-d H:i:s') . " - 统计：成功 {$stats['success']} 个，失败 {$stats['failed']} 个";

// 保存日志
saveWarmupLog($log);

/**
 * 预热首页缓存
 */
function warmupHomePage() {
    global $config, $stats, $log;
    
    echo "预热首页缓存...\n";
    $log[] = date('Y-m-d H:i:s') . " - 开始预热首页缓存";
    
    try {
        // 获取实际存在的模板目录
        $templates = getAvailableTemplates();
        
        foreach ($templates as $template) {
            $cache_key = "index_page_{$template}";
            
            // 删除现有缓存，强制重新生成
            cache_delete($cache_key);
            
            // 模拟首页数据获取
            $indexSize = isset($config['index_size']) ? intval($config['index_size']) : 10;
            $cache_index_time = isset($config['cache_index']) ? intval($config['cache_index']) : 3600;
            
            // 获取首页数据
            $categories = $GLOBALS['cached_categories'];
            $topPosts = getTopPosts(10);
            $normalPosts = getNormalPosts($indexSize);
            
            // 缓存数据
            $cache_data = array(
                'categories' => $categories,
                'topPosts' => $topPosts,
                'normalPosts' => $normalPosts,
                'generated_at' => time()
            );
            
            cache_set($cache_key, $cache_data, $cache_index_time);
            
            $stats['success']++;
            $stats['total']++;
            
            echo "  - {$template} 首页缓存生成成功\n";
        }
        
        $log[] = date('Y-m-d H:i:s') . " - 首页缓存预热完成";
        
    } catch (Exception $e) {
        $stats['failed']++;
        $stats['total']++;
        $log[] = date('Y-m-d H:i:s') . " - 首页缓存预热失败: " . $e->getMessage();
        echo "首页缓存预热失败: " . $e->getMessage() . "\n";
    }
}

/**
 * 预热栏目列表页缓存
 */
function warmupCategoryPages() {
    global $config, $stats, $log;
    
    echo "预热栏目列表页缓存...\n";
    $log[] = date('Y-m-d H:i:s') . " - 开始预热栏目列表页缓存";
    
    try {
        $categories = $GLOBALS['cached_categories'];
        $templates = getAvailableTemplates();
        $cache_list_time = isset($config['cache_list']) ? intval($config['cache_list']) : 1800;
        $perPage = isset($config['list_page_size']) ? intval($config['list_page_size']) : 20;
        
        // 预热所有栏目（一级和二级）的第一页
        foreach ($categories as $category) {
            // 处理所有栏目，不限制parent_id
            foreach ($templates as $template) {
                $cache_key = "category_list_{$template}_{$category['id']}_1_{$perPage}_0";

                // 删除现有缓存
                cache_delete($cache_key);

                // 获取栏目数据
                $posts = getCategoryPostsDirectly($category['id'], 1, $perPage, 0, 0, false);
                $totalPosts = getCachedCategoryPostsCount($category['id'], 0, 0, false);

                // 缓存数据
                $cache_data = array(
                    'posts' => $posts,
                    'totalPosts' => $totalPosts,
                    'generated_at' => time()
                );

                cache_set($cache_key, $cache_data, $cache_list_time);

                $stats['success']++;
                $stats['total']++;
            }

            // 显示栏目层级信息
            $level_text = $category['parent_id'] == 0 ? '一级栏目' : '二级栏目';
            echo "  - {$level_text} {$category['name']} 缓存生成成功\n";
        }
        
        $log[] = date('Y-m-d H:i:s') . " - 栏目列表页缓存预热完成";
        
    } catch (Exception $e) {
        $stats['failed']++;
        $stats['total']++;
        $log[] = date('Y-m-d H:i:s') . " - 栏目列表页缓存预热失败: " . $e->getMessage();
        echo "栏目列表页缓存预热失败: " . $e->getMessage() . "\n";
    }
}



/**
 * 预热热门详情页缓存
 */
function warmupPopularDetailPages() {
    global $config, $stats, $log, $db;

    echo "预热热门详情页缓存...\n";
    $log[] = date('Y-m-d H:i:s') . " - 开始预热热门详情页缓存";

    try {
        $templates = getAvailableTemplates();
        $cache_detail_time = isset($config['cache_detail']) ? intval($config['cache_detail']) : 7200;

        // 获取最近7天的热门信息（按浏览量排序）
        $sql = "SELECT id, category_id FROM posts WHERE status = 1 AND expired_at > ? AND created_at > ? ORDER BY view_count DESC LIMIT 20";
        $recent_time = time() - (7 * 24 * 3600); // 7天前
        $result = $db->query($sql, array(time(), $recent_time));

        $popular_posts = array();
        if ($result) {
            while ($row = $db->fetch_array($result)) {
                $popular_posts[] = $row;
            }
        }

        foreach ($popular_posts as $post) {
            foreach ($templates as $template) {
                $cache_key = "detail_{$post['id']}_{$template}";

                // 删除现有缓存，强制重新生成
                cache_delete($cache_key);

                // 获取详情数据
                $detail = getPostDetail($post['id']);
                if ($detail) {
                    $images = getCachedPostImages($post['id']);
                    $relatedPosts = getCachedRelatedPosts($post['category_id'], $post['id'], 5);
                    $categoryInfo = getCategoryInfo($post['category_id']);

                    // 缓存数据（不包含浏览次数，因为需要实时显示）
                    $cache_data = array(
                        'post' => $detail,
                        'images' => $images,
                        'related_posts' => $relatedPosts,
                        'category_info' => $categoryInfo,
                        'cached_at' => time()
                    );

                    cache_set($cache_key, $cache_data, $cache_detail_time);

                    $stats['success']++;
                    $stats['total']++;
                }
            }

            echo "  - 信息 ID:{$post['id']} 详情页缓存生成成功\n";
        }

        $log[] = date('Y-m-d H:i:s') . " - 热门详情页缓存预热完成";

    } catch (Exception $e) {
        $stats['failed']++;
        $stats['total']++;
        $log[] = date('Y-m-d H:i:s') . " - 热门详情页缓存预热失败: " . $e->getMessage();
        echo "热门详情页缓存预热失败: " . $e->getMessage() . "\n";
    }
}

/**
 * 清理过期缓存
 */
function cleanExpiredCache() {
    global $log;

    echo "清理过期缓存...\n";
    $log[] = date('Y-m-d H:i:s') . " - 开始清理过期缓存";

    try {
        $cache_dir = ROOT_PATH . 'data/cache/';
        $cleaned = 0;
        $expire_time = time() - (24 * 3600); // 24小时前

        if (is_dir($cache_dir)) {
            $files = glob($cache_dir . '*.cache');
            foreach ($files as $file) {
                $filename = basename($file);

                // 跳过基础数据缓存（地区、栏目等）
                if (strpos($filename, 'region_') === 0 ||
                    strpos($filename, 'category_all') === 0) {
                    continue;
                }

                // 清理过期文件
                if (filemtime($file) < $expire_time) {
                    if (@unlink($file)) {
                        $cleaned++;
                    }
                }
            }
        }

        echo "  - 清理了 {$cleaned} 个过期缓存文件\n";
        $log[] = date('Y-m-d H:i:s') . " - 清理过期缓存完成，清理了 {$cleaned} 个文件";

    } catch (Exception $e) {
        $log[] = date('Y-m-d H:i:s') . " - 清理过期缓存失败: " . $e->getMessage();
        echo "清理过期缓存失败: " . $e->getMessage() . "\n";
    }
}

/**
 * 保存预热日志
 */
function saveWarmupLog($log) {
    $log_file = ROOT_PATH . 'data/cache_warmup.log';
    $log_content = implode("\n", $log) . "\n\n";
    
    // 保持日志文件不超过1MB
    if (file_exists($log_file) && filesize($log_file) > 1024 * 1024) {
        // 只保留最后50%的内容
        $old_content = file_get_contents($log_file);
        $lines = explode("\n", $old_content);
        $keep_lines = array_slice($lines, count($lines) / 2);
        file_put_contents($log_file, implode("\n", $keep_lines));
    }
    
    file_put_contents($log_file, $log_content, FILE_APPEND | LOCK_EX);
}
?>
