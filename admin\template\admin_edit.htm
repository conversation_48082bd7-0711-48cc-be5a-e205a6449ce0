{include file="header.htm"}

<style>
    .form-group {
        margin-bottom: 15px;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
    }
    .form-label {
        display: block;
        margin-bottom: 0;
        font-weight: 500;
        width: 120px;
        text-align: right;
        padding-right: 15px;
        color: #666;
        line-height: 32px;
    }
    .form-field {
        flex: 0 0 auto;
        min-width: 300px;
        max-width: 350px;
    }
    .form-hint {
        flex: 1;
        margin-left: 15px;
        font-size: 12px;
        color: var(--text-secondary);
        padding-top: 10px;
    }
    .form-control {
        display: block;
        width: 100%;
        padding: 8px 12px;
        font-size: 14px;
        line-height: 1.5;
        color: #333;
        background-color: #fff;
        border: 1px solid var(--border-color);
        border-radius: 4px;
        transition: border-color 0.15s ease-in-out;
    }
    .form-control:focus {
        border-color: var(--primary-color);
        outline: 0;
    }
    .btn-light-primary {
        background-color: #e6f0ff;
        color: #1b68ff;
        border: 1px solid #cce0ff;
    }
    .btn-light-primary:hover {
        background-color: #d1e3ff;
        color: #0056b3;
    }
    .btn-light-secondary {
        background-color: #f0f0f0;
        color: #666666;
        border: 1px solid #dddddd;
    }
    .btn-light-secondary:hover {
        background-color: #e0e0e0;
        color: #444444;
    }
    @media (max-width: 992px) {
        .form-group {
            flex-direction: column;
            align-items: flex-start;
        }
        .form-label {
            width: 100%;
            text-align: left;
            margin-bottom: 5px;
            padding-right: 0;
        }
        .form-field {
            width: 100%;
            max-width: 100%;
        }
        .form-hint {
            margin-left: 0;
            margin-top: 5px;
            width: 100%;
        }
    }
</style>

<div class="section">
    <!-- 消息提示 -->
    {if $message}
    <div class="alert alert-success">
        <i class="fas fa-check-circle"></i>
        <div>
            <p>{$message}</p>
        </div>
    </div>
    {/if}
    
    {if $error}
    <div class="alert alert-danger">
        <i class="fas fa-exclamation-circle"></i>
        <div>
            <p>{$error}</p>
        </div>
    </div>
    {/if}
    
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div>
                <h3 class="card-title">{if $action == 'add'}添加管理员{else}编辑管理员{/if}</h3>
                <p class="card-subtitle">{if $action == 'add'}创建新的系统管理账号{else}修改管理员信息{/if}</p>
            </div>
            <div>
                <a href="admin.php" class="btn btn-light-secondary">
                    <i class="fas fa-arrow-left"></i> 返回列表
                </a>
            </div>
        </div>
            
        <div class="card-body">
            <form action="admin.php?action={$action}{if $action == 'edit'}&id={$admin_data.id}{/if}" method="post">
                <div class="form-group">
                    <label class="form-label" for="username">用户名 <span class="text-danger">*</span></label>
                    <div class="form-field">
                        <input type="text" class="form-control" id="username" name="username" value="{if isset($admin_data.username)}{$admin_data.username}{/if}" required>
                    </div>
                    <span class="form-hint">用户名将用于登录系统，设置后不建议频繁更改</span>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="realname">真实姓名</label>
                    <div class="form-field">
                        <input type="text" class="form-control" id="realname" name="realname" value="{if isset($admin_data.realname)}{$admin_data.realname}{/if}">
                    </div>
                    <span class="form-hint">管理员的真实姓名或昵称，便于标识</span>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="password">{if $action == 'add'}密码 <span class="text-danger">*</span>{else}新密码{/if}</label>
                    <div class="form-field">
                        <input type="password" class="form-control" id="password" name="password" {if $action == 'add'}required{/if}>
                    </div>
                    <span class="form-hint">{if $action == 'add'}请设置登录密码{else}如需修改密码请在此输入新密码，留空表示不修改{/if}</span>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="confirm_password">{if $action == 'add'}确认密码 <span class="text-danger">*</span>{else}确认新密码{/if}</label>
                    <div class="form-field">
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" {if $action == 'add'}required{/if}>
                    </div>
                    <span class="form-hint">请再次输入密码以确认</span>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="role">角色</label>
                    <div class="form-field">
                        <select class="form-control" id="role" name="role">
                            <option value="admin" {if isset($admin_data.role) && $admin_data.role == 'admin' || !isset($admin_data.role)}selected{/if}>普通管理员</option>
                            <option value="super_admin" {if isset($admin_data.role) && $admin_data.role == 'super_admin'}selected{/if}>超级管理员</option>
                        </select>
                    </div>
                    <span class="form-hint">不同角色拥有不同的权限</span>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="status">状态</label>
                    <div class="form-field">
                        <select class="form-control" id="status" name="status">
                            <option value="1" {if !isset($admin_data.status) || $admin_data.status == 1}selected{/if}>正常</option>
                            <option value="0" {if isset($admin_data.status) && $admin_data.status == 0}selected{/if}>禁用</option>
                        </select>
                    </div>
                    <span class="form-hint">禁用状态的管理员无法登录系统</span>
                </div>
                
                {if $action == 'edit'}
                <div class="form-group">
                    <label class="form-label">创建时间</label>
                    <div class="form-field">
                        <p class="form-control-static">{if isset($admin_data.created_at) && $admin_data.created_at}{$admin_data.created_at}{else}--{/if}</p>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">最后登录时间</label>
                    <div class="form-field">
                        <p class="form-control-static">{if isset($admin_data.last_login) && $admin_data.last_login}{$admin_data.last_login}{else}从未登录{/if}</p>
                    </div>
                </div>
                {/if}
                
                <div class="form-group">
                    <div class="form-field" style="margin-left: 120px; margin-top: 20px;">
                        <button type="submit" class="btn btn-light-primary">
                            <i class="fas fa-save"></i> {if $action == 'add'}添加{else}保存{/if}
                        </button>
                        <a href="admin.php" class="btn btn-light-secondary" style="margin-left: 10px;">
                            <i class="fas fa-times"></i> 取消
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

{include file="footer.htm"} 