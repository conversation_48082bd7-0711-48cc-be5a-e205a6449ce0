<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>系统提示 - {$site_name}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="static/css/bootstrap.min.css">
    <link rel="stylesheet" href="static/css/font-awesome.min.css">
    <link rel="stylesheet" href="static/css/admin.css">
    <script src="static/js/jquery.min.js"></script>
    <script src="static/js/bootstrap.min.js"></script>
    <style>
        .message-box {
            max-width: 500px;
            margin: 100px auto;
            background: #fff;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            padding: 30px;
            text-align: center;
        }
        .message-icon {
            font-size: 48px;
            margin-bottom: 20px;
            color: #3498db;
        }
        .message-content {
            font-size: 18px;
            margin-bottom: 20px;
            color: #333;
        }
        .message-info {
            font-size: 14px;
            color: #666;
            margin-bottom: 20px;
        }
        .progress {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="message-box">
            <div class="message-icon">
                <i class="fas fa-info-circle"></i>
            </div>
            <div class="message-content">
                {$message}
            </div>
            <div class="progress">
                <div class="progress-bar progress-bar-striped active" role="progressbar" style="width: 100%"></div>
            </div>
            <div class="message-info">
                <span id="wait">{$wait}</span> 秒后自动跳转，如果没有跳转，请点击 <a href="{$url}">这里</a>
            </div>
        </div>
    </div>
    
    <script>
        $(function() {
            var wait = {$wait};
            var interval = setInterval(function() {
                wait--;
                $('#wait').text(wait);
                if (wait <= 0) {
                    clearInterval(interval);
                    window.location.href = '{$url}';
                }
            }, 1000);
            
            // 设置进度条
            var progress = $('.progress-bar');
            var width = 100;
            var step = 100 / {$wait};
            var progressInterval = setInterval(function() {
                width -= step;
                progress.css('width', width + '%');
                if (width <= 0) {
                    clearInterval(progressInterval);
                }
            }, 1000);
        });
    </script>
</body>
</html> 