{include file="header.htm"}

<!-- 页面标题 -->
<div class="page-title">
    <h1>操作日志管理</h1>
    <div class="d-flex gap-2">
        <a href="?op=export{$url_params}" class="btn btn-sm btn-outline">
            <i class="fas fa-download"></i>
            <span>导出日志</span>
        </a>
    </div>
</div>

<!-- 统计信息 -->
<div class="section">
    <div class="card">
        <div class="stats-compact">
            <div class="stat-compact">
                <span class="stat-number">{if isset($today_stats.total_today)}{$today_stats.total_today}{else}0{/if}</span>
                <span class="stat-text">今日操作</span>
            </div>
            <div class="stat-compact">
                <span class="stat-number">{if isset($today_stats.unique_ips_today)}{$today_stats.unique_ips_today}{else}0{/if}</span>
                <span class="stat-text">今日活跃IP</span>
            </div>
            <div class="stat-compact">
                <span class="stat-number">{if isset($today_stats.active_users_today)}{$today_stats.active_users_today}{else}0{/if}</span>
                <span class="stat-text">今日活跃用户</span>
            </div>
            <div class="stat-compact">
                <span class="stat-number">{if isset($logs_data.total)}{$logs_data.total}{else}0{/if}</span>
                <span class="stat-text">日志总数</span>
            </div>
        </div>
    </div>
</div>

<!-- 消息提示 -->
{if $message}
<div class="alert alert-success">
    <i class="fas fa-check-circle"></i>
    <div>{$message}</div>
</div>
{/if}

{if $error}
<div class="alert alert-danger">
    <i class="fas fa-exclamation-circle"></i>
    <div>{$error}</div>
</div>
{/if}

<!-- 筛选表单 -->
<div class="section">
    <div class="card">
        <h3 class="card-title">
            筛选条件
            <span class="record-count">共 {if isset($logs_data.total)}{$logs_data.total}{else}0{/if} 条记录</span>
        </h3>
        <div class="filter-form-expanded">
            <form method="GET" action="">
                <!-- 第一行：操作类型、目标类型 -->
                <div class="filter-row-expanded">
                    <div class="filter-item-expanded">
                        <label>操作类型:</label>
                        <select name="operation_type" class="form-control">
                            <option value="">全部</option>
                            <option value="create" {if isset($filter_params.operation_type) && $filter_params.operation_type == 'create'}selected{/if}>创建</option>
                            <option value="update" {if isset($filter_params.operation_type) && $filter_params.operation_type == 'update'}selected{/if}>修改</option>
                            <option value="delete" {if isset($filter_params.operation_type) && $filter_params.operation_type == 'delete'}selected{/if}>删除</option>
                            <option value="login" {if isset($filter_params.operation_type) && $filter_params.operation_type == 'login'}selected{/if}>登录</option>
                            <option value="logout" {if isset($filter_params.operation_type) && $filter_params.operation_type == 'logout'}selected{/if}>登出</option>
                        </select>
                    </div>

                    <div class="filter-item-expanded">
                        <label>目标类型:</label>
                        <select name="target_type" class="form-control">
                            <option value="">全部</option>
                            <option value="post" {if isset($filter_params.target_type) && $filter_params.target_type == 'post'}selected{/if}>信息</option>
                            <option value="news" {if isset($filter_params.target_type) && $filter_params.target_type == 'news'}selected{/if}>新闻</option>
                            <option value="category" {if isset($filter_params.target_type) && $filter_params.target_type == 'category'}selected{/if}>分类</option>
                            <option value="admin" {if isset($filter_params.target_type) && $filter_params.target_type == 'admin'}selected{/if}>管理员</option>
                            <option value="auth" {if isset($filter_params.target_type) && $filter_params.target_type == 'auth'}selected{/if}>认证</option>
                        </select>
                    </div>

                    <div class="filter-item-expanded">
                        <label>IP地址:</label>
                        <input type="text" name="ip_address" value="{if isset($filter_params.ip_address)}{$filter_params.ip_address}{/if}"
                               placeholder="IP地址" class="form-control">
                    </div>

                    <div class="filter-item-expanded">
                        <label>关键词:</label>
                        <input type="text" name="keyword" value="{if isset($filter_params.keyword)}{$filter_params.keyword}{/if}"
                               placeholder="标题、描述、用户名" class="form-control">
                    </div>
                </div>

                <!-- 第二行：时间筛选 -->
                <div class="filter-row-expanded">
                    <div class="filter-item-expanded">
                        <label>开始日期:</label>
                        <input type="date" name="start_date" value="{if isset($filter_params.start_date)}{$filter_params.start_date}{/if}" class="form-control">
                    </div>

                    <div class="filter-item-expanded">
                        <label>结束日期:</label>
                        <input type="date" name="end_date" value="{if isset($filter_params.end_date)}{$filter_params.end_date}{/if}" class="form-control">
                    </div>

                    <div class="filter-item-expanded">
                        <!-- 占位 -->
                    </div>
                </div>

                <!-- 第三行：操作按钮 -->
                <div class="filter-row-expanded">
                    <div class="filter-buttons-expanded">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i>
                            筛选
                        </button>
                        <a href="operation_logs.php" class="btn btn-outline">
                            <i class="fas fa-undo"></i>
                            重置
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 日志列表 -->
<div class="section">
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">日志列表</h3>
        </div>

        <!-- 批量操作表单 -->
        <form id="batchForm" method="POST" action="?op=batch_delete">
            <!-- 日志表格 -->
            <div class="table-responsive">
                <table class="table table-logs">
                    <thead>
                        <tr>
                            <th style="width: 40px;"><input type="checkbox" id="selectAllHeader"></th>
                            <th style="width: 60px;">ID</th>
                            <th style="width: 120px;">用户</th>
                            <th style="width: 80px;">操作</th>
                            <th style="width: 80px;">目标</th>
                            <th style="width: 70px;">目标ID</th>
                            <th style="min-width: 200px;">操作描述</th>
                            <th style="width: 160px;">IP地址</th>
                            <th style="width: 60px;">状态</th>
                            <th style="width: 90px;">时间</th>
                            <th style="width: 70px;">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {if $logs_data.data && count($logs_data.data) > 0}
                            {foreach from=$logs_data.data item=log}
                            <tr>
                                <td><input type="checkbox" name="ids[]" value="{$log.id}" class="log-checkbox"></td>
                                <td>{$log.id}</td>
                                <td title="用户ID: {$log.user_id}">
                                    <strong style="color: var(--text-color);">{$log.username}</strong>
                                    <small style="color: var(--text-secondary); margin-left: 6px;">({$log.user_type})</small>
                                </td>
                                <td>
                                    <span class="badge badge-{$log.operation_type}">
                                        {$log.operation_type}
                                    </span>
                                </td>
                                <td>{$log.target_type}</td>
                                <td>{if $log.target_id > 0}{$log.target_id}{else}-{/if}</td>
                                <td title="{$log.operation_desc}">
                                    {if $log.target_title}
                                        <strong style="color: var(--text-color);">{$log.target_title}</strong>
                                        <span style="color: var(--text-secondary); margin-left: 8px;">{$log.operation_desc_short}</span>
                                    {else}
                                        <span style="color: var(--text-color);">{$log.operation_desc_short}</span>
                                    {/if}
                                </td>
                                <td>
                                    {$log.ip_address}{if $log.port > 0}:{$log.port}{/if}
                                </td>
                                <td>
                                    <span class="badge badge-success">成功</span>
                                </td>
                                <td>
                                    {$log.formatted_time}
                                </td>
                                <td>
                                    <a href="?op=delete&id={$log.id}"
                                       onclick="return confirm('确定要删除这条日志吗？')"
                                       class="btn btn-sm btn-light-danger">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </td>
                            </tr>
                            {/foreach}
                        {else}
                            <tr>
                                <td colspan="11" class="text-center text-muted py-4">暂无日志记录</td>
                            </tr>
                        {/if}
                    </tbody>
                </table>
            </div>

            <!-- 批量操作按钮 -->
            <div class="batch-actions-bottom">
                <label class="batch-select-all">
                    <input type="checkbox" id="selectAll"> 全选
                </label>
                <div class="batch-buttons">
                    <button type="button" onclick="batchDelete()" class="btn btn-sm btn-light-danger">
                        <i class="fas fa-trash"></i>
                        <span>批量删除</span>
                    </button>
                    <button type="button" onclick="showCleanDialog()" class="btn btn-sm btn-warning">
                        <i class="fas fa-broom"></i>
                        <span>清理旧日志</span>
                    </button>
                </div>
            </div>
        </form>

        <!-- 分页 -->
        {if $logs_data.total_pages > 1}
        <div class="pagination-wrapper">
            {$pagination_html}
            <div class="pagination-info">
                共 {$logs_data.total} 条记录，每页 {$logs_data.page_size} 条，共 {$logs_data.total_pages} 页，当前第 {$logs_data.page} 页
            </div>
        </div>
        {/if}
    </div>
</div>

<!-- 清理对话框 -->
<div class="modal" id="cleanDialog" style="display:none;">
    <div class="modal-content">
        <div class="modal-header">
            <h4>清理旧日志</h4>
            <button type="button" class="modal-close" onclick="hideCleanDialog()">&times;</button>
        </div>
        <form method="POST" action="?op=clean">
            <div class="modal-body">
                <div class="form-group">
                    <label>保留最近
                        <input type="number" name="days" value="90" min="7" max="365" class="form-control d-inline" style="width:80px;">
                        天的日志
                    </label>
                </div>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <div>注意：此操作将永久删除超过指定天数的日志记录，无法恢复。</div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" onclick="hideCleanDialog()" class="btn btn-outline">取消</button>
                <button type="submit" onclick="return confirm('确定要清理旧日志吗？此操作无法恢复！')" class="btn btn-danger">确定清理</button>
            </div>
        </form>
    </div>
</div>

<script>
// 全选功能
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.log-checkbox');
    checkboxes.forEach(cb => cb.checked = this.checked);
});

document.getElementById('selectAllHeader').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.log-checkbox');
    checkboxes.forEach(cb => cb.checked = this.checked);
    document.getElementById('selectAll').checked = this.checked;
});

// 批量删除
function batchDelete() {
    const checked = document.querySelectorAll('.log-checkbox:checked');
    if (checked.length === 0) {
        alert('请选择要删除的日志记录');
        return;
    }
    
    if (confirm(`确定要删除选中的 ${checked.length} 条日志记录吗？`)) {
        document.getElementById('batchForm').submit();
    }
}

// 显示清理对话框
function showCleanDialog() {
    document.getElementById('cleanDialog').style.display = 'block';
}

// 隐藏清理对话框
function hideCleanDialog() {
    document.getElementById('cleanDialog').style.display = 'none';
}

// 点击对话框外部关闭
document.getElementById('cleanDialog').addEventListener('click', function(e) {
    if (e.target === this) {
        hideCleanDialog();
    }
});
</script>

{include file="footer.htm"}
