{include file="header.htm"}

<div class="page-title">
    <h1>友情链接管理</h1>
    <small style="margin-left: 15px; color: var(--text-secondary); font-weight: normal; font-size: 13px;">
        <i class="fas fa-link" style="color: var(--primary-color);"></i> {$pagination.total_items} 个友情链接
    </small>
</div>

<style>
/* 搜索区域样式 */
.search-container {
    margin-bottom: 0;
}

.search-input-group {
    display: flex;
    align-items: center;
    gap: 8px;
    position: relative;
}

.search-icon {
    position: absolute;
    left: 12px;
    color: #6c757d;
    z-index: 10;
}

.search-input {
    padding-left: 35px !important;
    border-radius: 6px;
    border: 1px solid #dee2e6;
    flex: 1;
}

.search-input:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.search-btn {
    border-radius: 6px;
    padding: 8px 16px;
    white-space: nowrap;
}

.clear-btn {
    border-radius: 6px;
    padding: 8px 12px;
    white-space: nowrap;
}

/* 表格样式 */
.compact-table {
    font-size: 14px;
    margin-bottom: 0;
}

.compact-table th {
    background-color: #f8f9fa;
    border-color: #dee2e6;
    font-weight: 600;
    color: #495057;
    padding: 12px 8px;
    vertical-align: middle;
}

.compact-table td {
    padding: 10px 8px;
    vertical-align: middle;
    border-color: #dee2e6;
}

.compact-table tbody tr:hover {
    background-color: #f8f9fa;
}

/* 链接名称样式 */
.link-name {
    display: flex;
    align-items: center;
    font-weight: 500;
}

/* 链接URL样式 */
.link-url a {
    color: #6c757d;
    font-size: 13px;
}

.link-url a:hover {
    color: #0d6efd;
}

/* 徽章样式 */
.badge {
    font-size: 11px;
    padding: 4px 8px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 50px;
    text-align: center;
    vertical-align: middle;
}

/* 按钮组样式 */
.btn-group .btn {
    border-radius: 4px;
    margin-right: 2px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .search-input-group {
        flex-direction: column;
        align-items: stretch;
    }

    .search-input {
        margin-bottom: 8px;
    }

    .compact-table {
        font-size: 12px;
    }

    .compact-table th,
    .compact-table td {
        padding: 8px 4px;
    }
}
.info-actions .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    white-space: nowrap;
    text-decoration: none !important;
}
.info-actions .btn-group {
    display: inline-block;
}
a, a:hover, a:focus, a:active {
    text-decoration: none !important;
}
.nav-link {
    text-decoration: none !important;
}

.link-logo {
    width: 32px;
    height: 32px;
    object-fit: cover;
    border-radius: 4px;
}

.link-url {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.status-badge {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* 表格单元格对齐 */
.table td {
    vertical-align: middle;
}

.table .text-center {
    text-align: center !important;
}

.table .text-muted {
    color: #6c757d !important;
}

.status-active {
    background-color: #d4edda;
    color: #155724;
}

.status-inactive {
    background-color: #f8d7da;
    color: #721c24;
}

/* 模态框修复样式 */
.modal {
    z-index: 1050;
}

.modal-backdrop {
    z-index: 1040;
}

.modal.show {
    display: block !important;
}

.modal:not(.show) {
    display: none !important;
}

/* 确保关闭按钮可点击 */
.btn-close {
    position: relative;
    z-index: 1060;
    cursor: pointer;
}

/* 按钮间距 */
.d-flex.gap-2 > * + * {
    margin-left: 0.5rem;
}

/* 卡片头部优化 */
.card-header {
    padding: 1rem 1.25rem;
    border-bottom: 1px solid #dee2e6;
}

.card-title {
    font-size: 1.1rem;
    font-weight: 600;
}

</style>

{if isset($message)}
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="fas fa-check-circle"></i>
    {$message}
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
{/if}

{if isset($error)}
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-circle"></i>
    {$error}
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
{/if}

<!-- 友情链接列表 -->
<div class="section">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h3 class="card-title mb-0">
                <i class="fas fa-link"></i>
                友情链接管理
            </h3>
            <div class="d-flex gap-2">
                <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addLinkModal">
                    <i class="fas fa-plus"></i>
                    添加友情链接
                </button>
                <button type="button" class="btn btn-danger btn-sm" onclick="batchDelete()">
                    <i class="fas fa-trash"></i>
                    批量删除
                </button>
            </div>
        </div>
        <form id="list-form" action="links.php?action=batch_delete" method="post">
            <div class="table-responsive">
                <table class="table table-vcenter table-striped table-hover compact-table">
                    <thead class="table-light">
                        <tr>
                            <th width="40"><input type="checkbox" id="check-all"></th>
                            <th width="50">ID</th>
                            <th>链接名称</th>
                            <th>链接地址</th>
                            <th>描述</th>
                            <th width="60">排序</th>
                            <th width="70">状态</th>
                            <th width="80">打开方式</th>
                            <th width="100">创建时间</th>
                            <th width="120">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {if !$links}
                        <tr>
                            <td colspan="10" class="text-center text-muted py-4">
                                <i class="fas fa-link fa-2x mb-2"></i>
                                <div>暂无友情链接数据</div>
                            </td>
                        </tr>
                        {else}
                        {loop $links $item}
                        <tr>
                            <td><input type="checkbox" name="link_ids[]" value="{$item.id}" class="check-item"></td>
                            <td class="text-muted">{$item.id}</td>
                            <td>
                                <div class="link-name">
                                    <i class="fas fa-link text-primary me-1"></i>
                                    <strong>{$item.name}</strong>
                                </div>
                            </td>
                            <td>
                                <div class="link-url">
                                    <a href="{$item.url}" target="_blank" rel="nofollow" class="text-decoration-none" title="{$item.url}">
                                        <i class="fas fa-external-link-alt text-muted me-1"></i>
                                        {php}echo mb_strlen($item['url']) > 40 ? mb_substr($item['url'], 0, 40) . '...' : $item['url'];{/php}
                                    </a>
                                </div>
                            </td>
                            <td class="text-muted">
                                {if $item.description}
                                    {php}echo mb_strlen($item['description']) > 30 ? mb_substr($item['description'], 0, 30) . '...' : $item['description'];{/php}
                                {else}
                                    <span class="text-muted">--</span>
                                {/if}
                            </td>
                            <td class="text-center">
                                <span class="badge bg-secondary">{$item.sort_order}</span>
                            </td>
                            <td class="text-center">
                                {if $item.status == 1}
                                    <span class="badge bg-success">启用</span>
                                {else}
                                    <span class="badge bg-danger">禁用</span>
                                {/if}
                            </td>
                            <td class="text-center">
                                {if $item.target == '_blank'}
                                    <span class="badge bg-info">新窗口</span>
                                {else}
                                    <span class="badge bg-secondary">当前窗口</span>
                                {/if}
                            </td>
                            <td class="text-muted small">
                                {php}echo date('m-d H:i', $item['created_at']);{/php}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-outline-primary" title="编辑" onclick="editLink('{$item.id}')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <a href="links.php?action=toggle_status&id={$item.id}" class="btn btn-sm btn-outline-{if $item.status == 1}warning{else}success{/if}" title="{if $item.status == 1}禁用{else}启用{/if}" onclick="return confirm('确定要{if $item.status == 1}禁用{else}启用{/if}这个友情链接吗？')">
                                        <i class="fas fa-{if $item.status == 1}eye-slash{else}eye{/if}"></i>
                                    </a>
                                    <a href="links.php?action=delete&id={$item.id}" class="btn btn-sm btn-outline-danger" title="删除" onclick="return confirm('确定要删除这个友情链接吗？')">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {/loop}
                        {/if}
                    </tbody>
                </table>
            </div>

            {if $links}
            <div class="table-footer">
                <div class="row align-items-center">
                    <div class="col-md-12">
                        {if $pagination.total_pages > 1}
                        <nav aria-label="分页导航">
                            <ul class="pagination pagination-sm justify-content-end mb-0">
                                {if $pagination.has_prev}
                                <li class="page-item">
                                    <a class="page-link" href="links.php?action=list&page={$pagination.prev_page}{if $keyword}&keyword={$keyword}{/if}">上一页</a>
                                </li>
                                {/if}

                                {php}
                                for ($i = 1; $i <= $pagination['total_pages']; $i++) {
                                    $active = ($i == $pagination['current_page']) ? 'active' : '';
                                    $url = "links.php?action=list&page=" . $i;
                                    if (!empty($keyword)) {
                                        $url .= "&keyword=" . urlencode($keyword);
                                    }
                                    echo "<li class=\"page-item " . $active . "\"><a class=\"page-link\" href=\"" . $url . "\">" . $i . "</a></li>";
                                }
                                {/php}

                                {if $pagination.has_next}
                                <li class="page-item">
                                    <a class="page-link" href="links.php?action=list&page={$pagination.next_page}{if $keyword}&keyword={$keyword}{/if}">下一页</a>
                                </li>
                                {/if}
                            </ul>
                        </nav>
                        {/if}
                    </div>
                </div>
            </div>
            {/if}
        </form>
    </div>
</div>

<!-- 添加友情链接弹层 -->
<div class="modal fade" id="addLinkModal" tabindex="-1" aria-labelledby="addLinkModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addLinkModalLabel">
                    <i class="fas fa-plus-circle"></i> 添加友情链接
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addLinkForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">链接名称 <span class="text-danger">*</span></label>
                                <input type="text" name="name" class="form-control" placeholder="请输入链接名称" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">链接地址 <span class="text-danger">*</span></label>
                                <input type="url" name="url" class="form-control" placeholder="https://www.example.com" required>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">链接描述</label>
                        <textarea name="description" class="form-control" rows="3" placeholder="请输入链接描述（可选）"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">排序</label>
                                <input type="number" name="sort_order" class="form-control" value="0" min="0">
                                <small class="text-muted">数字越小排序越靠前</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">打开方式</label>
                                <select name="target" class="form-control">
                                    <option value="_blank">新窗口打开</option>
                                    <option value="_self">当前窗口打开</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">状态</label>
                                <select name="status" class="form-control">
                                    <option value="1">启用</option>
                                    <option value="0">禁用</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" onclick="$('#addLinkModal').modal('hide')">
                    <i class="fas fa-times"></i> 取消
                </button>
                <button type="button" class="btn btn-primary" onclick="submitAddForm()">
                    <i class="fas fa-save"></i> 保存
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 编辑友情链接弹层 -->
<div class="modal fade" id="editLinkModal" tabindex="-1" aria-labelledby="editLinkModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editLinkModalLabel">
                    <i class="fas fa-edit"></i> 编辑友情链接
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editLinkForm">
                    <input type="hidden" name="id" id="edit_id">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">链接名称 <span class="text-danger">*</span></label>
                                <input type="text" name="name" id="edit_name" class="form-control" placeholder="请输入链接名称" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">链接地址 <span class="text-danger">*</span></label>
                                <input type="url" name="url" id="edit_url" class="form-control" placeholder="https://www.example.com" required>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">链接描述</label>
                        <textarea name="description" id="edit_description" class="form-control" rows="3" placeholder="请输入链接描述（可选）"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">排序</label>
                                <input type="number" name="sort_order" id="edit_sort_order" class="form-control" value="0" min="0">
                                <small class="text-muted">数字越小排序越靠前</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">打开方式</label>
                                <select name="target" id="edit_target" class="form-control">
                                    <option value="_blank">新窗口打开</option>
                                    <option value="_self">当前窗口打开</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">状态</label>
                                <select name="status" id="edit_status" class="form-control">
                                    <option value="1">启用</option>
                                    <option value="0">禁用</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" onclick="$('#editLinkModal').modal('hide')">
                    <i class="fas fa-times"></i> 取消
                </button>
                <button type="button" class="btn btn-primary" onclick="submitEditForm()">
                    <i class="fas fa-save"></i> 保存
                </button>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // 确保所有模态框都是隐藏状态
    $('#addLinkModal').modal('hide');
    $('#editLinkModal').modal('hide');

    // 全选/取消全选
    $('#check-all').change(function() {
        $('.check-item').prop('checked', $(this).prop('checked'));
    });

    // 单个复选框变化时检查全选状态
    $('.check-item').change(function() {
        var total = $('.check-item').length;
        var checked = $('.check-item:checked').length;
        $('#check-all').prop('checked', total === checked);
    });

    // 检查URL参数，如果有show_add_modal参数则显示添加弹窗
    var urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('show_add_modal') === '1') {
        setTimeout(function() {
            $('#addLinkModal').modal('show');
        }, 100);
        // 清除URL参数
        window.history.replaceState({}, document.title, window.location.pathname);
    }
});

// 批量删除
function batchDelete() {
    var checkedItems = $('.check-item:checked');
    if (checkedItems.length === 0) {
        alert('请选择要删除的友情链接');
        return false;
    }

    if (confirm('确定要删除选中的 ' + checkedItems.length + ' 个友情链接吗？此操作不可恢复！')) {
        $('#list-form').submit();
    }
}

// 提交添加表单
function submitAddForm() {
    var form = $('#addLinkForm');
    var formData = new FormData(form[0]);

    // 表单验证
    var name = form.find('input[name="name"]').val().trim();
    var url = form.find('input[name="url"]').val().trim();

    if (!name) {
        alert('请输入链接名称');
        form.find('input[name="name"]').focus();
        return;
    }

    if (!url) {
        alert('请输入链接地址');
        form.find('input[name="url"]').focus();
        return;
    }

    // 禁用提交按钮
    var submitBtn = $('.modal-footer .btn-primary');
    var originalText = submitBtn.html();
    submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 保存中...');

    // 发送AJAX请求
    $.ajax({
        url: 'links.php?action=save',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                // 关闭弹层
                $('#addLinkModal').modal('hide');
                // 重新加载页面
                location.reload();
            } else {
                alert('保存失败：' + (response.error || '未知错误'));
            }
        },
        error: function() {
            alert('网络错误，请稍后重试');
        },
        complete: function() {
            // 恢复提交按钮
            submitBtn.prop('disabled', false).html(originalText);
        }
    });
}

// 重置弹层表单
$('#addLinkModal').on('hidden.bs.modal', function() {
    $('#addLinkForm')[0].reset();
});

// 编辑友情链接
function editLink(id) {
    // 发送AJAX请求获取链接数据
    $.ajax({
        url: 'links.php?action=get&id=' + id,
        type: 'GET',
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                var data = response.data;
                // 填充表单数据
                $('#edit_id').val(data.id);
                $('#edit_name').val(data.name);
                $('#edit_url').val(data.url);
                $('#edit_description').val(data.description || '');
                $('#edit_sort_order').val(data.sort_order || 0);
                $('#edit_target').val(data.target || '_blank');
                $('#edit_status').val(data.status || 1);

                // 显示弹层
                $('#editLinkModal').modal('show');
            } else {
                alert('获取链接数据失败：' + (response.error || '未知错误'));
            }
        },
        error: function() {
            alert('网络错误，请稍后重试');
        }
    });
}

// 提交编辑表单
function submitEditForm() {
    var form = $('#editLinkForm');
    var formData = new FormData(form[0]);

    // 表单验证
    var name = form.find('input[name="name"]').val().trim();
    var url = form.find('input[name="url"]').val().trim();

    if (!name) {
        alert('请输入链接名称');
        form.find('input[name="name"]').focus();
        return;
    }

    if (!url) {
        alert('请输入链接地址');
        form.find('input[name="url"]').focus();
        return;
    }

    // 禁用提交按钮
    var submitBtn = $('#editLinkModal .modal-footer .btn-primary');
    var originalText = submitBtn.html();
    submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 保存中...');

    // 发送AJAX请求
    $.ajax({
        url: 'links.php?action=save',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                // 关闭弹层
                $('#editLinkModal').modal('hide');
                // 重新加载页面
                location.reload();
            } else {
                alert('保存失败：' + (response.error || '未知错误'));
            }
        },
        error: function() {
            alert('网络错误，请稍后重试');
        },
        complete: function() {
            // 恢复提交按钮
            submitBtn.prop('disabled', false).html(originalText);
        }
    });
}

// 重置编辑弹层表单
$('#editLinkModal').on('hidden.bs.modal', function() {
    $('#editLinkForm')[0].reset();
});

// 强制关闭所有模态框的函数
function forceCloseModals() {
    $('.modal').modal('hide');
    $('.modal-backdrop').remove();
    $('body').removeClass('modal-open');
    $('body').css('padding-right', '');
}

// 添加ESC键关闭模态框
$(document).keyup(function(e) {
    if (e.keyCode === 27) { // ESC键
        forceCloseModals();
    }
});

// 点击模态框外部关闭
$(document).on('click', '.modal', function(e) {
    if (e.target === this) {
        $(this).modal('hide');
    }
});
</script>

{include file="footer.htm"}
