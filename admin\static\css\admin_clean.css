/* Admin panel styles - clean version without Chinese comments */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #1b68ff;
    --primary-hover: #0045ce;
    --success-color: #3ad29f;
    --warning-color: #eea303;
    --danger-color: #f82f58;
    --info-color: #17a2b8;
    --text-color: #001a4e;
    --text-secondary: #6c757d;
    --border-color: #e9ecef;
    --bg-gray: #f8f9fa;
    --sidebar-width: 160px;
    --sidebar-collapsed-width: 64px;
    --sidebar-bg: #343a40;
    --header-height: 64px;
    --card-shadow: 0 0.5rem 1rem rgba(18, 38, 63, 0.05);
    --transition: all 0.3s ease;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: var(--text-color);
    background: var(--bg-gray);
}

/* Layout */
.wrapper {
    display: flex;
    min-height: 100vh;
}

/* Sidebar */
.sidebar {
    width: var(--sidebar-width);
    background: var(--sidebar-bg);
    color: #fff;
    position: fixed;
    height: 100vh;
    left: 0;
    top: 0;
    z-index: 1000;
    transition: var(--transition);
    padding-top: var(--header-height);
    overflow-y: auto;
    box-shadow: 2px 0 6px rgba(0,0,0,0.1);
}

.sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
    overflow-x: hidden;
}

.sidebar-header {
    position: fixed;
    top: 0;
    left: 0;
    width: var(--sidebar-width);
    height: var(--header-height);
    background: var(--sidebar-bg);
    display: flex;
    align-items: center;
    padding: 0 20px;
    z-index: 1001;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.sidebar.collapsed .sidebar-header {
    width: var(--sidebar-collapsed-width);
    padding: 0;
    justify-content: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
    color: #fff;
    text-decoration: none;
    font-size: 16px;
    font-weight: 600;
}

.sidebar.collapsed .logo {
    justify-content: center;
    padding: 0;
    margin: 0;
}

.sidebar.collapsed .logo span {
    display: none;
}

/* Menu items */
.menu-item {
    padding: 12px 20px;
    color: rgba(255,255,255,0.7);
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    border-left: 3px solid transparent;
}

.sidebar.collapsed .menu-item {
    padding: 12px 0;
    justify-content: center;
    border-left: none;
}

.menu-item:hover {
    color: #fff;
    background: rgba(255,255,255,0.1);
    border-left: 3px solid var(--primary-color);
}

.menu-item.active {
    color: #fff;
    background: rgba(0,0,0,0.2);
    border-left: 3px solid var(--primary-color);
}

.sidebar.collapsed .menu-item:hover,
.sidebar.collapsed .menu-item.active {
    border-left: none;
    background: rgba(255,255,255,0.1);
}

.sidebar.collapsed .menu-item span {
    display: none;
}

.menu-item a {
    color: inherit;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 12px;
    width: 100%;
}

.menu-item i {
    font-size: 16px;
    width: 20px;
    text-align: center;
}

/* Main content */
.main-content {
    margin-left: var(--sidebar-width);
    flex: 1;
    padding: 24px;
    padding-top: calc(var(--header-height) + 24px);
    transition: var(--transition);
}

.wrapper.collapsed .main-content {
    margin-left: var(--sidebar-collapsed-width);
}

/* Top navigation */
.top-nav {
    height: var(--header-height);
    background: #fff;
    box-shadow: var(--card-shadow);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24px;
    position: fixed;
    top: 0;
    right: 0;
    left: var(--sidebar-width);
    z-index: 999;
    transition: var(--transition);
}

.wrapper.collapsed .top-nav {
    left: var(--sidebar-collapsed-width);
}

.nav-left {
    display: flex;
    align-items: center;
    gap: 16px;
}

.toggle-sidebar {
    font-size: 20px;
    cursor: pointer;
    color: var(--text-secondary);
    transition: var(--transition);
}

.toggle-sidebar:hover {
    color: var(--text-color);
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-secondary);
}

.breadcrumb i {
    font-size: 12px;
}

.admin-badge {
    background: var(--primary-color);
    color: #fff;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.nav-right {
    display: flex;
    align-items: center;
    gap: 16px;
}

.nav-item {
    position: relative;
    cursor: pointer;
    color: var(--text-secondary);
    transition: var(--transition);
    padding: 8px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-item:hover {
    color: var(--primary-color);
    background-color: rgba(27, 104, 255, 0.1);
}

.nav-item i {
    font-size: 18px;
}

.user-item {
    display: flex;
    align-items: center;
    background-color: var(--bg-gray);
    border-radius: 24px;
    padding: 6px 16px;
    margin-left: 8px;
    border: 1px solid var(--border-color);
}

.user-item:hover {
    border-color: var(--primary-color);
    background-color: rgba(27, 104, 255, 0.05);
}

.user-avatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    font-weight: 500;
}

.user-name {
    font-weight: 500;
    margin-right: 12px;
}

.logout-link {
    color: var(--text-secondary);
    transition: var(--transition);
    display: flex;
    align-items: center;
}

.logout-link:hover {
    color: var(--danger-color);
}

.badge {
    position: absolute;
    top: 0;
    right: 0;
    background: var(--danger-color);
    color: #fff;
    font-size: 12px;
    height: 18px;
    min-width: 18px;
    border-radius: 9px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 6px;
}

/* Operation type badges */
.badge {
    position: static;
    display: inline-block;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 1px solid;
}

.badge-create {
    background: rgba(58, 210, 159, 0.1);
    color: #3ad29f;
    border-color: rgba(58, 210, 159, 0.2);
}

.badge-update {
    background: rgba(238, 163, 3, 0.1);
    color: #eea303;
    border-color: rgba(238, 163, 3, 0.2);
}

.badge-delete {
    background: rgba(248, 47, 88, 0.1);
    color: #f82f58;
    border-color: rgba(248, 47, 88, 0.2);
}

.badge-login {
    background: rgba(27, 104, 255, 0.1);
    color: #1b68ff;
    border-color: rgba(27, 104, 255, 0.2);
}

.badge-logout {
    background: rgba(108, 117, 125, 0.1);
    color: #6c757d;
    border-color: rgba(108, 117, 125, 0.2);
}

.badge-test {
    background: rgba(108, 117, 125, 0.1);
    color: #6c757d;
    border-color: rgba(108, 117, 125, 0.2);
}

.badge-success {
    background: rgba(58, 210, 159, 0.1);
    color: #3ad29f;
    border-color: rgba(58, 210, 159, 0.2);
}

/* Page title */
.page-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--border-color);
}

.page-title h1 {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-color);
}

/* Sections */
.section {
    margin-bottom: 32px;
}

.section-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 16px;
}

/* Cards */
.card {
    background: #fff;
    border-radius: 8px;
    box-shadow: var(--card-shadow);
    padding: 24px;
    margin-bottom: 24px;
}

.card-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 16px;
}

/* Stats grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
    margin-bottom: 32px;
}

.stat-card {
    background: #fff;
    border-radius: 8px;
    padding: 24px;
    box-shadow: var(--card-shadow);
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 20px;
}

.stat-title {
    font-size: 14px;
    color: var(--text-secondary);
    font-weight: 500;
}

.stat-value {
    font-size: 28px;
    font-weight: 700;
    color: var(--text-color);
}

.stat-trend {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    font-weight: 500;
}

.trend-up {
    color: var(--success-color);
}

.trend-down {
    color: var(--danger-color);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border: 1px solid transparent;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    background: transparent;
}

.btn-primary {
    background: var(--primary-color);
    color: #fff;
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background: var(--primary-hover);
    border-color: var(--primary-hover);
}

.btn-warning {
    background: var(--warning-color);
    color: #fff;
    border-color: var(--warning-color);
}

.btn-warning:hover {
    background: #d39e00;
    border-color: #d39e00;
}

.btn-danger {
    background: var(--danger-color);
    color: #fff;
    border-color: var(--danger-color);
}

.btn-danger:hover {
    background: #e11d48;
    border-color: #e11d48;
}

.btn-outline {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: #fff;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
}

/* Light buttons */
.btn-light-primary {
    background-color: rgba(27, 104, 255, 0.1);
    color: var(--primary-color);
    border-color: rgba(27, 104, 255, 0.2);
}

.btn-light-primary:hover {
    background-color: rgba(27, 104, 255, 0.2);
    color: var(--primary-hover);
}

.btn-light-success {
    background-color: rgba(58, 210, 159, 0.1);
    color: var(--success-color);
    border-color: rgba(58, 210, 159, 0.2);
}

.btn-light-danger {
    background-color: rgba(248, 47, 88, 0.1);
    color: var(--danger-color);
    border-color: rgba(248, 47, 88, 0.2);
}

/* Tables */
.table-responsive {
    overflow-x: auto;
}

.table {
    width: 100%;
    border-collapse: collapse;
    background: #fff;
}

.table th,
.table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.table th {
    background: var(--bg-gray);
    font-weight: 600;
    color: var(--text-color);
}

.table tbody tr:hover {
    background: rgba(27, 104, 255, 0.05);
}

/* Tags */
.tag {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.tag-success {
    background: rgba(58, 210, 159, 0.1);
    color: var(--success-color);
}

.tag-warning {
    background: rgba(238, 163, 3, 0.1);
    color: var(--warning-color);
}

.tag-danger {
    background: rgba(248, 47, 88, 0.1);
    color: var(--danger-color);
}

/* Utility classes */
.d-flex {
    display: flex !important;
}

.flex-wrap {
    flex-wrap: wrap !important;
}

.gap-2 {
    gap: 8px !important;
}

.gap-3 {
    gap: 12px !important;
}

.text-center {
    text-align: center !important;
}

.action-buttons {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

/* Alert styles */
.alert {
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    gap: 12px;
    border: 1px solid transparent;
}

.alert i {
    font-size: 16px;
}

.alert-success {
    background-color: rgba(58, 210, 159, 0.1);
    color: var(--success-color);
    border-color: rgba(58, 210, 159, 0.2);
}

.alert-warning {
    background-color: rgba(238, 163, 3, 0.1);
    color: var(--warning-color);
    border-color: rgba(238, 163, 3, 0.2);
}

.alert-danger {
    background-color: rgba(248, 47, 88, 0.1);
    color: var(--danger-color);
    border-color: rgba(248, 47, 88, 0.2);
}

.alert-info {
    background-color: rgba(23, 162, 184, 0.1);
    color: var(--info-color);
    border-color: rgba(23, 162, 184, 0.2);
}

/* Text utilities */
.text-muted {
    color: var(--text-secondary) !important;
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        width: var(--sidebar-width);
        transform: translateX(-100%);
    }

    .sidebar.collapsed {
        width: var(--sidebar-collapsed-width);
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .wrapper.collapsed .main-content {
        margin-left: var(--sidebar-collapsed-width);
    }

    .top-nav {
        left: 0;
    }

    .wrapper.collapsed .top-nav {
        left: var(--sidebar-collapsed-width);
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .page-title {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }
}

/* Text color utilities */
.text-primary {
    color: var(--primary-color) !important;
}

.text-success {
    color: var(--success-color) !important;
}

.text-warning {
    color: var(--warning-color) !important;
}

.text-danger {
    color: var(--danger-color) !important;
}

.text-info {
    color: var(--info-color) !important;
}

.text-muted {
    color: var(--text-secondary) !important;
}

/* Cache management styles */
.cache-operations {
    display: grid;
    gap: 32px;
}

.operation-group {
    background: #fff;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.operation-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 2px solid var(--bg-gray);
}

.operation-header i {
    font-size: 20px;
    color: var(--primary-color);
}

.operation-header span {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-color);
}

.operation-header small {
    color: var(--text-secondary);
    font-size: 13px;
    margin-left: auto;
}

.operation-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.operation-btn {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 20px;
    border-radius: 10px;
    text-decoration: none;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.operation-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.operation-btn:hover::before {
    left: 100%;
}

.refresh-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.refresh-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.clean-btn {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

.clean-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(240, 147, 251, 0.4);
}

.danger-btn {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
}

.danger-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
}

.btn-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    flex-shrink: 0;
}

.btn-content {
    flex: 1;
}

.btn-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 4px;
}

.btn-desc {
    font-size: 13px;
    opacity: 0.9;
}

/* Enhanced stat cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 32px;
}

.stat-card {
    background: #fff;
    border-radius: 16px;
    padding: 24px;
    display: flex;
    align-items: center;
    gap: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    text-decoration: none;
    color: inherit;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    flex-shrink: 0;
}

.stat-content {
    flex: 1;
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 4px;
}

.stat-title {
    font-size: 14px;
    color: var(--text-secondary);
    font-weight: 500;
}

/* Quick tips */
.quick-tips {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.tip-item {
    background: #fff;
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: flex-start;
    gap: 16px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border-left: 4px solid var(--primary-color);
}

.tip-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
    flex-shrink: 0;
}

.tip-content h4 {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 8px;
}

.tip-content p {
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.5;
    margin: 0;
}

/* Cache management responsive */
@media (max-width: 768px) {
    .operation-buttons {
        grid-template-columns: 1fr;
    }

    .operation-btn {
        padding: 16px;
        gap: 12px;
    }

    .btn-icon {
        width: 40px;
        height: 40px;
        font-size: 18px;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 16px;
    }

    .stat-card {
        padding: 16px;
        gap: 12px;
    }

    .stat-icon {
        width: 48px;
        height: 48px;
        font-size: 20px;
    }

    .stat-value {
        font-size: 20px;
    }

    .quick-tips {
        grid-template-columns: 1fr;
    }
}

/* Setting Tabs Styles */
.setting-tabs {
    display: flex;
    margin-bottom: 0;
    border-bottom: 1px solid var(--border-color);
}

.setting-tab {
    padding: 12px 24px;
    background: #f8f9fa;
    color: var(--text-secondary);
    text-decoration: none;
    border: 1px solid var(--border-color);
    border-bottom: none;
    border-radius: 6px 6px 0 0;
    margin-right: 2px;
    transition: all 0.3s ease;
    font-weight: 500;
    position: relative;
    white-space: nowrap;
}

.setting-tab:hover {
    background: #e9ecef;
    color: var(--text-color);
}

.setting-tab.active {
    background: #fff;
    color: var(--primary-color);
    border-bottom: 1px solid #fff;
    margin-bottom: -1px;
    z-index: 1;
}

/* Form Styles */
.form-horizontal {
    padding: 20px;
}

.form-group {
    margin-bottom: 20px;
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.form-label {
    flex: 0 0 150px;
    font-weight: 500;
    color: var(--text-color);
    padding-top: 8px;
    text-align: right;
}

.form-field {
    flex: 1;
    max-width: 400px;
}

.form-help {
    flex: 0 0 200px;
    padding-top: 8px;
    padding-left: 15px;
}

.help-text {
    font-size: 12px;
    color: var(--text-secondary);
    line-height: 1.4;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 14px;
    color: var(--text-color);
    background: #fff;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(27, 104, 255, 0.1);
}

.form-textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 14px;
    color: var(--text-color);
    background: #fff;
    min-height: 80px;
    resize: vertical;
    transition: border-color 0.3s ease;
}

.form-textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(27, 104, 255, 0.1);
}

.form-select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 14px;
    color: var(--text-color);
    background: #fff;
    cursor: pointer;
    transition: border-color 0.3s ease;
}

.form-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(27, 104, 255, 0.1);
}

.form-check {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    margin-right: 20px;
    cursor: pointer;
}

.form-check input[type="radio"],
.form-check input[type="checkbox"] {
    width: 16px;
    height: 16px;
    margin: 0;
    cursor: pointer;
}

.form-check-label {
    font-size: 14px;
    color: var(--text-color);
    cursor: pointer;
}

.help-block {
    margin-top: 5px;
    font-size: 12px;
    color: var(--text-secondary);
    line-height: 1.4;
}

.form-actions {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: 12px;
    padding-left: 165px;
}

/* Responsive Form Styles */
@media (max-width: 768px) {
    .setting-tabs {
        flex-wrap: wrap;
        gap: 4px;
        border-bottom: none;
        margin-bottom: 10px;
    }

    .setting-tab {
        border-radius: 4px;
        border: 1px solid var(--border-color);
        margin-right: 0;
        margin-bottom: 0;
    }

    .setting-tab.active {
        border: 1px solid var(--primary-color);
        margin-bottom: 0;
    }

    .form-group {
        flex-direction: column;
        gap: 8px;
    }

    .form-label {
        flex: none;
        text-align: left;
        padding-top: 0;
    }

    .form-field {
        max-width: none;
    }

    .form-help {
        flex: none;
        padding-left: 0;
        padding-top: 5px;
    }

    .form-actions {
        padding-left: 0;
    }
}

/* Modal styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    min-width: 350px;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    padding: 20px 20px 15px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h4 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--text-color);
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: var(--bg-gray);
    color: var(--text-color);
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 15px 20px 20px;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* 紧凑统计样式 */
.stats-compact {
    display: flex;
    gap: 30px;
    padding: 15px 20px;
    background: var(--bg-gray);
    border-radius: 6px;
    align-items: center;
    flex-wrap: wrap;
}

.stat-compact {
    display: flex;
    align-items: center;
    gap: 8px;
}

.stat-number {
    font-size: 20px;
    font-weight: 600;
    color: var(--primary-color);
}

.stat-text {
    font-size: 13px;
    color: var(--text-secondary);
}

/* 紧凑筛选表单样式 */
.filter-form-compact {
    padding: 15px 20px;
    background: var(--bg-gray);
    border-radius: 6px;
}

.filter-row-single {
    display: flex;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
}

.filter-item-inline {
    display: flex;
    align-items: center;
    gap: 6px;
    white-space: nowrap;
}

.filter-item-inline label {
    font-size: 13px;
    color: var(--text-color);
    font-weight: 500;
    margin: 0;
}

.form-control-sm {
    padding: 4px 8px;
    font-size: 13px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    min-width: 100px;
}

.form-control-sm:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(27, 104, 255, 0.1);
}

.filter-buttons-inline {
    display: flex;
    gap: 8px;
    margin-left: auto;
}

/* 日志表格优化 */
.table-logs {
    font-size: 13px;
}

.table-logs th {
    font-size: 12px;
    padding: 10px 8px;
    background: var(--bg-gray);
    border-bottom: 2px solid var(--border-color);
}

.table-logs td {
    padding: 8px;
    vertical-align: middle;
    border-bottom: 1px solid var(--border-color);
}

.table-logs .badge {
    font-size: 9px;
    padding: 2px 6px;
}

/* 底部批量操作样式 */
.batch-actions-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-top: 1px solid var(--border-color);
    background: var(--bg-gray);
}

.batch-select-all {
    display: flex;
    align-items: center;
    gap: 6px;
    margin: 0;
    font-size: 14px;
    color: var(--text-color);
    cursor: pointer;
}

.batch-select-all input[type="checkbox"] {
    margin: 0;
}

.batch-buttons {
    display: flex;
    gap: 10px;
    align-items: center;
}

/* 记录数量样式 */
.record-count {
    font-size: 13px;
    font-weight: 400;
    color: var(--text-secondary);
    margin-left: 10px;
}

/* 日志表格优化 */
.table-logs {
    font-size: 13px;
}

.table-logs th {
    font-size: 12px;
    padding: 10px 8px;
    background: var(--bg-gray);
    border-bottom: 2px solid var(--border-color);
}

.table-logs td {
    padding: 8px;
    vertical-align: middle;
    border-bottom: 1px solid var(--border-color);
}

.table-logs .badge {
    font-size: 9px;
    padding: 2px 6px;
}

/* 响应式调整 */
@media (max-width: 1200px) {
    .filter-row-single {
        gap: 15px;
    }

    .filter-item-inline {
        gap: 4px;
    }

    .form-control-sm {
        min-width: 80px;
    }
}

@media (max-width: 768px) {
    .stats-compact {
        gap: 20px;
        justify-content: center;
    }

    .filter-row-single {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .filter-item-inline {
        justify-content: space-between;
    }

    .filter-buttons-inline {
        margin-left: 0;
        justify-content: center;
    }
}

/* 扩展筛选表单样式 - 3行布局 */
.filter-form-expanded {
    padding: 20px !important;
    background: var(--bg-gray) !important;
    border-radius: 8px !important;
    margin-bottom: 0 !important;
}

.filter-row-expanded {
    display: flex !important;
    gap: 20px !important;
    margin-bottom: 16px !important;
    align-items: end !important;
    flex-wrap: wrap !important;
}

.filter-row-expanded:last-child {
    margin-bottom: 0 !important;
}

.filter-item-expanded {
    display: flex !important;
    flex-direction: column !important;
    gap: 6px !important;
    min-width: fit-content !important;
}

.filter-item-expanded .form-control {
    width: auto !important;
    min-width: 120px !important;
}

.filter-item-expanded select.form-control {
    min-width: 100px !important;
}

.filter-item-expanded input[type="text"].form-control {
    min-width: 140px !important;
}

.filter-item-expanded input[type="date"].form-control {
    min-width: 150px !important;
}

.filter-item-expanded label {
    font-size: 13px !important;
    font-weight: 500 !important;
    color: var(--text-color) !important;
    margin-bottom: 0 !important;
}

.filter-buttons-expanded {
    display: flex !important;
    gap: 10px !important;
    align-items: center !important;
}

/* 日志表格优化 - 去除换行 */
.table-logs td {
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
}

/* 分页样式 */
.pagination-wrapper {
    margin-top: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding-top: 15px;
    border-top: 1px solid var(--border-color);
}

.pagination-container {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 100%;
}

.pagination {
    display: flex;
    align-items: center;
    gap: 8px;
    justify-content: center;
}

.page-link {
    display: inline-block;
    padding: 8px 12px;
    margin: 0 2px;
    color: var(--primary-color);
    text-decoration: none;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: #fff;
    font-size: 14px;
    line-height: 1.4;
    transition: var(--transition);
    min-width: 60px;
    text-align: center;
}

.page-link:hover {
    background-color: var(--primary-color);
    color: #fff;
    border-color: var(--primary-color);
    text-decoration: none;
}

.page-current {
    display: inline-block;
    padding: 8px 12px;
    margin: 0 2px;
    color: #fff;
    background-color: var(--primary-color);
    border: 1px solid var(--primary-color);
    border-radius: 4px;
    font-size: 14px;
    line-height: 1.4;
    font-weight: 500;
    min-width: 60px;
    text-align: center;
}

.page-info {
    margin-left: 15px;
    color: var(--text-secondary);
    font-size: 14px;
}

/* 简单分页样式 */
.simple-pagination {
    display: flex;
    align-items: center;
    gap: 8px;
}

.pagination-btn {
    display: inline-block;
    padding: 6px 12px;
    margin: 0 2px;
    color: var(--primary-color);
    text-decoration: none;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: #fff;
    font-size: 13px;
    line-height: 1.4;
    transition: var(--transition);
    min-width: 60px;
    text-align: center;
}

.pagination-btn:hover {
    background-color: var(--primary-color);
    color: #fff;
    border-color: var(--primary-color);
    text-decoration: none;
}

.pagination-btn.active {
    background-color: var(--primary-color);
    color: #fff;
    border-color: var(--primary-color);
    font-weight: 500;
}

.pagination-btn.disabled {
    color: var(--text-secondary);
    background-color: var(--bg-gray);
    border-color: var(--border-color);
    cursor: not-allowed;
    opacity: 0.6;
}

.pagination-btn.disabled:hover {
    background-color: var(--bg-gray);
    color: var(--text-secondary);
    border-color: var(--border-color);
}

/* 标准分页模板样式 */
.pagination-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 20px 0;
}

.pagination {
    display: flex;
    padding-left: 0;
    list-style: none;
    border-radius: 0.25rem;
    margin-bottom: 10px;
}

.page-item {
    margin: 0 2px;
}

.page-item:first-child .page-link {
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
}

.page-item:last-child .page-link {
    border-top-right-radius: 0.25rem;
    border-bottom-right-radius: 0.25rem;
}

.page-item.active .page-link {
    z-index: 1;
    color: #fff;
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.page-item.disabled .page-link {
    color: var(--text-secondary);
    pointer-events: none;
    cursor: not-allowed;
    background-color: #fff;
    border-color: var(--border-color);
}

.page-link {
    position: relative;
    display: block;
    padding: 0.5rem 0.75rem;
    margin-left: -1px;
    line-height: 1.25;
    color: var(--primary-color);
    background-color: #fff;
    border: 1px solid var(--border-color);
    text-decoration: none;
    transition: var(--transition);
}

.page-link:hover {
    z-index: 2;
    color: var(--primary-hover);
    text-decoration: none;
    background-color: var(--bg-gray);
    border-color: var(--border-color);
}

.pagination-info {
    color: var(--text-secondary);
    font-size: 0.875rem;
}
