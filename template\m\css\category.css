/* 移动端列表页样式 */

/* 强制统一背景色 */
body {
    background-color: #f5f5f5 !important;
}

/* 头部样式 */
.container {
    width: 100%;
    padding: 0 16px;
}

/* 头部样式现在统一在common.css中 */

/* 搜索层样式现在统一在common.css中 */

/* 已使用transition替代动画 */

/* 分类头部 */
.category-header {
    background-color: var(--card-color);
    padding: 11px;
    margin-bottom: 0;
    border-radius: var(--radius);
    border: 1px solid var(--border-color);
}

.category-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 5px;
}

.category-desc {
    font-size: 12px;
    color: #999;
}

/* 筛选区域 */
.filter-container {
    background-color: var(--card-color);
    margin-bottom: 0;
    border-radius: var(--radius);
    border: 1px solid var(--border-color);
    visibility: hidden;
}

.filter-container.loaded {
    visibility: visible;
}

.filter-section {
    padding: 7px 10px;
    border-bottom: 1px solid var(--border-color);
    position: relative;
}

.filter-title {
    font-size: 13px;

    margin-bottom: 6px;
    color: #333;
}

.filter-title i {
    color: var(--primary-color);
    margin-right: 3px;
}

.filter-options {
    white-space: nowrap;
    overflow-x: auto;
    overflow-y: hidden;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;
    padding-bottom: 3px;
    max-height: 32px;
}

.filter-options::-webkit-scrollbar {
    display: none;
}

.filter-options::after {
    content: '';
    position: absolute;
    top: 30px;
    right: 0;
    width: 35px;
    height: 30px;
    background: linear-gradient(to right, rgba(255,255,255,0) 0%, rgba(255,255,255,0.7) 40%, rgba(255,255,255,1) 90%);
    pointer-events: none;
}

.filter-options.no-more::after {
    display: none;
}

.filter-options.expanded {
    white-space: normal;
    max-height: 200px;
    overflow-y: auto;
}

.filter-options.expanded::after {
    display: none;
}

.filter-options a {
    display: inline-block;
    margin: 0 5px 5px 0;
    padding: 3px 8px;
    background-color: #f9f9f9;
    border-radius: 3px;
    color: #666;
    text-decoration: none;
    font-size: 12px;
}

.filter-options a.active {
    background-color: var(--primary-color);
    color: #fff;
}

.filter-options a.highlight {
    /* 无动画，仅轻微的颜色变化 */
    border: 1px solid var(--primary-color);
}

.filter-more {
    position: absolute;
    right: 10px;
    top: 10px;
    width: 16px;
    height: 16px;
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    font-size: 14px;
}

.filter-more:after {
    display: none;
}

.filter-more.active:after {
    display: none;
}

/* 子分类列表 */
.subcategory-list {
    background-color: #fff;
    padding: 5px;
    margin-bottom: 0;
}

.subcategory-title {
    font-size: 15px;
    padding: 5px 0;
    border-bottom: 1px solid #eee;
    margin-bottom: 10px;
}

.subcategory-items {
    display: flex;
    flex-wrap: wrap;
}

.subcat-item {
    width: 25%;
    text-align: center;
    margin-bottom: 10px;
}

.subcat-item a {
    display: block;
    color: #666;
    text-decoration: none;
    font-size: 13px;
    padding: 5px;
}

.subcat-item a:hover {
    color: #ff6600;
}

/* 文章列表头部 */
.post-list-header {
    padding: 12px 10px;
    border-bottom: 1px solid var(--border-color);
    font-size: 14px;
    font-weight: bold;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.view-switch {
    display: flex;
    align-items: center;
}

.view-btn {
    padding: 2px 4px;
    background: none;
    border: 1px solid #eee;
    border-radius: 3px;
    margin-left: 4px;
    cursor: pointer;
    font-size: 11px;
    color: #666;
}

.view-btn.active {
    background-color: var(--primary-color);
    color: #fff;
    border-color: var(--primary-color);
}

/* 图文布局样式 */
.post-list {
    background-color: var(--card-color);
    margin-bottom: 0;
    padding: 0;
    border-radius: var(--radius);
    border: 1px solid var(--border-color);
}

/* 全局清除所有标题下划线 */
.post-title a, a.post-title, .post-content a, .simple-title a {
    text-decoration: none !important;
    border-bottom: none !important;
}

/* 过期信息样式 */
.post-expired {
    text-decoration: line-through !important;
}

.post-item {
    padding: 12px 15px; /* 增加内边距，提升视觉舒适度 */
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: flex-start;
    transition: background-color 0.1s ease;
    -webkit-tap-highlight-color: rgba(0,0,0,0);
}

.post-item:active {
    background-color: rgba(0,0,0,0.05);
}

.post-item:last-child {
    border-bottom: none;
}

.post-image {
    width: 90px;
    height: 70px;
    margin-right: 12px; /* 增加图片与内容的间距 */
    flex-shrink: 0;
    overflow: hidden;
    background-color: #f5f5f5;
    border-radius: 4px; /* 稍微增加圆角 */
    position: relative;
}

.post-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0;
    transition: opacity 0.2s;
}

.post-image img.loaded {
    opacity: 1;
}

.post-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    overflow: hidden;
    min-height: 70px;
}

.post-title {
    font-size: 15px; /* 调整标题字体大小，更适合移动端阅读 */
    font-weight: 500; /* 增加字体粗细，提升标题层次感 */
    margin-bottom: 6px; /* 增加标题与元数据的间距 */
    line-height: 1.4; /* 优化行高，提升可读性 */
    color: #222;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    letter-spacing: 0; /* 移除负字间距，提升可读性 */
    margin-top: 0; /* 移除负边距 */
    font-family: var(--font-family);
}

.post-title a {
    color: #222;
    text-decoration: none;
    border-bottom: none;
}

/* 置顶帖子样式 */
.post-item.is-top {
    background-color: #fff;
    border-left: none;
}

.post-item.is-top .post-title {
    font-weight: 500;
    color: #e74950;
}

.post-item.is-top .post-title a {
    color: #e74950;
    font-weight: bold;
}

.post-item.is-top .top-tag {
    display: inline-block;
    padding: 1px 4px; /* 增加置顶标签内边距 */
    background-color: var(--primary-color);
    color: #fff;
    font-size: 10px;
    border-radius: 2px;
    margin-right: 4px; /* 增加标签与标题的间距 */
    vertical-align: middle;
    line-height: 1.4;
}

.post-info {
    display: flex;
    font-size: 13px;
    color: #999;
    flex-wrap: nowrap;
    align-items: center;
    margin-top: auto;
    letter-spacing: 0.3px;
    justify-content: space-between;
}

.post-meta {
    color: #666; /* 调整元数据颜色，提升可读性 */
    font-size: 12px; /* 调整元数据字体大小 */
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    font-weight: 400;
    overflow: hidden;
    flex: 1;
    margin-top: 2px; /* 增加与标题的间距 */
}

.post-meta div, .post-region {
    margin-right: 10px; /* 增加元数据项之间的间距 */
    white-space: nowrap;
}

.post-region {
    background-color: rgba(0, 120, 215, 0.1);
    color: rgba(0, 120, 215, 0.8);
    padding: 2px 6px; /* 增加标签内边距 */
    border-radius: 3px;
    font-size: 11px; /* 调整标签字体大小 */
}

.post-time {
    white-space: nowrap;
    margin-left: 5px;
}

.post-expire {
    background-color: rgba(255, 80, 0, 0.1);
    color: rgba(255, 80, 0, 0.8);
    padding: 2px 6px; /* 增加过期标签内边距 */
    border-radius: 3px;
    font-size: 11px; /* 调整过期标签字体大小 */
    display: inline-block;
    margin-right: 0;
}

/* 文本列表样式 */
.simple-list {
    background-color: var(--card-color);
    margin-bottom: 0;
    padding: 0;
    border-radius: var(--radius);
    border: 1px solid var(--border-color);
}

/* 新的列表项样式 - 使用更高优先级 */
.simple-list .list-item,
.list-item {
    padding: 15px 10px !important;
    border-bottom: 1px solid var(--border-color, #eeeeee) !important;
    border-bottom: 1px solid #eeeeee !important; /* 备用样式 */
    display: block !important; /* 覆盖flex布局 */
}

.simple-list .list-item:active,
.list-item:active {
    background-color: rgba(0,0,0,0.05) !important;
}

.simple-list .list-item:last-child,
.list-item:last-child {
    border-bottom: none !important;
}

.simple-list .item-title,
.item-title {
    margin: 0 !important;
}

.simple-list .item-title a,
.item-title a {
    color: #333 !important;
    text-decoration: none !important;
    font-size: 15px !important;
    font-weight: 500 !important;
    line-height: 1.4 !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
    display: block !important;
}

.simple-list .item-title a.expired,
.item-title a.expired {
    color: #999 !important;
    text-decoration: line-through !important;
}

/* 标签容器样式 - 与首页一致 */
.tab-container {
    background-color: #fff;
    margin-bottom: 10px;
    border-radius: 3px;
}

.tab-header {
    display: flex;
    border-bottom: 1px solid #f0f0f0;
    justify-content: space-between;
    align-items: center;
    padding: 0 15px;
}

.tab-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    padding: 12px 0;
}

/* 行布局样式 - 标题和时间在一行 */
.item-row {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    gap: 10px !important;
}

.item-left {
    flex: 1 !important;
    min-width: 0 !important; /* 允许内容收缩 */
}

.item-right {
    flex-shrink: 0 !important;
}

.item-time {
    color: #999 !important;
    font-size: 12px !important;
    white-space: nowrap !important;
}

.top-tag {
    background: #ff4444 !important; /* 红色徽章 */
    color: white !important;
    font-size: 10px !important;
    padding: 2px 4px !important;
    border-radius: 3px !important;
    margin-left: 4px !important; /* 减少间距，紧挨着标题 */
    font-weight: bold !important;
    display: inline !important;
    vertical-align: middle !important;
    line-height: 1 !important;
}

.simple-list .item-meta,
.item-meta {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 12px !important;
    color: #999 !important;
    font-size: 12px !important;
}

.simple-list .meta-item,
.meta-item {
    display: flex !important;
    align-items: center !important;
    white-space: nowrap !important;
}

.simple-list .meta-item i,
.meta-item i {
    margin-right: 4px !important;
    font-size: 11px !important;
    width: 12px !important;
    text-align: center !important;
}

.simple-list .meta-item:first-child i,
.meta-item:first-child i {
    color: #ff6b6b !important;
}

.simple-list .meta-item:nth-child(2) i,
.meta-item:nth-child(2) i {
    color: #4ecdc4 !important;
}

.simple-list .meta-item:last-child i,
.meta-item:last-child i {
    color: #45b7d1 !important;
}

/* 置顶帖子样式 */
.list-item.is-top {
    background-color: #ffffff !important; /* 背景与其他一样 */
    border-left: none !important; /* 左侧不要边框 */
}

/* 置顶标题红色 */
.list-item.is-top .item-title a {
    color: #ff4444 !important;
    font-weight: 600 !important;
}

/* 兼容旧样式 */
.simple-item {
    padding: 7px 10px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.simple-item:active {
    background-color: rgba(0,0,0,0.05);
}

.simple-item:last-child {
    border-bottom: none;
}

.simple-title {
    font-size: 17px;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.simple-title a {
    color: #333;
    text-decoration: none;
}

.simple-item.is-top {
    background-color: #fff9f6;
    border-left: 3px solid #ff6600;
}

.simple-item.is-top .simple-title a {
    color: #e74950;
    font-weight: bold;
}

.top-dot {
    display: inline-block;
    width: 6px;
    height: 6px;
    background-color: #ff6600;
    border-radius: 50%;
    margin-right: 5px;
    vertical-align: middle;
}

.simple-meta {
    color: #999;
    font-size: 13px;
    margin-left: 10px;
    white-space: nowrap;
}

/* 分页样式 */
.pagination {
    background-color: #fff;
    padding: 10px 10px;
    text-align: center;
    margin-bottom: 0;
}

.pagination a, .pagination span {
    display: inline-block;
    padding: 6px 12px;
    margin: 0 3px;
    border: 1px solid #eee;
    border-radius: 4px;
    text-decoration: none;
    color: var(--text-color);
    font-size: 14px;
    min-width: 36px;
    transition: all 0.3s ease;
}

.pagination a:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background-color: rgba(var(--primary-color-rgb), 0.05);
}

.pagination .current {
    background-color: var(--primary-color);
    color: #fff;
    border-color: var(--primary-color);
}

.pagination .disabled {
    color: #ccc;
    cursor: not-allowed;
    border-color: #f5f5f5;
    background-color: #fafafa;
}

/* 简化分页样式 */
.simple-pagination {
    display: flex;
    justify-content: space-between;
    padding: 10px 15px;
}

.simple-pagination a {
    flex: 1;
    margin: 0 5px;
    text-align: center;
    padding: 8px 0;
}

/* 加载更多按钮样式 - 移动端简洁版 v2.0 */
.pagination-container {
    margin: 15px 0;
    padding: 0 15px;
    text-align: center;
}

.load-more {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    background: linear-gradient(135deg, var(--primary-color) 0%, rgba(var(--primary-color-rgb), 0.8) 100%);
    color: #fff;
    text-align: center;
    padding: 12px 24px;
    font-size: 14px;
    border-radius: 25px;
    border: none;
    cursor: pointer;
    position: relative;
    transition: all 0.2s ease;
    margin: 0 auto;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(var(--primary-color-rgb), 0.3);
    min-width: 140px;
}

.load-more:before {
    content: '↓';
    font-size: 14px;
    transition: transform 0.2s ease;
}

.load-more:active {
    transform: translateY(1px);
    box-shadow: 0 1px 4px rgba(var(--primary-color-rgb), 0.4);
}

.load-more:active:before {
    transform: translateY(2px);
}

.load-more.loading {
    color: rgba(255,255,255,0.8);
    pointer-events: none;
    background: linear-gradient(135deg, #ccc 0%, #bbb 100%);
    box-shadow: 0 1px 4px rgba(0,0,0,0.1);
}

.load-more.loading:before {
    display: none;
}

.load-more.loading:after {
    content: '';
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255,255,255,0.3);
    border-top: 2px solid #fff;
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
}

.load-more-end {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    background: #f8f9fa;
    color: #999;
    text-align: center;
    padding: 10px 20px;
    font-size: 13px;
    border-radius: 20px;
    border: 1px solid #e9ecef;
    margin: 0 auto;
    font-weight: 500;
}

.load-more-end:before {
    content: '✓';
    font-size: 14px;
    color: var(--primary-color);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 加载更多按钮的微妙脉冲动画 */
@keyframes pulse {
    0% { box-shadow: 0 2px 8px rgba(var(--primary-color-rgb), 0.3); }
    50% { box-shadow: 0 2px 12px rgba(var(--primary-color-rgb), 0.4); }
    100% { box-shadow: 0 2px 8px rgba(var(--primary-color-rgb), 0.3); }
}

.load-more {
    animation: pulse 2s ease-in-out infinite;
}

/* 底部导航样式 */
.navbar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #fff;
    display: flex;
    box-shadow: 0 -2px 10px rgba(0,0,0,0.05);
    z-index: 100;
    padding: 8px 0 5px;
    height: 55px;
    border-top-left-radius: 15px;
    border-top-right-radius: 15px;
}