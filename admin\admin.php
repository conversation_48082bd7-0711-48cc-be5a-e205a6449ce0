<?php
// 定义安全常量
define('IN_BTMPS', true);
/**
 * 管理员管理页面
 */
// 引入公共文件
require_once('../include/common.inc.php');

// 检查管理员是否登录
if (!isset($_SESSION['admin']) || $_SESSION['admin']['is_login'] !== true) {
    header("Location: login.php");
    exit;
}

// 当前页面
$current_page = 'admin';

// 操作类型
$action = isset($_GET['action']) ? $_GET['action'] : 'list';

// 信息提示
$message = '';
$error = '';

// 修改模板目录为admin/template目录
$tpl->setTemplateDir(dirname(__FILE__) . '/template/');

// 处理管理员操作
switch ($action) {
    // 管理员列表
    case 'list':
        // 获取当前页码
        $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
        $page = max(1, $page);
        
        // 每页显示数量
        $per_page = 10;
        
        // 获取关键词
        $keyword = isset($_GET['keyword']) ? trim($_GET['keyword']) : '';
        
        // 构建查询条件
        $where = "1=1";
        if (!empty($keyword)) {
            $where .= " AND (username LIKE '%" . $db->escape($keyword) . "%' OR realname LIKE '%" . $db->escape($keyword) . "%')";
        }
        
        // 获取总数
        $sql = "SELECT COUNT(*) as total FROM admins WHERE " . $where;
        $result = $db->query($sql);
        $row = $db->fetch_array($result);
        $total_admins = $row['total'];
        
        // 分页处理
        $offset = ($page - 1) * $per_page;
        $sql = "SELECT * FROM admins WHERE " . $where . " ORDER BY id ASC LIMIT " . $offset . ", " . $per_page;
        $result = $db->query($sql);
        
        $admins = [];
        while ($row = $db->fetch_array($result)) {
            // 格式化最后登录时间
            if (!empty($row['last_login'])) {
                $row['last_login_formatted'] = date('Y-m-d H:i:s', $row['last_login']);
            } else {
                $row['last_login_formatted'] = '从未登录';
            }
            $admins[] = $row;
        }
        
        // 生成分页数据
        $pagination = generate_pagination($total_admins, $page, $per_page, 5);
        
        // 构建分页链接
        $base_url = '?';
        $params = '';
        if (!empty($keyword)) {
            $params .= '&keyword=' . urlencode($keyword);
        }
        
        // 设置分页模板变量
        set_pagination_template_vars($pagination, $base_url, $params);
        
        // 设置模板变量
        $tpl->assign('admins', $admins);
        $tpl->assign('keyword', $keyword);
        break;
        
    // 添加管理员
    case 'add':
        // 初始化空的管理员数据，防止模板中出现未定义变量错误
        $admin_data = array(
            'username' => '',
            'realname' => '',
            'role' => 'admin',
            'status' => 1
        );
        $tpl->assign('admin_data', $admin_data);
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // 处理表单提交
            $result = save_admin();
            if ($result['success']) {
                $message = '管理员添加成功';
                // 重定向到列表页
                header("Location: admin.php?message=" . urlencode($message));
                exit;
            } else {
                $error = $result['error'];
            }
        }
        break;
        
    // 编辑管理员
    case 'edit':
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        if (!$id) {
            $error = '未指定管理员ID';
            break;
        }
        
        // 获取管理员信息
        $sql = "SELECT * FROM admins WHERE id = " . $id . " LIMIT 1";
        $result = $db->query($sql);
        
        if ($db->num_rows($result) == 0) {
            $error = '管理员不存在';
            break;
        }
        
        $admin = $db->fetch_array($result);
        
        // 格式化时间字段
        if (isset($admin['created_at']) && !empty($admin['created_at'])) {
            $admin['created_at'] = date('Y-m-d H:i:s', $admin['created_at']);
        }
        
        if (isset($admin['last_login']) && !empty($admin['last_login'])) {
            $admin['last_login'] = date('Y-m-d H:i:s', $admin['last_login']);
        }
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // 处理表单提交
            $result = save_admin($id);
            if ($result['success']) {
                $message = '管理员更新成功';
                // 重定向到列表页
                header("Location: admin.php?message=" . urlencode($message));
                exit;
            } else {
                $error = $result['error'];
            }
        }
        
        $tpl->assign('admin_data', $admin);
        break;
        
    // 删除管理员
    case 'delete':
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        if (!$id) {
            $error = '未指定管理员ID';
            break;
        }
        
        // 检查是否是当前登录的管理员
        if ($_SESSION['admin']['id'] == $id) {
            $error = '不能删除当前登录的管理员账号';
            // 返回列表页
            header("Location: admin.php?error=" . urlencode($error));
            exit;
        }
        
        // 检查是否是最后一个管理员
        $sql = "SELECT COUNT(*) as total FROM admins";
        $result = $db->query($sql);
        $row = $db->fetch_array($result);
        
        if ($row['total'] <= 1) {
            $error = '系统中必须保留至少一个管理员账号';
            // 返回列表页
            header("Location: admin.php?error=" . urlencode($error));
            exit;
        }
        
        // 执行删除
        $sql = "DELETE FROM admins WHERE id = " . $id;
        $result = $db->query($sql);
        
        if ($result) {
            $message = '管理员删除成功';
        } else {
            $error = '删除管理员失败';
        }
        
        // 重定向到列表页
        header("Location: admin.php?" . (!empty($message) ? "message=" . urlencode($message) : "error=" . urlencode($error)));
        exit;
        break;
        
    // 更改状态
    case 'toggle_status':
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        if (!$id) {
            $error = '未指定管理员ID';
            break;
        }
        
        // 检查是否是当前登录的管理员
        if ($_SESSION['admin']['id'] == $id) {
            $error = '不能禁用当前登录的管理员账号';
            // 返回列表页
            header("Location: admin.php?error=" . urlencode($error));
            exit;
        }
        
        // 获取当前状态
        $sql = "SELECT status FROM admins WHERE id = " . $id;
        $result = $db->query($sql);
        $row = $db->fetch_array($result);
        
        $new_status = ($row['status'] == 1) ? 0 : 1;
        
        // 更新状态
        $sql = "UPDATE admins SET status = " . $new_status . " WHERE id = " . $id;
        $result = $db->query($sql);
        
        if ($result) {
            $message = ($new_status == 1) ? '管理员已启用' : '管理员已禁用';
        } else {
            $error = '更新管理员状态失败';
        }
        
        // 重定向到列表页
        header("Location: admin.php?" . (!empty($message) ? "message=" . urlencode($message) : "error=" . urlencode($error)));
        exit;
        break;
        
    default:
        $error = '未知操作';
        break;
}

// 接收URL传递的消息
if (isset($_GET['message']) && empty($message)) {
    $message = $_GET['message'];
}

// 接收URL传递的错误信息
if (isset($_GET['error']) && empty($error)) {
    $error = $_GET['error'];
}

// 传递数据到模板
$tpl->assign('current_page', $current_page);
$tpl->assign('breadcrumb', '管理员管理');
$tpl->assign('message', $message);
$tpl->assign('error', $error);
$tpl->assign('action', $action);
$tpl->assign('page_title', '管理员管理');
$tpl->assign('admin', $_SESSION['admin']);

// 根据不同操作分配特定数据
switch ($action) {
    case 'list':
        $tpl->display('admin_list.htm');
        break;
        
    case 'add':
    case 'edit':
        $tpl->display('admin_edit.htm');
        break;
        
    default:
        $tpl->display('admin_list.htm');
        break;
}

/**
 * 保存管理员信息
 */
function save_admin($id = 0) {
    global $db;
    
    // 获取表单数据
    $username = isset($_POST['username']) ? trim($_POST['username']) : '';
    $password = isset($_POST['password']) ? trim($_POST['password']) : '';
    $confirm_password = isset($_POST['confirm_password']) ? trim($_POST['confirm_password']) : '';
    $realname = isset($_POST['realname']) ? trim($_POST['realname']) : '';
    $role = isset($_POST['role']) ? trim($_POST['role']) : 'admin';
    $status = isset($_POST['status']) ? intval($_POST['status']) : 1;
    
    // 验证数据
    if (empty($username)) {
        return array('success' => false, 'error' => '用户名不能为空');
    }
    
    // 添加新管理员时密码必填
    if ($id == 0 && empty($password)) {
        return array('success' => false, 'error' => '密码不能为空');
    }
    
    // 如果填写了密码，需要检查两次输入是否一致
    if (!empty($password) && $password !== $confirm_password) {
        return array('success' => false, 'error' => '两次输入的密码不一致');
    }
    
    // 检查用户名是否已存在
    $sql = "SELECT id FROM admins WHERE username = '" . $db->escape($username) . "'";
    if ($id > 0) {
        $sql .= " AND id != " . $id;
    }
    $sql .= " LIMIT 1";
    
    $result = $db->query($sql);
    if ($db->num_rows($result) > 0) {
        return array('success' => false, 'error' => '用户名已存在，请选择其他用户名');
    }
    
    // 准备数据
    $data = array(
        'username' => $username,
        'realname' => $realname,
        'role' => $role,
        'status' => $status,
        'updated_at' => time()
    );
    
    // 如果设置了新密码
    if (!empty($password)) {
        $data['password'] = password_hash($password, PASSWORD_DEFAULT);
    }
    
    // 执行数据库操作
    if ($id > 0) {
        // 更新现有管理员
        $result = $db->update('admins', $data, "id = " . $id);
    } else {
        // 添加新管理员
        $data['created_at'] = time();
        $result = $db->insert('admins', $data);
    }
    
    if ($result) {
        return array('success' => true);
    } else {
        return array('success' => false, 'error' => '保存管理员信息失败');
    }
}

/**
 * 生成分页数据
 */
function generate_pagination($total_items, $current_page, $items_per_page, $show_pages) {
    // 计算总页数
    $total_pages = ceil($total_items / $items_per_page);
    
    // 确保当前页不超过总页数
    $current_page = min($current_page, $total_pages);
    $current_page = max(1, $current_page);
    
    // 计算起始和结束索引
    $start = ($current_page - 1) * $items_per_page + 1;
    $end = min($start + $items_per_page - 1, $total_items);
    
    // 计算显示的页码链接（前后各显示5个页码）
    $start_page = max(1, $current_page - $show_pages);
    $end_page = min($total_pages, $current_page + $show_pages);
    
    // 生成页码链接
    $page_links = [];
    for ($i = $start_page; $i <= $end_page; $i++) {
        $page_links[$i] = '?page=' . $i;
    }
    
    // 拼接分页数据
    $pagination = [
        'total_items' => $total_items,
        'items_per_page' => $items_per_page,
        'current_page' => $current_page,
        'total_pages' => $total_pages,
        'start' => $start,
        'end' => $end,
        'page_links' => $page_links,
        'first_link' => '?page=1',
        'last_link' => '?page=' . $total_pages,
        'previous_link' => str_replace('{page}', max(1, $current_page - 1), '?page={page}'),
        'next_link' => str_replace('{page}', min($total_pages, $current_page + 1), '?page={page}')
    ];
    
    return $pagination;
}

/**
 * 设置分页模板变量
 */
function set_pagination_template_vars($pagination, $base_url, $params) {
    global $tpl;
    
    // 处理分页链接
    foreach ($pagination['page_links'] as $page => $link) {
        $pagination['page_links'][$page] = $base_url . 'page=' . $page . $params;
    }
    
    // 添加参数到其他链接
    $pagination['first_link'] = $base_url . 'page=1' . $params;
    $pagination['last_link'] = $base_url . 'page=' . $pagination['total_pages'] . $params;
    $pagination['previous_link'] = $base_url . 'page=' . max(1, $pagination['current_page'] - 1) . $params;
    $pagination['next_link'] = $base_url . 'page=' . min($pagination['total_pages'], $pagination['current_page'] + 1) . $params;
    
    $tpl->assign('pagination', $pagination);
} 