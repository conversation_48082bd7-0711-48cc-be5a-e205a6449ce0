<?php
/**
 * AJAX加载更多帖子处理程序 - API接口
 */
// 定义安全常量
define('IN_BTMPS', true);

require_once '../include/common.inc.php';

// 输出JSON头
header('Content-Type: application/json');

// 获取参数
$catId = isset($_GET['cat_id']) ? intval($_GET['cat_id']) : 0;
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$areaId = isset($_GET['area_id']) ? intval($_GET['area_id']) : 0;
$perPage = isset($GLOBALS['settings']['list_page_size']) ? intval($GLOBALS['settings']['list_page_size']) : 10;

// 验证参数
if ($catId <= 0 || $page <= 0) {
    echo json_encode(['code' => 400, 'msg' => '参数错误']);
    exit;
}

// 获取分类信息
$allCategories = getCategories();
$category = null;
foreach ($allCategories as $cat) {
    if ($cat['id'] == $catId) {
        $category = $cat;
        break;
    }
}

if (!$category) {
    echo json_encode(['code' => 404, 'msg' => '分类不存在']);
    exit;
}

try {
    // 处理区域
    $allRegions = getRegions();
    $areaSelection = processAreaSelection($areaId, $allRegions);
    $isProvinceArea = $areaSelection['isProvinceArea'];

    // 获取帖子数据
    $posts = getCategoryPostsDirectly($catId, $page, $perPage, 0, $areaId, $isProvinceArea);

    // 如果没有数据
    if (empty($posts)) {
        echo json_encode(['code' => 204, 'msg' => '没有更多数据', 'has_more' => false]);
        exit;
    }

    // 获取总数
    $totalPosts = getCategoryPostsCount($catId, 0, $areaId, $isProvinceArea);
    $totalPages = ceil($totalPosts / $perPage);
    $hasMore = ($page < $totalPages);

    // 开始缓冲区以捕获模板输出
    ob_start();

    // 加载模板引擎
    $template = new Template();
    $template->setTemplateDir(TEMPLATE_PATH . TEMPLATE_DIR . '/');
    $template->setCacheDir(DATA_PATH.'cache/');
    // 不需要设置template_ext，使用默认值
    
    // 分配变量到模板
    $template->assign('posts', $posts);
    $template->assign('category', $category);

    // 根据分类模板选择对应的列表模板
    $listTemplate = 'post_item_list.htm';
    if (!empty($category['template'])) {
        if ($category['template'] === 'category_fang.htm') {
            // 根据设备类型选择对应的模板
            if (TEMPLATE_DIR === 'm') {
                $listTemplate = 'post_item_fang_list.htm';
            } else {
                $listTemplate = 'post_item_fang_list.htm';
            }
        }
    }

    // 加载帖子列表部分模板
    $template->display($listTemplate);

    // 获取输出并清除缓冲区
    $html = ob_get_clean();

    // 返回JSON数据
    echo json_encode([
        'code' => 200,
        'html' => $html,
        'has_more' => $hasMore,
        'next_page' => $hasMore ? $page + 1 : 0
    ]);
} catch (Exception $e) {
    // 捕获并记录任何错误
    error_log("AJAX loadmore error: " . $e->getMessage());
    echo json_encode([
        'code' => 500, 
        'msg' => '服务器内部错误',
        'error' => $e->getMessage()
    ]);
} 
