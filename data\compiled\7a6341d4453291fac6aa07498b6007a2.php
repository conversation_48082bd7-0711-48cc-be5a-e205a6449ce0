<!DOCTYPE html>
<?php 
// 获取主题设置
$theme = isset($_COOKIE['site_theme']) ? $_COOKIE['site_theme'] : 'red';
$theme_class = 'theme-' . $theme;
 ?>
<html lang="zh-CN" class="<?php echo $theme_class ?? ""; ?> no-fouc">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title><?php echo (isset($post['title'])) ? $post['title'] : ""; ?> - <?php echo $site_name ?? ""; ?></title>
    <meta name="keywords" content="<?php echo (isset($post['title'])) ? $post['title'] : ""; ?>,<?php echo (isset($post['category_name'])) ? $post['category_name'] : ""; ?>,<?php echo $site_name ?? ""; ?>">
    <meta name="description" content="<?php echo mb_substr(strip_tags($post['content']), 0, 100, 'utf-8'); ?>">
    <link rel="stylesheet" href="/static/font-awesome/css/all.min.css">
    <link rel="stylesheet" href="/static/css/themes.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="/template/m/css/common.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="/template/m/css/view.css?v=<?php echo time(); ?>">
    <script src="/static/js/common.js"></script>
    <script src="/static/js/page-stabilizer.js"></script>
    <style>
        /* 移除区域标题左侧边框 */
        .post-gallery-title:before,
        .post-content-title:before,
        .post-fields-title:before,
        .contact-title:before,
        .section-title:before {
            display: none !important;
        }

        /* 相应地移除左侧内边距并减小字体粗细 */
        .post-gallery-title,
        .post-content-title,
        .post-fields-title,
        .contact-title,
        .section-title {
            padding-left: 0 !important;
            font-weight: 500 !important;
            font-size: 16px !important;
        }

        /* 详情页元数据样式 - 内联确保优先级 */
        .detail-meta {
            margin-bottom: 15px !important;
            font-size: 12px !important;
            color: #999 !important;
        }

        .detail-meta .meta-row {
            display: flex !important;
            flex-wrap: wrap !important;
            gap: 12px !important;
            margin-bottom: 8px !important;
        }

        .detail-meta .meta-row:last-child {
            margin-bottom: 0 !important;
        }

        .detail-meta .meta-item {
            display: flex !important;
            align-items: center !important;
            white-space: nowrap !important;
        }

        .detail-meta .meta-item i {
            margin-right: 4px !important;
            font-size: 11px !important;
            width: 12px !important;
            text-align: center !important;
        }

        /* 图标颜色 */
        .detail-meta .meta-row:first-child .meta-item:first-child i {
            color: #ff6b6b !important; /* 地区图标 */
        }

        .detail-meta .meta-row:first-child .meta-item:nth-child(2) i {
            color: #4ecdc4 !important; /* 有效期图标 */
        }

        .detail-meta .meta-row:first-child .meta-item:last-child i {
            color: #45b7d1 !important; /* 时间图标 */
        }

        .detail-meta .meta-row:last-child .meta-item:first-child i {
            color: #28a745 !important; /* 浏览图标 */
        }

        .detail-meta .meta-row:last-child .meta-item:nth-child(2) i {
            color: #6c757d !important; /* 编号图标 */
        }

        .detail-meta .report-link {
            color: #dc3545 !important;
            text-decoration: none !important;
        }

        .detail-meta .report-link i {
            color: #dc3545 !important;
        }

        /* 联系信息样式 */
        .contact-meta-item {
            display: flex !important;
            align-items: center !important;
            margin-bottom: 12px !important;
            font-size: 14px !important;
        }

        .contact-meta-item:last-child {
            margin-bottom: 0 !important;
        }

        .contact-meta-item i {
            width: 16px !important;
            text-align: center !important;
            margin-right: 8px !important;
            font-size: 14px !important;
        }

        .contact-meta-item .fas.fa-user {
            color: #17a2b8 !important; /* 用户图标 */
        }

        .contact-meta-item .fas.fa-phone {
            color: #28a745 !important; /* 电话图标 */
        }

        .contact-meta-item .fas.fa-map-marker-alt {
            color: #ff6b6b !important; /* 地址图标 */
        }

        .contact-meta-item .fab.fa-weixin {
            color: #07c160 !important; /* 微信图标 */
        }

        .contact-meta-item .contact-label {
            min-width: 50px !important;
            color: #999 !important;
            font-weight: 500 !important;
            margin-right: 8px !important;
        }

        .contact-meta-item .contact-value {
            flex: 1 !important;
            color: #333 !important;
            word-break: break-all !important;
        }

        .contact-meta-item .phone-number {
            font-size: 16px !important;
            font-weight: bold !important;
            color: #ff6600 !important;
        }

        .contact-meta-item .expired-text {
            font-size: 14px !important;
            color: #999 !important;
        }
        
        /* 减小内容标题的字体粗细 */
        .post-title {
            font-weight: 500 !important;
            font-size: 20px !important;
        }
        
        /* 减淡信息编号、浏览次数和发布时间的文字颜色 */
        .post-id, .post-time, .post-views {
            color: #aaaaaa !important;
            font-size: 13px !important;
        }
        
        /* 文本内容样式调整 */
        .post-text {
            font-size: 16px !important;
            color: #333333 !important;
            line-height: 1.8 !important;
        }
        
        /* 区域与栏目标签样式 */
        .tag-label {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: normal;
            margin-right: 6px;
            color: #5d8cb9;
            background-color: #eef4fb;
            border: 1px solid #d7e6f5;
        }
        
        /* 不同标签使用不同颜色 */
        .tag-label.parent-category {
            color: #5c7cb4;
            background-color: #e8f0fd;
            border: 1px solid #d0e0f5;
        }
        
        .tag-label.category {
            color: #5d8cb9;
            background-color: #eef4fb;
            border: 1px solid #d7e6f5;
        }
        
        .tag-label.region {
            color: #6baac4;
            background-color: #edf7fa;
            border: 1px solid #d5ecf2;
        }
        
        /* 管理弹出层 */
        .manage-layer {
            position: fixed;
            left: 0;
            right: 0;
            bottom: 0;
            top: 0;
            background: rgba(0,0,0,0.65);
            z-index: 999;
            display: none;
            align-items: center;
            justify-content: center;
        }
        
        .manage-content-centered {
            width: 85%;
            max-width: 320px;
            background: #fff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
            transform: scale(0.85);
            opacity: 0;
            transition: transform 0.3s, opacity 0.3s;
        }
        
        .manage-layer.active {
            display: flex;
        }
        
        .manage-layer.active .manage-content-centered {
            transform: scale(1);
            opacity: 1;
        }
        
        .password-dialog {
            padding: 20px;
        }
        
        .manage-title {
            font-size: 18px;
            color: #333;
            margin: 0 0 10px;
            text-align: center;
            font-weight: 600;
        }
        
        .manage-subtitle {
            text-align: center;
            color: #666;
            font-size: 14px;
            margin: 0 0 20px;
        }
        
        .password-input-container {
            position: relative;
            margin-bottom: 20px;
        }
        
        .password-icon {
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
        }
        
        .manage-input {
            width: 100%;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 12px 12px 12px 35px;
            margin-bottom: 5px;
            box-sizing: border-box;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        .manage-input:focus {
            border-color: var(--primary-color);
            outline: none;
            box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.1);
        }
        
        .manage-btn {
            display: block;
            width: 100%;
            border: none;
            background: var(--primary-color);
            color: #fff;
            padding: 12px 10px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 10px;
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(var(--primary-color-rgb), 0.2);
            transition: all 0.2s;
        }
        
        .manage-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(var(--primary-color-rgb), 0.25);
        }
        
        .manage-close {
            display: block;
            width: 100%;
            border: none;
            background: transparent;
            color: #666;
            padding: 10px;
            font-size: 14px;
            text-align: center;
            text-decoration: none;
            cursor: pointer;
        }
        
        /* Loading动画 */
        .loading {
            display: none;
            text-align: center;
            padding: 20px 0;
        }
        
        .loading i {
            animation: loading 1s linear infinite;
        }
        
        @keyframes loading {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        /* 头部右侧按钮区域 */
        .header-right {
            display: flex;
            align-items: center;
            justify-content: flex-end;
        }
        
        /* 搜索图标按钮 */
        .header-search-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            color: white;
            border-radius: 50%;
        }
        
        .header-search-icon:active {
            background-color: rgba(255,255,255,0.1);
        }
        
        /* 搜索弹出层样式 */
        .search-layer {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            background-color: var(--primary-color);
            transform: translateY(-100%);
            transition: transform 0.3s ease;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .search-layer.active {
            transform: translateY(0);
        }
        
        .search-header {
            display: flex;
            align-items: center;
            height: 50px;
            padding: 0 10px;
        }
        
        .search-back {
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            margin-right: 10px;
            border: none;
            font-size: 14px;
            cursor: pointer;
        }
        
        .search-form {
            flex: 1;
            display: flex;
            align-items: center;
            background: rgba(255,255,255,0.2);
            border-radius: 18px;
            height: 36px;
            padding: 0 15px;
        }
        
        .search-icon {
            color: rgba(255,255,255,0.8);
            margin-right: 10px;
            font-size: 14px;
        }
        
        .search-input {
            flex: 1;
            border: none;
            background: transparent;
            color: #fff;
            font-size: 14px;
            outline: none;
            height: 100%;
        }
        
        .search-input::placeholder {
            color: rgba(255,255,255,0.7);
        }
        
        .search-cancel {
            margin-left: 10px;
            color: #fff;
            background: transparent;
            border: none;
            font-size: 14px;
            padding: 0 5px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <header>
        <div class="header-inner">
            <div class="header-left">
                <a href="javascript:history.back();" class="header-back">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </div>
            <div class="header-title">信息详情</div>
            <div class="header-right">
                <a href="javascript:openSearchLayer();" class="header-search-icon">
                    <i class="fas fa-search"></i>
                </a>
            </div>
        </div>
    </header>

    <!-- 搜索弹出层 -->
    <div class="search-layer" id="searchLayer">
        <div class="search-header">
            <button class="search-back" onclick="closeSearchLayer()">
                <i class="fas fa-arrow-left"></i>
            </button>
            <form action="/search.php" method="get" class="search-form" id="view-search-form">
                <span class="search-icon" id="view-search-icon">
                    <i class="fas fa-search"></i>
                </span>
                <span class="search-loading" id="view-search-loading" style="display: none;">
                    <i class="fas fa-spinner fa-spin"></i>
                </span>
                <input type="text" name="keyword" class="search-input" placeholder="输入关键词搜索..." id="searchInput" autocomplete="off">
                <input type="hidden" name="category_id" value="<?php echo (isset($post['category_id'])) ? $post['category_id'] : ""; ?>">
            </form>
            <button class="search-cancel" onclick="closeSearchLayer()">取消</button>
        </div>
    </div>

    <div class="breadcrumb">
        <div class="container">
            <a href="/">首页</a>
            <span class="separator"></span>
            <?php if($post['parent_category_id'] > 0): ?>
            <a href="/<?php echo (isset($post['parent_category_pinyin'])) ? $post['parent_category_pinyin'] : ""; ?>/"><?php echo (isset($post['parent_category_name'])) ? $post['parent_category_name'] : ""; ?></a>
            <span class="separator"></span>
            <?php endif; ?>
            <a href="/<?php echo (isset($post['category_pinyin'])) ? $post['category_pinyin'] : ""; ?>/"><?php echo (isset($post['category_name'])) ? $post['category_name'] : ""; ?></a>
            <span class="separator"></span>
            <span class="current">详情</span>
        </div>
    </div>

    <div class="post-header skeleton-container">
        <div class="container">
            <h1 class="post-title <?php if(empty($post['title'])): ?>skeleton<?php endif; ?>"><?php echo (isset($post['title'])) ? $post['title'] : ""; ?></h1>
            
            <!-- 简化信息布局 - 单行显示带图标 -->
            <div class="post-info-simple">
                <div class="info-item">
                  
                    编号:<?php echo (isset($post['id'])) ? $post['id'] : ""; ?>
                </div>
                <div class="info-item">
                    <i class="far fa-clock"></i>
                    时间:<?php echo friendlyTime($post['updated_at']); ?>
                </div>
                <div class="info-item">
                    <i class="fas fa-calendar-alt"></i>
                    有效期:<?php 
                    if (!empty($post['expired_at'])) {
                        $days = getRemainingDaysInt($post['expired_at']);
                        echo $days > 0 ? $days.' 天' : '已过期';
                    } else {
                        echo '长期';
                    }
                     ?>
                </div>
                <div class="info-item">
                    <i class="fas fa-eye"></i>
                    浏览:<?php echo (isset($post['view_count'])) ? $post['view_count'] : ""; ?>次
                </div>
            </div>
            <?php if(null !== ($post ?? null) && is_array($post) && array_key_exists('price', $post) && !empty($post['price']) && $post['price'] > 0): ?>
            <div class="post-price <?php if(empty($post['price'])): ?>skeleton skeleton-text<?php endif; ?>">￥<?php echo number_format($post['price'], 2); ?></div>
            <?php endif; ?>
        </div>
    </div>

    <div class="post-content skeleton-container">
        <div style="position: relative; margin-top: 10px;">
            <div class="post-text <?php if(empty($post['content'])): ?>skeleton skeleton-lines<?php endif; ?>" id="post-content" style="max-height: 150px; overflow: hidden; transition: max-height 0.3s ease;">
                <?php echo (isset($post['content'])) ? $post['content'] : ""; ?>
            </div>
            <div id="content-fade" style="position: absolute; bottom: 0; left: 0; width: 100%; height: 40px; background: linear-gradient(to bottom, rgba(255,255,255,0), rgba(255,255,255,1)); display: none;"></div>
        </div>
        <div id="expand-btn" style="text-align: center; padding: 8px 0 0; color: var(--primary-color); display: none; cursor: pointer;">
            <span id="expand-text">查看全部</span> <i class="fas fa-chevron-down" id="expand-icon"></i>
        </div>

        <?php if(null !== ($images ?? null) && !empty($images)): ?>
        <div class="gallery-container" style="margin-top: 5px; display: flex; flex-wrap: wrap; gap: 4px;">
            <?php if(null !== ($images ?? null) && is_array($images)): foreach($images as $key => $image): ?>
            <div class="gallery-item <?php if(empty($image['thumb_path'])): ?>skeleton<?php endif; ?>" onclick="openLightbox(<?php echo $key ?? ""; ?>)">
                <img src="/<?php echo (isset($image['thumb_path'])) ? $image['thumb_path'] : ""; ?>" data-src="/<?php echo (isset($image['thumb_path'])) ? $image['thumb_path'] : ""; ?>" alt="<?php echo (isset($post['title'])) ? $post['title'] : ""; ?>" class="lazy-image">
            </div>
            <?php endforeach; endif; ?>
        </div>
        <?php endif; ?>

        <!-- 交易提醒 - 放在正文下方 -->
        <div class="trade-warning">
            <div class="warning-line">交易提醒：建议见面交易，当面验货付款</div>
            <div class="warning-line">安全提示：谨防网络诈骗，切勿提前转账</div>
        </div>
    </div>

    <?php if(null !== ($post ?? null) && is_array($post) && array_key_exists('fields', $post) && !empty($post['fields'])): ?>
    <div class="post-fields skeleton-container">
        <div class="post-fields-title">附加信息</div>
        <?php if(null !== ($post ?? null) && is_array($post['fields'])): foreach($post['fields'] as $field): ?>
        <div class="field-item">
            <div class="field-label"><?php echo (isset($field['label'])) ? $field['label'] : ""; ?>:</div>
            <div class="field-value <?php if(empty($field['value'])): ?>skeleton skeleton-text<?php endif; ?>"><?php echo (isset($field['value'])) ? $field['value'] : ""; ?></div>
        </div>
        <?php endforeach; endif; ?>
    </div>
    <?php endif; ?>

    <div class="contact-box skeleton-container">
        <div class="contact-title">联系方式</div>
        <?php 
        // 判断信息是否过期
        $isExpired = ($post['is_expired'] == 1);
         ?>

        <!-- 联系信息 - 使用图标风格 -->
        <div class="contact-info-meta">
            <div class="contact-meta-item">
                <i class="fas fa-user"></i>
                <span class="contact-label">称呼:</span>
                <span class="contact-value <?php if(empty($post['contact_name'])): ?>skeleton skeleton-text<?php endif; ?>"><?php echo (isset($post['contact_name'])) ? $post['contact_name'] : ""; ?></span>
            </div>
            <div class="contact-meta-item">
                <i class="fas fa-phone"></i>
                <span class="contact-label">手机:</span>
                <span class="contact-value">
                    <?php if(!$isExpired): ?>
                        <span class="phone-number <?php if(empty($post['mobile'])): ?>skeleton skeleton-text<?php endif; ?>"><?php echo (isset($post['mobile'])) ? $post['mobile'] : ""; ?></span>
                        <?php if(null !== ($post ?? null) && is_array($post) && array_key_exists('mobile', $post) && !empty($post['mobile'])): ?>
                        <?php 
                        $phoneLocation = getPhoneLocation($post['mobile']);
                         ?>
                        <?php if(null !== ($phoneLocation ?? null) && !empty($phoneLocation)): ?>
                        <span style="color: #999; font-size: 12px; margin-left: 8px; font-family: SimSun, '宋体', serif;">号码归属地：<?php echo $phoneLocation ?? ""; ?></span>
                        <?php endif; ?>
                        <?php endif; ?>
                    <?php else: ?>
                        <span class="expired-text">信息已过期，联系方式已隐藏</span>
                    <?php endif; ?>
                </span>
            </div>
            <?php if(null !== ($post ?? null) && is_array($post) && array_key_exists('contact_address', $post) && !empty($post['contact_address'])): ?>
            <div class="contact-meta-item">
                <i class="fas fa-map-marker-alt"></i>
                <span class="contact-label">地址:</span>
                <span class="contact-value <?php if(empty($post['contact_address'])): ?>skeleton skeleton-text<?php endif; ?>"><?php echo (isset($post['contact_address'])) ? $post['contact_address'] : ""; ?></span>
            </div>
            <?php endif; ?>
            <?php if(null !== ($post ?? null) && is_array($post) && array_key_exists('wechat', $post) && !empty($post['wechat'])): ?>
            <div class="contact-meta-item">
                <i class="fab fa-weixin"></i>
                <span class="contact-label">微信:</span>
                <span class="contact-value">
                    <?php if(!$isExpired): ?>
                        <span class="<?php if(empty($post['wechat'])): ?>skeleton skeleton-text<?php endif; ?>"><?php echo (isset($post['wechat'])) ? $post['wechat'] : ""; ?></span>
                    <?php else: ?>
                        <span class="expired-text">信息已过期，微信已隐藏</span>
                    <?php endif; ?>
                </span>
            </div>
            <?php endif; ?>
        </div>

        <?php if(!$isExpired): ?>
        <div class="call-btn-container">
            <a href="tel:<?php echo (isset($post['mobile'])) ? $post['mobile'] : ""; ?>" class="call-btn <?php if(empty($post['mobile'])): ?>skeleton<?php endif; ?>">
                <i class="fas fa-phone-alt" style="margin-right: 8px;"></i>拨打电话
            </a>
        </div>
        <?php endif; ?>
    </div>

    <?php if(null !== ($related_posts ?? null) && !empty($related_posts)): ?>
    <!-- 相关信息部分已删除 -->
    <?php endif; ?>

    <!-- 添加图片灯箱等现有脚本 -->
    <?php if(null !== ($images ?? null) && !empty($images)): ?>
    <div class="lightbox" id="lightbox">
        <img src="" alt="" class="lightbox-img" id="lightbox-img">
        <div class="lightbox-close" onclick="closeLightbox()"><i class="fas fa-times"></i></div>
        <div class="lightbox-prev" onclick="changeImage(-1)"><i class="fas fa-chevron-left"></i></div>
        <div class="lightbox-next" onclick="changeImage(1)"><i class="fas fa-chevron-right"></i></div>
    </div>

    <script>
        var images = [];
        <?php 
        // 在PHP中生成JavaScript数组初始化
        echo "images = [";
        if (!empty($images)) {
            foreach ($images as $image) {
                if (!empty($image['file_path'])) {
                    echo '"/' . $image['file_path'] . '",';
                }
            }
        }
        echo "];";
         ?>
        var currentImageIndex = 0;
        
        // 预加载图片，确保无论从哪个页面进入都能正确显示
        function preloadImages() {
            for (var i = 0; i < images.length; i++) {
                (function(i) {
                    var img = new Image();
                    img.onload = function() {
                        // 图片加载完成后更新对应展示图片的透明度
                        var galleryItems = document.querySelectorAll('.gallery-item img');
                        if (galleryItems[i]) {
                            galleryItems[i].style.opacity = '1';
                            galleryItems[i].classList.add('loaded');
                        }
                    };
                    img.src = images[i];
                })(i);
            }
        }
        
        function openLightbox(index) {
            currentImageIndex = index;
            document.getElementById('lightbox-img').src = images[index];
            document.getElementById('lightbox').style.display = 'block';
            document.body.style.overflow = 'hidden';
        }
        
        function closeLightbox() {
            document.getElementById('lightbox').style.display = 'none';
            document.body.style.overflow = 'auto';
        }
        
        function changeImage(step) {
            currentImageIndex = (currentImageIndex + step + images.length) % images.length;
            document.getElementById('lightbox-img').src = images[currentImageIndex];
        }
        
        // 点击图片以外的区域关闭灯箱
        document.getElementById('lightbox').addEventListener('click', function(e) {
            if (e.target === this) {
                closeLightbox();
            }
        });
        
        // 键盘控制
        document.addEventListener('keydown', function(e) {
            if (document.getElementById('lightbox').style.display === 'block') {
                if (e.key === 'Escape') {
                    closeLightbox();
                } else if (e.key === 'ArrowLeft') {
                    changeImage(-1);
                } else if (e.key === 'ArrowRight') {
                    changeImage(1);
                }
            }
        });
    </script>
    <?php endif; ?>

    <!-- 内容展开收起脚本 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            var content = document.getElementById('post-content');
            var fadeElement = document.getElementById('content-fade');
            var expandBtn = document.getElementById('expand-btn');
            var expandText = document.getElementById('expand-text');
            var expandIcon = document.getElementById('expand-icon');
            var expanded = false;
            
            // 检查内容是否足够长以需要展开按钮
            function checkContentHeight() {
                if (content.scrollHeight > 150) {
                    fadeElement.style.display = 'block';
                    expandBtn.style.display = 'block';
                }
            }
            
            // 展开/收起内容
            expandBtn.addEventListener('click', function() {
                if (!expanded) {
                    content.style.maxHeight = content.scrollHeight + 'px';
                    fadeElement.style.display = 'none';
                    expandText.textContent = '收起内容';
                    expandIcon.className = 'fas fa-chevron-up';
                    expanded = true;
                } else {
                    content.style.maxHeight = '150px';
                    fadeElement.style.display = 'block';
                    expandText.textContent = '查看全部';
                    expandIcon.className = 'fas fa-chevron-down';
                    expanded = false;
                }
            });
            
            // 初始检查
            checkContentHeight();
            
            // 骨架屏处理
            presetContentHeights();
            loadLazyImages();
            
            // 预加载图片数组，确保图片显示
            if (typeof preloadImages === 'function') {
                preloadImages();
            }
            
            // 移除FOUC类以显示内容
            setTimeout(function() {
                document.documentElement.classList.remove('no-fouc');
                document.body.classList.add('content-loaded');
            }, 100);
        });
        
        // 预设内容区域高度以减少布局晃动
        function presetContentHeights() {
            // 设置图片区域最小高度
            var galleryItems = document.querySelectorAll('.gallery-item');
            galleryItems.forEach(function(item) {
                if (!item.style.minHeight) {
                    item.style.minHeight = '100px';
                }
            });
            
            // 设置内容区域最小高度
            var contentArea = document.getElementById('post-content');
            if (contentArea && !contentArea.style.minHeight) {
                contentArea.style.minHeight = '100px';
            }
            
            // 设置联系区域最小高度
            var contactItems = document.querySelectorAll('.contact-item');
            contactItems.forEach(function(item) {
                if (!item.style.minHeight) {
                    item.style.minHeight = '36px';
                }
            });
        }
        
        // 图片延迟加载
        function loadLazyImages() {
            var lazyImages = document.querySelectorAll('.lazy-image');
            
            // 首先设置合理的默认图片路径
            lazyImages.forEach(function(img) {
                // 确保图片源存在
                if (!img.src || img.src === window.location.href) {
                    var defaultPath = img.getAttribute('data-src') || img.src || '';
                    if (defaultPath) {
                        img.src = defaultPath;
                    }
                }
                
                // 初始设置为不透明，避免列表页进入时的闪烁
                img.style.opacity = '0';
                
                // 立即触发加载
                setTimeout(function() {
                    img.style.opacity = '1';
                    img.classList.add('loaded');
                }, 10);
                
                // 当图片加载完成时显示
                img.onload = function() {
                    this.style.opacity = '1';
                    this.classList.add('loaded');
                };
                
                // 设置图片加载失败处理
                img.onerror = function() {
                    if (!this.src.includes('no-image.png')) {
                        this.src = '/static/images/no-image.png';
                    }
                };
            });
        }
    </script>
    
    <!-- 搜索功能脚本 -->
    <script>
        // 搜索功能脚本
        function openSearchLayer() {
            var searchLayer = document.getElementById('searchLayer');
            searchLayer.style.display = 'block';
            setTimeout(function() {
                searchLayer.classList.add('active');
                document.getElementById('searchInput').focus();
            }, 10);
        }
        
        function closeSearchLayer() {
            var searchLayer = document.getElementById('searchLayer');
            searchLayer.classList.remove('active');
            setTimeout(function() {
                searchLayer.style.display = 'none';
            }, 300);
        }

        // 显示搜索加载状态
        function showViewSearchLoading() {
            var searchIcon = document.getElementById('view-search-icon');
            var searchLoading = document.getElementById('view-search-loading');

            if (searchIcon) {
                searchIcon.style.display = 'none';
            }
            if (searchLoading) {
                searchLoading.style.display = 'inline-block';
            }
        }

        // 绑定搜索表单提交事件
        document.addEventListener('DOMContentLoaded', function() {
            var viewSearchForm = document.getElementById('view-search-form');
            if (viewSearchForm) {
                viewSearchForm.addEventListener('submit', function(e) {
                    var input = document.getElementById('searchInput');
                    var keyword = input.value.trim();

                    if (keyword) {
                        showViewSearchLoading();
                    }
                });
            }
        });
    </script>

    <!-- 底部版权 -->
    <footer>
    <div class="container">
        <div class="footer-nav">
            <a href="/login.php">登录/注册</a>
            <a href="/fee.php">电话费用</a>
            <a href="/feedback.php">用户反馈</a>
        </div>
        <div class="theme-switcher">
            <p>主题切换</p>
            <div class="theme-dots">
                <a href="javascript:void(0);" onclick="switchTheme('red')" class="theme-dot theme-red" title="红色主题"></a>
                <a href="javascript:void(0);" onclick="switchTheme('blue')" class="theme-dot theme-blue" title="蓝色主题"></a>
                <a href="javascript:void(0);" onclick="switchTheme('green')" class="theme-dot theme-green" title="绿色主题"></a>
                <a href="javascript:void(0);" onclick="switchTheme('purple')" class="theme-dot theme-purple" title="紫色主题"></a>
                <a href="javascript:void(0);" onclick="switchTheme('orange')" class="theme-dot theme-orange" title="橙色主题"></a>
                <a href="javascript:void(0);" onclick="switchTheme('pink')" class="theme-dot theme-pink" title="粉色主题"></a>
                <a href="javascript:void(0);" onclick="switchTheme('ocean')" class="theme-dot theme-ocean" title="海洋主题"></a>
                <a href="javascript:void(0);" onclick="switchTheme('wechat')" class="theme-dot theme-wechat" title="微信风格"></a>
                <a href="javascript:void(0);" onclick="switchTheme('alipay')" class="theme-dot theme-alipay" title="支付宝风格"></a>
                <a href="javascript:void(0);" onclick="switchTheme('simple')" class="theme-dot theme-simple" title="简约主题"></a>
            </div>
        </div>
        <div class="footer-info">
            <p>京ICP证060405号</p>
            <p>客户服务热线: 10105858</p>
        </div>
    </div>
    <script>
    // 主题切换功能
    function switchTheme(theme) {
        // 设置cookie，有效期30天
        var date = new Date();
        date.setTime(date.getTime() + (30 * 24 * 60 * 60 * 1000));
        document.cookie = "site_theme=" + theme + "; expires=" + date.toUTCString() + "; path=/";
        
        // 更新页面上的主题类
        document.documentElement.className = document.documentElement.className.replace(/theme-\w+/g, '');
        document.documentElement.classList.add('theme-' + theme);
        
        // 同时更新body的主题类，确保背景色只应用于内容区域
        document.body.className = document.body.className.replace(/theme-\w+/g, '');
        document.body.classList.add('theme-' + theme);
        
        // 更新当前选中的主题点
        highlightCurrentTheme(theme);
        
        // 显示切换成功提示
        var themeNames = {
            'red': '红色',
            'blue': '蓝色',
            'green': '绿色',
            'purple': '紫色',
            'orange': '橙色',
            'pink': '粉色',
            'ocean': '海洋',
            'wechat': '微信风格',
            'alipay': '支付宝风格',
            'miui': '小米风格',
            'douyin': '抖音风格',
            'simple': '简约'
        };
        
        // 创建提示元素
        var toast = document.createElement('div');
        toast.className = 'theme-toast';
        toast.textContent = '已切换到' + themeNames[theme] + '主题';
        document.body.appendChild(toast);
        
        // 2秒后移除提示
        setTimeout(function() {
            toast.classList.add('hide');
            setTimeout(function() {
                document.body.removeChild(toast);
            }, 300);
        }, 2000);
    }
    
    // 高亮当前主题
    function highlightCurrentTheme(theme) {
        // 移除所有主题点的高亮
        var themeDots = document.querySelectorAll('.theme-dot');
        themeDots.forEach(function(dot) {
            dot.classList.remove('active');
        });
        
        // 添加当前主题的高亮
        var currentThemeDot = document.querySelector('.theme-dot.theme-' + theme);
        if (currentThemeDot) {
            currentThemeDot.classList.add('active');
        }
    }
    
    // 在页面加载时，根据当前主题高亮对应的主题点
    document.addEventListener('DOMContentLoaded', function() {
        // 获取当前主题
        var currentTheme = 'red'; // 默认主题
        var htmlClass = document.documentElement.className;
        var themeMatch = htmlClass.match(/theme-(\w+)/);
        
        if (themeMatch && themeMatch[1]) {
            currentTheme = themeMatch[1];
            
            // 确保body也具有相同的主题类
            document.body.className = document.body.className.replace(/theme-\w+/g, '');
            document.body.classList.add('theme-' + currentTheme);
        }
        
        // 高亮当前主题
        highlightCurrentTheme(currentTheme);
    });
    </script>
    <style>
    /* 主题切换样式 */
    html, body {
        min-height: 100%;
    }
    
    body {
        background-color: #f5f5f5; /* 默认背景色 */
        margin: 0;
        padding: 0;
    }
    
    /* 确保主题颜色只应用于body */
    html.theme-red, html.theme-blue, html.theme-green, 
    html.theme-purple, html.theme-orange, html.theme-pink,
    html.theme-ocean, html.theme-wechat, html.theme-alipay,
    html.theme-miui, html.theme-douyin {
        background-color: #fff; /* 重置HTML背景为白色 */
    }
    
    /* 主题背景色应用到body */
    body.theme-red { background-color: #fff5f5; }
    body.theme-blue { background-color: #f5f8ff; }
    body.theme-green { background-color: #f5fff8; }
    body.theme-purple { background-color: #f8f5ff; }
    body.theme-orange { background-color: #fff9f5; }
    body.theme-pink { background-color: #fff5f9; }
    body.theme-ocean { background-color: #f5faff; }
    body.theme-wechat { background-color: #f5fff7; }
    body.theme-alipay { background-color: #f5faff; }
    body.theme-simple { background-color: #f8f8f8; }
    
    .theme-switcher {
        text-align: center;
        margin: 15px 0;
        padding: 10px;
        background-color: rgba(255, 255, 255, 0.9);
        border-radius: 10px;
    }
    
    .theme-switcher p {
        margin: 0 0 8px 0;
        font-size: 14px;
        color: #666;
        font-weight: 500;
    }
    
    .theme-dots {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 12px;
        padding: 5px;
    }
    
    .theme-dot {
        display: inline-block;
        width: 22px;
        height: 22px;
        border-radius: 50%;
        cursor: pointer;
        transition: transform 0.2s, box-shadow 0.2s;
        border: 2px solid #fff;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    
    .theme-dot:hover {
        transform: scale(1.2);
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }
    
    .theme-dot.active {
        transform: scale(1.2);
        box-shadow: 0 0 0 2px #fff, 0 0 0 4px var(--primary-color, currentColor);
    }
    
    /* 主题颜色 */
    .theme-red {
        background-color: #e53935;
    }
    
    .theme-blue {
        background-color: #4285f4;
    }
    
    .theme-green {
        background-color: #00a878;
    }
    
    .theme-purple {
        background-color: #7b68ee;
    }
    
    .theme-orange {
        background-color: #ff6b01;
    }
    
    .theme-pink {
        background-color: #e91e63;
    }
    
    .theme-ocean {
        background-color: #006994;
    }
    
    .theme-wechat {
        background-color: #07c160;
    }
    
    .theme-alipay {
        background-color: #1677ff;
    }
    
    .theme-simple {
        background-color: #ffffff;
        border: 1px solid #eeeeee;
    }
    
    /* 主题切换toast提示 */
    .theme-toast {
        position: fixed;
        bottom: 80px;
        left: 50%;
        transform: translateX(-50%);
        background-color: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 10px 20px;
        border-radius: 20px;
        font-size: 14px;
        z-index: 1000;
        opacity: 1;
        transition: opacity 0.3s;
    }
    
    .theme-toast.hide {
        opacity: 0;
    }
    
    /* 简约主题特殊处理 */
    .theme-simple header {
        background-color: #ffffff !important;
        color: #333333 !important;
        border-bottom: 1px solid #eeeeee !important;
        box-shadow: 0 1px 3px rgba(0,0,0,0.05) !important;
    }
    
    .theme-simple .header-back,
    .theme-simple .header-title,
    .theme-simple .header-share,
    .theme-simple .header-search-icon {
        color: #333333 !important;
    }
    
    .theme-simple .header-back:active,
    .theme-simple .header-share:active,
    .theme-simple .header-search-icon:active {
        background-color: rgba(0,0,0,0.05) !important;
    }
    </style>
</footer>


    <!-- 底部导航 -->
    <nav class="navbar">
        <a href="/" class="nav-item">
            <span class="nav-icon"><i class="fas fa-home"></i></span>
            <span class="nav-text">首页</span>
        </a>
        <a href="/post.php?category_id=<?php echo (isset($post['category_id'])) ? $post['category_id'] : ""; ?>" class="nav-item">
            <span class="nav-icon"><i class="fas fa-edit"></i></span>
            <span class="nav-text">发布</span>
        </a>
        <a href="javascript:showManage();" class="nav-item">
            <span class="nav-icon"><i class="fas fa-cog"></i></span>
            <span class="nav-text">管理</span>
        </a>
        <?php if(!$isExpired): ?>
        <a href="tel:<?php echo (isset($post['mobile'])) ? $post['mobile'] : ""; ?>" class="nav-item phone-btn">
            <span class="nav-icon"><i class="fas fa-phone-alt"></i></span>
            <span class="nav-text">拨打电话</span>
        </a>
        <a href="/haibao/?id=<?php echo (isset($post['id'])) ? $post['id'] : ""; ?>" class="nav-item poster-btn">
            <span class="nav-icon"><i class="fas fa-image"></i></span>
            <span class="nav-text">生成海报</span>
        </a>
        <?php else: ?>
        <span class="nav-item phone-btn disabled" style="opacity: 0.5; cursor: not-allowed;">
            <span class="nav-icon"><i class="fas fa-phone-alt"></i></span>
            <span class="nav-text">已过期</span>
        </span>
        <a href="/haibao/?id=<?php echo (isset($post['id'])) ? $post['id'] : ""; ?>" class="nav-item poster-btn">
            <span class="nav-icon"><i class="fas fa-image"></i></span>
            <span class="nav-text">生成海报</span>
        </a>
        <?php endif; ?>
    </nav>

    <!-- 管理弹出层 -->
    <div class="manage-layer" id="manageLayer">
        <div class="manage-content-centered">
            <div id="passwordSection" class="password-dialog">
                <h3 class="manage-title">信息管理</h3>
                <p class="manage-subtitle">请输入管理密码以验证您的身份</p>
                <form action="/manage.php" method="post">
                    <input type="hidden" name="id" value="<?php echo (isset($post['id'])) ? $post['id'] : ""; ?>">
                    <div class="password-input-container">
                        <i class="fas fa-lock password-icon"></i>
                        <input type="password" class="manage-input" name="password" placeholder="请输入管理密码" required>
                    </div>
                    <button type="submit" class="manage-btn">验证密码</button>
                    <a href="javascript:;" class="manage-close" onclick="closeManage()">取消</a>
                </form>
            </div>
        </div>
    </div>

    <script>
    var manageLayer = document.getElementById('manageLayer');

    function showManage() {
        if (manageLayer) {
            manageLayer.style.display = 'flex';
            setTimeout(function() {
                manageLayer.classList.add('active');
                // 聚焦到密码输入框
                document.querySelector('.manage-input').focus();
            }, 10);
        }
    }

    function closeManage() {
        if (manageLayer) {
            manageLayer.classList.remove('active');
            setTimeout(function() {
                manageLayer.style.display = 'none';
            }, 300);
        }
    }

    // 点击遮罩层关闭
    if (manageLayer) {
        manageLayer.addEventListener('click', function(e) {
            if (e.target === this) {
                closeManage();
            }
        });
    }

    // 举报相关函数
    function showReportModal(postId) {
        window.currentReportPostId = postId;
        document.getElementById('reportModal').style.display = 'flex';
        // 清空表单
        document.querySelectorAll('input[name="report_type"]').forEach(radio => radio.checked = false);
        document.getElementById('report-content').value = '';
        document.getElementById('report-error').style.display = 'none';
    }

    function closeReportModal() {
        document.getElementById('reportModal').style.display = 'none';
    }

    function submitReport() {
        const reportType = document.querySelector('input[name="report_type"]:checked');
        const content = document.getElementById('report-content').value;
        const errorElement = document.getElementById('report-error');

        if (!reportType) {
            errorElement.textContent = '请选择举报类型';
            errorElement.style.display = 'block';
            return;
        }

        // 提交举报
        fetch('/api/report.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                post_id: window.currentReportPostId,
                report_type: reportType.value,
                content: content
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('举报提交成功，我们会尽快处理');
                closeReportModal();
            } else {
                errorElement.textContent = data.message || '举报提交失败';
                errorElement.style.display = 'block';
            }
        })
        .catch(error => {
            errorElement.textContent = '网络错误，请稍后重试';
            errorElement.style.display = 'block';
        });
    }

    // 点击遮罩层关闭举报弹窗
    document.addEventListener('DOMContentLoaded', function() {
        const reportModal = document.getElementById('reportModal');
        if (reportModal) {
            reportModal.addEventListener('click', function(event) {
                if (event.target === this) {
                    closeReportModal();
                }
            });
        }
    });
    </script>

    <!-- 举报弹出层 -->
    <div class="report-modal" id="reportModal" style="display: none;">
        <div class="report-modal-content">
            <div class="modal-header">
                <h3>举报信息</h3>
                <span class="modal-close" onclick="closeReportModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label>举报类型：</label>
                    <div class="report-types">
                        <label><input type="radio" name="report_type" value="1"> 虚假信息</label>
                        <label><input type="radio" name="report_type" value="2"> 诈骗信息</label>
                        <label><input type="radio" name="report_type" value="3"> 违法违规</label>
                        <label><input type="radio" name="report_type" value="4"> 其他问题</label>
                    </div>
                </div>
                <div class="form-group">
                    <label>详细说明（可选）：</label>
                    <textarea id="report-content" placeholder="请简要说明举报原因..." rows="3"></textarea>
                </div>
                <div class="error-message" id="report-error" style="display: none; color: #f00; font-size: 13px; margin-top: 6px;"></div>
            </div>
            <div class="modal-footer">
                <button type="button" onclick="closeReportModal()" class="btn-cancel">取消</button>
                <button type="button" onclick="submitReport()" class="btn-submit">提交举报</button>
            </div>
        </div>
    </div>

    <style>
    /* 举报弹出层样式 */
    .report-modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1000;
        align-items: center;
        justify-content: center;
    }

    .report-modal-content {
        background-color: white;
        border-radius: 8px;
        width: 90%;
        max-width: 380px;
        margin: 20px;
    }

    .modal-header {
        padding: 12px 16px;
        border-bottom: 1px solid #eee;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .modal-header h3 {
        margin: 0;
        font-size: 16px;
        color: #333;
    }

    .modal-close {
        font-size: 20px;
        cursor: pointer;
        color: #999;
        line-height: 1;
    }

    .modal-body {
        padding: 16px;
    }

    .form-group {
        margin-bottom: 12px;
    }

    .form-group label {
        display: block;
        margin-bottom: 6px;
        font-weight: 500;
        color: #333;
        font-size: 14px;
    }

    .report-types {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 6px;
    }

    .report-types label {
        display: flex;
        align-items: center;
        padding: 8px 10px;
        border: 1px solid #ddd;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.2s;
        font-weight: normal;
        font-size: 13px;
    }

    .report-types label:hover {
        background-color: #f5f5f5;
        border-color: var(--primary-color);
    }

    .report-types input[type="radio"] {
        margin-right: 6px;
    }

    .report-types label:has(input:checked) {
        background-color: var(--primary-color-light, #e7f3ff);
        border-color: var(--primary-color);
        color: var(--primary-color);
    }

    #report-content {
        width: 100%;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 6px;
        resize: vertical;
        font-family: inherit;
        font-size: 13px;
        box-sizing: border-box;
        min-height: 60px;
    }

    .modal-footer {
        padding: 12px 16px;
        border-top: 1px solid #eee;
        display: flex;
        justify-content: flex-end;
        gap: 8px;
    }

    .btn-cancel, .btn-submit {
        padding: 8px 16px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        transition: all 0.2s;
    }

    .btn-cancel {
        background-color: #f5f5f5;
        color: #666;
    }

    .btn-cancel:hover {
        background-color: #e8e8e8;
    }

    .btn-submit {
        background-color: var(--primary-color, #007bff);
        color: white;
    }

    .btn-submit:hover {
        background-color: var(--primary-color-dark, #0056b3);
    }
    </style>








    </script>

</body>
</html>