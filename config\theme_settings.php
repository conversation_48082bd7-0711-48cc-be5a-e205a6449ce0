<?php
/**
 * 主题设置文件
 * 主题颜色定义已移至 themes.css
 * 此文件仅处理主题切换逻辑
 */

//====================================================
// 网站主题设置 - 请在此处配置
//====================================================

// 当前启用的主题，通过修改此值可以切换网站的配色方案
// 可选值: blue(蓝色科技), orange(橙色经典), green(绿色自然), purple(紫色雅致), red(红色活力)
// dark(深色), pink(粉色), ocean(海洋)
// 新增: wechat(微信风格), alipay(支付宝风格), douyin(抖音风格), miui(小米风格)
$current_theme = isset($_COOKIE['site_theme']) ? $_COOKIE['site_theme'] : "blue";

// 所有可用主题名称
$themes = [
    'blue', 'orange', 'green', 'purple', 'red', 
    'dark', 'pink', 'ocean',
    'wechat', 'alipay', 'douyin', 'miui'
];

/**
 * 获取当前主题的类名
 * 
 * @return string 主题类名
 */
function get_theme_class() {
    global $current_theme;
    return 'theme-' . $current_theme;
}

/**
 * 切换到指定的主题
 * 
 * @param string $theme_name 主题名称
 * @return bool 是否成功切换
 */
function switch_theme($theme_name) {
    global $themes;
    
    // 验证主题是否存在
    if (!in_array($theme_name, $themes)) {
        return false;
    }
    
    // 设置cookie，有效期30天
    setcookie('site_theme', $theme_name, time() + 30 * 24 * 3600, '/');
    
    return true;
}

/**
 * 获取所有可用主题
 * 
 * @return array 主题名称数组
 */
function get_available_themes() {
    global $themes;
    return $themes;
}

/**
 * 获取当前启用的主题名称
 * 
 * @return string 当前主题名称
 */
function get_current_theme() {
    global $current_theme;
    return $current_theme;
}