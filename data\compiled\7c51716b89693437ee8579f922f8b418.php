<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>泊头生活网 - 选择发布栏目</title>
    <meta name="keywords" content="泊头生活网,泊头信息网,泊头信息港,泊头生活信息网站" />
    <meta name="description" content="泊头生活网(泊头信息网)，河北泊头生活信息网站。" />
    <link rel="stylesheet" href="/template/pc/css/common.css">
    <link rel="stylesheet" href="/template/pc/css/post.css?v=20250408">
    <link rel="stylesheet" href="/static/font-awesome/css/all.min.css">
    <script type="text/javascript" src="/template/pc/js/m.js"></script>
</head>
<body>
    <!-- 顶部 -->
    <div class="yui-top yui-1200">
    <div class="yui-top-center">
        <div class="yui-top-left yui-left">
            <a href="/">网站首页</a>
            <a href="#">移动版</a>
            <a href="#">微信公众号</a>
            <a href="#">快速发布</a>
        </div>

        <div class="yui-top-right yui-right yui-text-right">
            <a href="#">登录</a><a href="#">注册</a><div class="yui-top-dropdown">
                <span class="yui-top-dropdown-btn">会员中心</span>
                <ul class="yui-top-dropdown-menu">
                    <li><a href="#">我的信息</a></li>
                    <li><a href="#">我的收藏</a></li>
                    <li><a href="#">账号设置</a></li>
                </ul>
            </div><div class="yui-top-dropdown">
                <span class="yui-top-dropdown-btn">商家中心</span>
                <ul class="yui-top-dropdown-menu">
                    <li><a href="#">商家入驻</a></li>
                    <li><a href="#">商家管理</a></li>
                    <li><a href="#">营销推广</a></li>
                </ul>
            </div><div class="yui-top-dropdown">
                <span class="yui-top-dropdown-btn">网站导航</span>
                <ul class="yui-top-dropdown-menu">
                    <li><a href="#">关于我们</a></li>
                    <li><a href="#">联系我们</a></li>
                    <li><a href="#">使用帮助</a></li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- 简洁头部 -->
<div class="simple-header">
    <div class="simple-header-inner">
        <div class="logo-title-group">
            <div class="simple-logo">
                <a href="/"><img src="/template/pc/images/logo.png" alt="泊头生活网"></a>
            </div>
            <span class="title-separator"></span>
            <h1 class="simple-title">选择栏目</h1>
        </div>
        <a href="/" class="simple-back">返回网站首页</a>
    </div>
</div>
    
    <!-- 主内容区域 -->
    <div class="yui-content yui-1200">
        <div class="content-wrap">
            <!-- 左侧分类列表 -->
            <div class="left-column">
                <div class="pd10">
                    <div class="yui-h-title">
                        <h3><?php echo isset($selected_parent_id) ? '请选择您要发布的信息子分类' : '请选择您要发布的信息类别'; ?></h3><span></span>
                    </div>
                </div>
                
                <div class="pd10">
                    <div class="category-list">

                        <?php if (isset($selected_parent_id)): ?>
                            <!-- 显示特定一级分类及其子分类 -->
                            <?php foreach ($selected_categories as $parent): ?>
                            <div class="primary-category" style="width: 100%; float: none; clear: both;">
                                <div class="primary-category-title"><?php echo $parent['name']; ?></div>
                                <div class="secondary-categories">
                                    <?php if (isset($sub_categories_map[$parent['id']])): ?>
                                        <?php foreach ($sub_categories_map[$parent['id']] as $child): ?>
                                        <div class="secondary-category">
                                            <a href="post.php?category_id=<?php echo $child['id']; ?>"><?php echo $child['name']; ?></a>
                                        </div>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <?php endforeach; ?>

                            <!-- 添加返回选择其他分类的链接 -->
                            <div style="padding: 15px 0; text-align: center; border-top: 1px solid #f0f0f0; margin-top: 10px;">
                                <a href="post.php?action=select" style="color: #0960bd; text-decoration: none; font-size: 14px;">
                                    <i class="fas fa-arrow-left"></i> 选择其他分类
                                </a>
                            </div>
                        <?php else: ?>
                            <!-- 显示所有分类（原有逻辑） -->
                            <?php
                            // 获取所有分类
                            $allCategories = $GLOBALS['cached_categories'];

                            // 按父子关系整理分类
                            $parentCategories = array();
                            $subCategories = array();

                            foreach ($allCategories as $cat) {
                                // 确保parent_id和id字段存在
                                $parentId = isset($cat['parent_id']) ? $cat['parent_id'] : 0;
                                $catId = isset($cat['id']) ? $cat['id'] : 0;

                                if ($parentId == 0 && $catId > 0) {
                                    $parentCategories[$catId] = $cat;
                                } else if ($catId > 0) {
                                    if (!isset($subCategories[$parentId])) {
                                        $subCategories[$parentId] = array();
                                    }
                                    $subCategories[$parentId][] = $cat;
                                }
                            }

                            // 遍历父级分类
                            foreach ($parentCategories as $parentId => $parent) {
                            ?>
                            <div class="primary-category" style="width: 100%; float: none; clear: both;">
                                <div class="primary-category-title"><?php echo isset($parent['name']) ? $parent['name'] : '未命名分类'; ?></div>
                                <div class="secondary-categories">
                                    <?php
                                    if (isset($subCategories[$parentId])) {
                                        foreach ($subCategories[$parentId] as $child) {
                                            // 确保id字段存在
                                            $childId = isset($child['id']) ? $child['id'] : 0;
                                            if ($childId > 0) {
                                    ?>
                                    <div class="secondary-category">
                                        <a href="post.php?category_id=<?php echo $childId; ?>"><?php echo isset($child['name']) ? $child['name'] : '未命名子分类'; ?></a>
                                    </div>
                                    <?php
                                            }
                                        }
                                    }
                                    ?>
                                </div>
                            </div>
                            <?php } ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- 底部 -->
    <div class="yui-footer">
    <div class="yui-1200">
        <div class="footer-content bg-white">
            <!-- 友情链接区域 -->
          
            <p class="footer-nav">
                <a href="https://www.botou.net/" title="泊头生活网">网站首页</a>
                <a href="https://www.botou.net/aboutus/tuiguang.html" target="_blank">广告服务</a>
                <a href="https://www.botou.net/aboutus/shenmin.html" target="_blank">法律声明</a>
                <a href="https://www.botou.net/aboutus/about.html" target="_blank">网站介绍</a>
                <a href="https://www.botou.net/aboutus/contactus.html" target="_blank">联系我们</a>
                <a href="https://www.botou.net/aboutus/job.html" target="_blank">招聘信息</a>
            </p>
            <p class="footer-disclaimer">2本站信息均由网民发表,不代表本网站立场,如侵犯了您的权利请致电投诉</p>
            <p class="footer-disclaimer">客服电话： &nbsp; 客服邮箱：<font><EMAIL></font> <a href="http://cyberpolice.mps.gov.cn/wfjb/" target="_blank" rel="nofollow">网络违法犯罪举报网站</a></p>
            <p class="footer-copyright"><?php if($site_copyright): ?><?php echo $site_copyright ?? ""; ?><?php else: ?>Copyright © 2024 分类信息网站 All Rights Reserved<?php endif; ?></p>
            <?php if($site_icp): ?><p class="footer-copyright"><a href="https://beian.miit.gov.cn/" target="_blank" id="footericp" rel="nofollow"><?php echo $site_icp ?? ""; ?></a></p><?php endif; ?>
        </div>
    </div>
</div>
</body>
</html>