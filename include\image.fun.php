<?php
/**
 * 图片处理相关函数库
 */

// 定义安全常量，禁止直接访问该文件
if (!defined('IN_BTMPS')) {
    exit('Access Denied');
}

/**
 * 获取信息的图片
 * @param int $postId 信息ID
 * @param bool $getAllImages 是否获取所有图片，默认为false只获取第一张
 * @return mixed 单张图片时返回图片路径字符串，获取所有图片时返回图片数组
 */
function getPostImage($postId, $getAllImages = false) {
    global $db;
    
    // 根据是否需要获取所有图片，调整SQL查询
    if ($getAllImages) {
        $sql = "SELECT * FROM attachments WHERE post_id = ? ORDER BY sort_order ASC, id ASC";
        $result = $db->query($sql, [$postId]);
        
        $images = array();
        while ($result && $row = $db->fetch_array($result)) {
            $images[] = $row;
        }
        
        return $images;
    } else {
        // 只获取第一张图片
        $sql = "SELECT thumb_path FROM attachments WHERE post_id = ? ORDER BY sort_order ASC LIMIT 1";
        $result = $db->query($sql, [$postId]);
        if ($row = $db->fetch_array($result)) {
            $imagePath = $row['thumb_path'];
            // 确保路径以斜杠开头
            if (substr($imagePath, 0, 1) !== '/') {
                $imagePath = '/' . $imagePath;
            }
        } else {
            // 默认图片
            $imagePath = '/uploads/images/default.jpg';
        }
        
        return $imagePath;
    }
}

/**
 * 删除图片
 * @param int $imageId 图片ID
 * @return bool 删除结果
 */
function deleteImage($imageId) {
    global $db;
    
    // 首先获取图片信息
    $sql = "SELECT * FROM attachments WHERE id = ?";
    $result = $db->query($sql, [$imageId]);
    $image = $db->fetch_array($result);
    
    if ($image) {
        // 删除物理文件
        if (file_exists(ROOT_PATH . $image['file_path'])) {
            @unlink(ROOT_PATH . $image['file_path']);
        }
        
        if (file_exists(ROOT_PATH . $image['thumb_path'])) {
            @unlink(ROOT_PATH . $image['thumb_path']);
        }
        
        // 删除数据库记录
        $sql = "DELETE FROM attachments WHERE id = ?";
        return $db->query($sql, [$imageId]);
    }
    
    return false;
}

/**
 * 删除指定信息的所有附件
 * @param int $postId 信息ID
 * @return bool 是否成功删除所有附件
 */
function deletePostAttachments($postId) {
    global $db;
    
    // 参数验证
    $postId = intval($postId);
    if ($postId <= 0) {
        return false;
    }
    
    // 查询该信息的所有附件
    $sql = "SELECT * FROM attachments WHERE post_id = ?";
    $result = $db->query($sql, [$postId]);
    
    $success = true;
    while ($image = $db->fetch_array($result)) {
        // 删除物理文件
        if (file_exists(ROOT_PATH . $image['file_path'])) {
            if (!@unlink(ROOT_PATH . $image['file_path'])) {
                $success = false;
                error_log("删除附件文件失败：" . ROOT_PATH . $image['file_path']);
            }
        }
        
        if (file_exists(ROOT_PATH . $image['thumb_path'])) {
            if (!@unlink(ROOT_PATH . $image['thumb_path'])) {
                $success = false;
                error_log("删除附件缩略图失败：" . ROOT_PATH . $image['thumb_path']);
            }
        }
    }
    
    // 删除数据库记录
    $sql = "DELETE FROM attachments WHERE post_id = ?";
    if (!$db->query($sql, [$postId])) {
        $success = false;
        error_log("删除附件数据库记录失败：post_id = " . $postId);
    }
    
    return $success;
}

/**
 * 上传并保存图片到指定信息ID
 * @param int $postId 信息ID
 * @return void
 */
function saveImages($postId) {
    global $db, $config;

    // 上传图片的最大数量 - 从配置文件读取
    $maxImages = isset($config['upload_image_count']) ? intval($config['upload_image_count']) : 10;

    // 图片大小限制 - 从配置文件读取（MB转换为字节）
    $maxSize = isset($config['upload_image_size']) ? intval($config['upload_image_size']) * 1024 * 1024 : 4 * 1024 * 1024;

    // 允许的扩展名 - 从配置文件读取
    $allowedExtensions = isset($config['allowed_extensions']) ? explode(',', $config['allowed_extensions']) : ['jpg', 'jpeg', 'png', 'gif'];

    // 检查已有图片数量
    $sql = "SELECT COUNT(*) as count FROM attachments WHERE post_id = ?";
    $result = $db->query($sql, [$postId]);
    $existingCount = 0;
    if ($row = $db->fetch_array($result)) {
        $existingCount = intval($row['count']);
    }

    // 计算可以上传的图片数量
    $remainingSlots = $maxImages - $existingCount;
    if ($remainingSlots <= 0) {
        error_log("图片数量已达上限：已有{$existingCount}张图片，最大允许{$maxImages}张");
        return;
    }
    
    // 处理上传的图片
    if (!empty($_FILES['images']['name'][0])) {
        $uploadCount = min(count($_FILES['images']['name']), $remainingSlots);

        if ($uploadCount <= 0) {
            error_log("无法上传更多图片：已有{$existingCount}张图片，最大允许{$maxImages}张");
            return;
        }
        
        for ($i = 0; $i < $uploadCount; $i++) {
            if ($_FILES['images']['error'][$i] == 0) {
                $fileName = $_FILES['images']['name'][$i];
                $fileSize = $_FILES['images']['size'][$i];
                $fileTmp = $_FILES['images']['tmp_name'][$i];
                $fileType = $_FILES['images']['type'][$i];

                // 检查文件大小
                if ($fileSize > $maxSize) {
                    error_log("文件大小超出限制：{$fileName} ({$fileSize} bytes > {$maxSize} bytes)");
                    continue;
                }

                // 检查文件扩展名
                $fileExtension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
                if (!in_array($fileExtension, $allowedExtensions)) {
                    error_log("文件扩展名不允许：{$fileName} (.{$fileExtension})");
                    continue;
                }

                // 生成唯一文件名
                $newFileName = date('YmdHis') . '_' . rand(1000, 9999) . '.' . $fileExtension;
                $uploadPath = UPLOAD_PATH . 'images/' . date('Y') . '/' . date('md') . '/';
                
                // 确保目录存在
                if (!is_dir($uploadPath)) {
                    mkdir($uploadPath, 0777, true);
                }
                
                $filePath = $uploadPath . $newFileName;
                
                // 上传文件
                if (move_uploaded_file($fileTmp, $filePath)) {
                    // 暂时禁用服务端压缩，使用客户端压缩的结果
                    // $compressedPath = compressServerImage($filePath, $fileExtension);
                    // if ($compressedPath && $compressedPath !== $filePath) {
                    //     // 如果压缩成功，替换原文件
                    //     unlink($filePath);
                    //     rename($compressedPath, $filePath);
                    //     // 更新文件大小
                    //     $fileSize = filesize($filePath);
                    // }

                    // 创建缩略图目录
                    $thumbDir = dirname($filePath) . '/thumbs/';
                    if (!is_dir($thumbDir)) {
                        mkdir($thumbDir, 0777, true);
                    }

                    // 生成缩略图
                    $thumbPath = $thumbDir . basename($filePath);

                    // 使用优化的缩略图生成函数
                    if (createThumbnail($filePath, $thumbPath, 300, 300, 85)) {
                        // 缩略图生成成功
                    } else {
                        // 备用方案：使用原有方法
                        $image = imagecreatefromstring(file_get_contents($filePath));
                        if ($image) {
                            $width = imagesx($image);
                            $height = imagesy($image);

                            // 计算缩略图尺寸
                            $maxWidth = 300;
                            $maxHeight = 300;

                            if ($width > $height) {
                                $newWidth = $maxWidth;
                                $newHeight = intval($height * $maxWidth / $width);
                            } else {
                                $newHeight = $maxHeight;
                                $newWidth = intval($width * $maxHeight / $height);
                            }

                            $thumb = imagecreatetruecolor($newWidth, $newHeight);
                            imagecopyresampled($thumb, $image, 0, 0, 0, 0, $newWidth, $newHeight, $width, $height);

                            // 保存缩略图
                            imagejpeg($thumb, $thumbPath, 85);
                            imagedestroy($image);
                            imagedestroy($thumb);
                        }
                    }
                    
                    // 相对路径
                    $relativeFilePath = str_replace(ROOT_PATH, '', $filePath);
                    $relativeThumbPath = str_replace(ROOT_PATH, '', $thumbPath);
                    
                    // 使用参数化查询保存到数据库
                    $sql = "INSERT INTO attachments (post_id, file_path, thumb_path, file_size, file_type, sort_order, created_at) 
                            VALUES (?, ?, ?, ?, ?, ?, ?)";
                    
                    $params = [
                        $postId,
                        $relativeFilePath,
                        $relativeThumbPath,
                        $fileSize,
                        $fileType,
                        $i,
                        time()
                    ];
                    
                    $db->query($sql, $params);
                }
            }
        }
    }
}

/**
 * 生成图片缩略图
 * @param string $srcPath 原图路径
 * @param string $targetPath 目标路径
 * @param int $maxWidth 最大宽度
 * @param int $maxHeight 最大高度
 * @param int $quality 图片质量(1-100)
 * @return bool 是否成功
 */
function createThumbnail($srcPath, $targetPath, $maxWidth = 300, $maxHeight = 300, $quality = 90) {
    // 检查源文件是否存在
    if (!file_exists($srcPath)) {
        return false;
    }
    
    // 确保目标目录存在
    $targetDir = dirname($targetPath);
    if (!is_dir($targetDir)) {
        mkdir($targetDir, 0777, true);
    }
    
    // 获取图片信息
    $imageInfo = getimagesize($srcPath);
    if ($imageInfo === false) {
        return false;
    }
    
    $width = $imageInfo[0];
    $height = $imageInfo[1];
    $type = $imageInfo[2];
    
    // 创建源图像资源
    switch ($type) {
        case IMAGETYPE_JPEG:
            $source = imagecreatefromjpeg($srcPath);
            break;
        case IMAGETYPE_PNG:
            $source = imagecreatefrompng($srcPath);
            break;
        case IMAGETYPE_GIF:
            $source = imagecreatefromgif($srcPath);
            break;
        default:
            return false;
    }
    
    if (!$source) {
        return false;
    }
    
    // 计算缩略图尺寸，保持宽高比
    if ($width > $height) {
        $newWidth = $maxWidth;
        $newHeight = intval($height * $maxWidth / $width);
    } else {
        $newHeight = $maxHeight;
        $newWidth = intval($width * $maxHeight / $height);
    }
    
    // 创建缩略图
    $thumb = imagecreatetruecolor($newWidth, $newHeight);
    
    // 处理PNG透明度
    if ($type == IMAGETYPE_PNG) {
        imagealphablending($thumb, false);
        imagesavealpha($thumb, true);
    }
    
    // 重采样
    imagecopyresampled($thumb, $source, 0, 0, 0, 0, $newWidth, $newHeight, $width, $height);
    
    // 保存缩略图
    $result = false;
    switch ($type) {
        case IMAGETYPE_JPEG:
            $result = imagejpeg($thumb, $targetPath, $quality);
            break;
        case IMAGETYPE_PNG:
            // PNG质量从0到9，与JPEG不同
            $pngQuality = round(9 - (($quality / 100) * 9));
            $result = imagepng($thumb, $targetPath, $pngQuality);
            break;
        case IMAGETYPE_GIF:
            $result = imagegif($thumb, $targetPath);
            break;
    }
    
    // 释放资源
    imagedestroy($source);
    imagedestroy($thumb);
    
    return $result;
}

/**
 * 水印处理
 * @param string $imagePath 图片路径
 * @param string $watermarkPath 水印图片路径
 * @param string $position 水印位置(top-left, top-right, bottom-left, bottom-right, center)
 * @param int $opacity 不透明度(0-100)
 * @return bool 是否成功
 */
function applyWatermark($imagePath, $watermarkPath, $position = 'bottom-right', $opacity = 50) {
    // 检查文件是否存在
    if (!file_exists($imagePath) || !file_exists($watermarkPath)) {
        return false;
    }
    
    // 获取图片信息
    $imageInfo = getimagesize($imagePath);
    $watermarkInfo = getimagesize($watermarkPath);
    
    if ($imageInfo === false || $watermarkInfo === false) {
        return false;
    }
    
    // 创建图像资源
    switch ($imageInfo[2]) {
        case IMAGETYPE_JPEG:
            $image = imagecreatefromjpeg($imagePath);
            break;
        case IMAGETYPE_PNG:
            $image = imagecreatefrompng($imagePath);
            break;
        case IMAGETYPE_GIF:
            $image = imagecreatefromgif($imagePath);
            break;
        default:
            return false;
    }
    
    switch ($watermarkInfo[2]) {
        case IMAGETYPE_JPEG:
            $watermark = imagecreatefromjpeg($watermarkPath);
            break;
        case IMAGETYPE_PNG:
            $watermark = imagecreatefrompng($watermarkPath);
            break;
        case IMAGETYPE_GIF:
            $watermark = imagecreatefromgif($watermarkPath);
            break;
        default:
            return false;
    }
    
    // 确保图像有效
    if (!$image || !$watermark) {
        return false;
    }
    
    // 计算水印位置
    $imageWidth = imagesx($image);
    $imageHeight = imagesy($image);
    $watermarkWidth = imagesx($watermark);
    $watermarkHeight = imagesy($watermark);
    
    // 定义边距
    $margin = 10;
    
    // 根据位置设置坐标
    switch ($position) {
        case 'top-left':
            $x = $margin;
            $y = $margin;
            break;
        case 'top-right':
            $x = $imageWidth - $watermarkWidth - $margin;
            $y = $margin;
            break;
        case 'bottom-left':
            $x = $margin;
            $y = $imageHeight - $watermarkHeight - $margin;
            break;
        case 'center':
            $x = ($imageWidth - $watermarkWidth) / 2;
            $y = ($imageHeight - $watermarkHeight) / 2;
            break;
        case 'bottom-right':
        default:
            $x = $imageWidth - $watermarkWidth - $margin;
            $y = $imageHeight - $watermarkHeight - $margin;
            break;
    }
    
    // 应用水印
    imagecopymerge($image, $watermark, $x, $y, 0, 0, $watermarkWidth, $watermarkHeight, $opacity);
    
    // 保存图片
    $result = false;
    switch ($imageInfo[2]) {
        case IMAGETYPE_JPEG:
            $result = imagejpeg($image, $imagePath, 90);
            break;
        case IMAGETYPE_PNG:
            $result = imagepng($image, $imagePath);
            break;
        case IMAGETYPE_GIF:
            $result = imagegif($image, $imagePath);
            break;
    }
    
    // 释放资源
    imagedestroy($image);
    imagedestroy($watermark);
    
    return $result;
}

/**
 * 服务端图片压缩
 * @param string $imagePath 图片路径
 * @param string $extension 文件扩展名
 * @return string|false 压缩后的文件路径，失败返回false
 */
function compressServerImage($imagePath, $extension) {
    if (!file_exists($imagePath)) {
        return false;
    }

    // 获取图片信息
    $imageInfo = getimagesize($imagePath);
    if ($imageInfo === false) {
        return false;
    }

    $width = $imageInfo[0];
    $height = $imageInfo[1];
    $type = $imageInfo[2];
    $fileSize = filesize($imagePath);

    // 如果图片已经很小，不需要压缩
    if ($fileSize <= 2 * 1024 * 1024 && $width <= 1920 && $height <= 1080) {
        return $imagePath;
    }

    // 创建图片资源
    $source = null;
    switch ($type) {
        case IMAGETYPE_JPEG:
            $source = imagecreatefromjpeg($imagePath);
            break;
        case IMAGETYPE_PNG:
            $source = imagecreatefrompng($imagePath);
            break;
        case IMAGETYPE_GIF:
            $source = imagecreatefromgif($imagePath);
            break;
        default:
            return false;
    }

    if (!$source) {
        return false;
    }

    // 计算新尺寸
    $maxWidth = 1920;
    $maxHeight = 1080;
    $newWidth = $width;
    $newHeight = $height;

    if ($width > $maxWidth || $height > $maxHeight) {
        $aspectRatio = $width / $height;

        if ($width > $maxWidth) {
            $newWidth = $maxWidth;
            $newHeight = intval($newWidth / $aspectRatio);
        }

        if ($newHeight > $maxHeight) {
            $newHeight = $maxHeight;
            $newWidth = intval($newHeight * $aspectRatio);
        }
    }

    // 创建新图片
    $newImage = imagecreatetruecolor($newWidth, $newHeight);

    // 处理PNG透明度
    if ($type == IMAGETYPE_PNG) {
        imagealphablending($newImage, false);
        imagesavealpha($newImage, true);
        $transparent = imagecolorallocatealpha($newImage, 255, 255, 255, 127);
        imagefill($newImage, 0, 0, $transparent);
    }

    // 重采样
    imagecopyresampled($newImage, $source, 0, 0, 0, 0, $newWidth, $newHeight, $width, $height);

    // 生成压缩后的文件路径
    $pathInfo = pathinfo($imagePath);
    $compressedPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '_compressed.' . $pathInfo['extension'];

    // 保存压缩后的图片
    $result = false;
    $quality = 85; // 高质量压缩

    switch ($type) {
        case IMAGETYPE_JPEG:
            $result = imagejpeg($newImage, $compressedPath, $quality);
            break;
        case IMAGETYPE_PNG:
            // PNG质量从0到9，与JPEG不同
            $pngQuality = round(9 - (($quality / 100) * 9));
            $result = imagepng($newImage, $compressedPath, $pngQuality);
            break;
        case IMAGETYPE_GIF:
            $result = imagegif($newImage, $compressedPath);
            break;
    }

    // 释放资源
    imagedestroy($source);
    imagedestroy($newImage);

    if ($result && file_exists($compressedPath)) {
        return $compressedPath;
    }

    return false;
}
