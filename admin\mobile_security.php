<?php
// 定义安全常量
define('IN_BTMPS', true);

/**
 * 手机号安全监控管理页面
 */
require_once(dirname(__FILE__) . '/../include/common.inc.php');

// 检查管理员是否登录
if (!isset($_SESSION['admin']) || $_SESSION['admin']['is_login'] !== true) {
    header("Location: login.php");
    exit;
}

// 页面标题
$page_title = '手机号安全监控';

// 处理AJAX请求
if (isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    switch ($_POST['action']) {
        case 'reset_mobile_limit':
            $mobile = trim($_POST['mobile']);
            if (empty($mobile)) {
                echo json_encode(['success' => false, 'message' => '手机号不能为空']);
                exit;
            }
            
            // 删除该手机号的今日记录
            $today = date('Y-m-d');
            $sql1 = "DELETE FROM mobile_daily_stats WHERE mobile = ? AND date = ?";
            $result1 = $db->query($sql1, [$mobile, $today]);
            
            $sql2 = "DELETE FROM mobile_post_records WHERE mobile = ? AND server_timestamp > ?";
            $result2 = $db->query($sql2, [$mobile, strtotime($today)]);
            
            if ($result1 !== false && $result2 !== false) {
                echo json_encode(['success' => true, 'message' => '重置成功']);
            } else {
                echo json_encode(['success' => false, 'message' => '重置失败']);
            }
            exit;
            
        case 'block_mobile':
            $mobile = trim($_POST['mobile']);
            $reason = trim($_POST['reason']);
            
            if (empty($mobile)) {
                echo json_encode(['success' => false, 'message' => '手机号不能为空']);
                exit;
            }
            
            // 这里可以添加黑名单功能
            echo json_encode(['success' => true, 'message' => '功能开发中']);
            exit;
    }
}

// 获取统计数据
$stats = [];

// 今日发布总数
$sql = "SELECT COUNT(*) as count FROM mobile_post_records WHERE server_timestamp > ?";
$result = $db->query($sql, [strtotime(date('Y-m-d'))]);
$row = $db->fetch_array($result);
$stats['today_posts'] = $row ? $row['count'] : 0;

// 今日活跃手机号数量
$sql = "SELECT COUNT(DISTINCT mobile) as count FROM mobile_post_records WHERE server_timestamp > ?";
$result = $db->query($sql, [strtotime(date('Y-m-d'))]);
$row = $db->fetch_array($result);
$stats['today_mobiles'] = $row ? $row['count'] : 0;

// 今日被限制的手机号数量
$sql = "SELECT COUNT(*) as count FROM mobile_daily_stats WHERE date = ? AND post_count >= ?";
$result = $db->query($sql, [date('Y-m-d'), $config['mobile_daily_post_limit']]);
$row = $db->fetch_array($result);
$stats['today_limited'] = $row ? $row['count'] : 0;

// 可疑时间异常记录
$sql = "SELECT COUNT(*) as count FROM mobile_post_records WHERE post_timestamp > server_timestamp + 300";
$result = $db->query($sql);
$row = $db->fetch_array($result);
$stats['suspicious_records'] = $row ? $row['count'] : 0;

// 获取今日发布记录
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$limit = 20;
$offset = ($page - 1) * $limit;

$sql = "SELECT r.*, s.post_count, s.date 
        FROM mobile_post_records r 
        LEFT JOIN mobile_daily_stats s ON r.mobile = s.mobile AND s.date = DATE(FROM_UNIXTIME(r.server_timestamp))
        WHERE r.server_timestamp > ? 
        ORDER BY r.server_timestamp DESC 
        LIMIT ? OFFSET ?";
$result = $db->query($sql, [strtotime(date('Y-m-d')), $limit, $offset]);

$records = [];
while ($row = $db->fetch_array($result)) {
    // 计算时间差异
    $time_diff = 0;
    $time_diff_status = 'normal';
    $time_diff_text = '正常';

    if ($row['post_timestamp'] && $row['server_timestamp']) {
        $time_diff = intval($row['post_timestamp']) - intval($row['server_timestamp']);

        if ($time_diff > 300) {
            $time_diff_status = 'danger';
            $time_diff_text = '+' . $time_diff . '秒';
        } elseif ($time_diff < -300) {
            $time_diff_status = 'warning';
            $time_diff_text = $time_diff . '秒';
        } else {
            $time_diff_status = 'success';
            $time_diff_text = '正常';
        }
    }

    $row['time_diff'] = $time_diff;
    $row['time_diff_status'] = $time_diff_status;
    $row['time_diff_text'] = $time_diff_text;

    // 格式化时间戳
    if ($row['server_timestamp']) {
        $row['formatted_time'] = date('Y-m-d H:i:s', $row['server_timestamp']);
    } else {
        $row['formatted_time'] = '-';
    }

    // 确保IP地址不为空
    if (empty($row['ip_address'])) {
        $row['ip_address'] = '-';
    }

    // 确保post_count不为空
    if (empty($row['post_count'])) {
        $row['post_count'] = 0;
    }

    $records[] = $row;
}

// 获取总记录数
$sql = "SELECT COUNT(*) as count FROM mobile_post_records WHERE server_timestamp > ?";
$result = $db->query($sql, [strtotime(date('Y-m-d'))]);
$row = $db->fetch_array($result);
$total_records = $row ? $row['count'] : 0;
$total_pages = ceil($total_records / $limit);

// 修改模板目录为admin/template目录
$tpl->setTemplateDir(dirname(__FILE__) . '/template/');

// 引入后台公共函数
require_once(dirname(__FILE__) . '/include/admin.fun.php');

// 设置公共模板变量
set_admin_template_vars($tpl, 'mobile_security', $page_title);

// 分配页面特定变量
$tpl->assign('stats', $stats);
$tpl->assign('records', $records);
$tpl->assign('current_page', $page);
$tpl->assign('total_pages', $total_pages);
$tpl->assign('mobile_daily_limit', $config['mobile_daily_post_limit']);

// 显示模板
$tpl->display('mobile_security.htm');
?>
