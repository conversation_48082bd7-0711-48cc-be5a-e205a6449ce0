{include file="header.htm"}

<!-- 引入UEditor -->
<script type="text/javascript">
    window.UEDITOR_HOME_URL = "/admin/static/ueditor/";
</script>
<script type="text/javascript" src="/admin/static/ueditor/ueditor.config.js"></script>
<script type="text/javascript" src="/admin/static/ueditor/ueditor.all.min.js"></script>
<!-- 引入UEditor样式表 -->
<link rel="stylesheet" type="text/css" href="/admin/static/ueditor/themes/default/_css/ueditor.css">
<!-- 引入UEditor自定义样式 -->
<link rel="stylesheet" type="text/css" href="/admin/static/css/ueditor-custom.css">

<style>
/* 页面标题样式 */
.page-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid #ddd;
}

.page-title h1 {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

/* 表单样式 - 统一布局 */
.form-group {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
    gap: 20px;
}

.form-group.full-width {
    flex-direction: row; /* 改为行布局，保持与其他表单组一致 */
    align-items: flex-start;
    gap: 20px;
}

.form-group.full-width .form-label {
    text-align: right; /* 保持与其他标签一致的对齐方式 */
    margin-bottom: 8px;
    flex: 0 0 140px; /* 保持与其他标签相同的宽度 */
}

.form-label {
    flex: 0 0 140px; /* 标签宽度 */
    margin: 8px 0 0 0; /* 调整上边距以对齐输入框 */
    font-weight: 600;
    color: #333;
    font-size: 14px;
    text-align: right;
    line-height: 1.5;
}

.form-field {
    flex: 1;
    min-width: 0;
    max-width: 600px; /* 限制输入框最大宽度，为描述留出空间 */
}

.form-description {
    flex: 0 0 200px; /* 描述文本固定宽度 */
    margin: 8px 0 0 0; /* 与输入框顶部对齐 */
    font-size: 12px;
    color: #666;
    line-height: 1.4;
    padding-left: 15px;
}

.form-hint {
    margin-top: 4px;
    font-size: 12px;
    color: #666;
    line-height: 1.4;
}

/* 全宽表单组的描述文本保持在右侧 */
.form-group.full-width .form-description {
    flex: 0 0 200px; /* 保持固定宽度 */
    margin: 8px 0 0 0; /* 与输入框对齐 */
    padding-left: 15px;
}

.form-group.full-width .form-hint {
    margin: 4px 0 0 0;
    padding-left: 0;
}

/* 复选框和单选框样式 */
.checkbox-inline, .radio-inline {
    display: inline-block;
    margin-right: 20px;
    margin-bottom: 0;
}

.checkbox-inline input[type="checkbox"],
.radio-inline input[type="radio"] {
    margin-right: 6px;
}

/* 按钮样式 */
.btn {
    display: inline-block;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.5;
    text-align: center;
    text-decoration: none;
    vertical-align: middle;
    cursor: pointer;
    border: 1px solid transparent;
    border-radius: 4px;
    transition: all 0.15s ease-in-out;
}

.btn-primary {
    color: #fff;
    background-color: #1b68ff;
    border-color: #1b68ff;
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
}

.btn-outline {
    color: #666;
    background-color: #fff;
    border-color: #ddd;
}

.btn-outline:hover {
    background-color: #f8f9fa;
    border-color: #ccc;
    color: #666;
    text-decoration: none;
}

/* 卡片样式 - 紧凑版 */
.card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.card-header {
    padding: 16px 20px;
    border-bottom: 1px solid #eee;
    background-color: #f8f9fa;
}

.card-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.card-body {
    padding: 20px;
}

/* 表单按钮区域 */
.form-actions {
    display: flex;
    gap: 12px;
    padding-top: 16px;
    border-top: 1px solid #eee;
    margin-top: 20px;
    margin-left: 160px; /* 与标签和输入框对齐 */
}

/* 特别针对编辑器的宽度优化 */
.form-group.full-width .form-field {
    max-width: none !important;
    width: 100% !important;
}

.form-group.full-width {
    width: 100%;
}

/* 确保卡片容器不限制宽度 */
.card-body {
    width: 100%;
}

/* 编辑器容器特殊处理 */
.editor-container {
    width: 100% !important;
    max-width: none !important;
}

.edui-default.edui-editor-popup,
.edui-default.edui-for-fullscreen {
    z-index: 9999 !important;
}

.edui-editor.edui-default.edui-for-fullscreen {
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: 100% !important;
    height: 100% !important;
    position: fixed !important;
}

/* 移除链接下划线 */
a, a:hover, a:focus, a:active {
    text-decoration: none !important;
}

.required:after {
    content: " *";
    color: #f82f58;
}
</style>

<!-- 页面标题 -->
<div class="page-title">
    <h1>{if isset($page_info)}编辑单页{else}添加单页{/if}</h1>
    <div class="d-flex gap-2">
        <a href="pages.php" class="btn btn-outline">
            <i class="fas fa-arrow-left"></i> 返回列表
        </a>
    </div>
</div>

<!-- 消息提示 -->
{if $message}
<div class="alert alert-success">
    <i class="fas fa-check-circle"></i> {$message}
</div>
{/if}

{if $error}
<div class="alert alert-danger">
    <i class="fas fa-exclamation-circle"></i> {$error}
</div>
{/if}

<div class="container-fluid">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div>
                <h3 class="card-title">{if isset($page_info)}编辑单页{else}添加单页{/if}</h3>
            </div>
        </div>

        <div class="card-body card-body-narrow">
            <form method="POST" action="pages.php?action={if isset($page_info)}edit{else}add{/if}" id="pageForm">
                {if isset($page_info)}
                <input type="hidden" name="id" value="{$page_info.id}">
                {/if}

                <div class="form-group">
                    <label class="form-label required">页面标题</label>
                    <div class="form-field">
                        <input type="text" name="title" class="form-control" placeholder="请输入页面标题" value="{if isset($page_info)}{$page_info.title}{/if}" required>
                    </div>
                    <div class="form-description">页面的标题，将显示在浏览器标题栏和页面中</div>
                </div>

                <div class="form-group">
                    <label class="form-label required">页面路径</label>
                    <div class="form-field">
                        <input type="text" name="path" class="form-control" placeholder="如: about/jianjie" value="{if isset($page_info)}{$page_info.path}{/if}" required id="page_path">
                    </div>
                    <div class="form-description">伪静态URL路径，不需要.html后缀</div>
                </div>

                <div class="form-group">
                    <label class="form-label">访问URL预览</label>
                    <div class="form-field">
                        <div class="form-control-static">
                            <code id="url_preview">/page/{if isset($page_info)}{$page_info.path}{else}your-path{/if}.html</code>
                        </div>
                    </div>
                    <div class="form-description">实际访问地址（自动生成）</div>
                </div>

                <div class="form-group">
                    <label class="form-label">SEO关键词</label>
                    <div class="form-field">
                        <input type="text" name="meta_keywords" class="form-control" placeholder="多个关键词用英文逗号分隔" value="{if isset($page_info)}{$page_info.meta_keywords}{/if}">
                    </div>
                    <div class="form-description">用于SEO优化的关键词，多个关键词用英文逗号分隔</div>
                </div>

                <div class="form-group">
                    <label class="form-label">SEO描述</label>
                    <div class="form-field">
                        <textarea name="meta_description" class="form-control" rows="3" placeholder="页面描述，用于搜索引擎显示">{if isset($page_info)}{$page_info.meta_description}{/if}</textarea>
                    </div>
                    <div class="form-description">页面描述，用于搜索引擎结果显示，建议150字以内</div>
                </div>

                <div class="form-group">
                    <label class="form-label">状态</label>
                    <div class="form-field">
                        <div class="radio-inline">
                            <input type="radio" name="status" value="1" {if !isset($page_info) || $page_info.status == 1}checked{/if}> 启用
                        </div>
                        <div class="radio-inline">
                            <input type="radio" name="status" value="0" {if isset($page_info) && $page_info.status == 0}checked{/if}> 禁用
                        </div>
                    </div>
                    <div class="form-description">禁用的页面不会在前台显示</div>
                </div>

                <div class="form-group">
                    <label class="form-label">排序</label>
                    <div class="form-field">
                        <input type="number" name="sort_order" class="form-control" value="{if isset($page_info)}{$page_info.sort_order}{else}0{/if}" min="0" style="width: 120px;">
                    </div>
                    <div class="form-description">数字越小排序越靠前</div>
                </div>

                <div class="form-group full-width">
                    <label class="form-label">页面内容</label>
                    <div class="form-field">
                        <!-- 使用UEditor替换原始的textarea -->
                        <div class="editor-container">
                            <script id="editor" name="content" type="text/plain">{if isset($page_info)}{php}echo htmlspecialchars_decode($page_info['content'], ENT_QUOTES);{/php}{/if}</script>
                        </div>
                    </div>
                   
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> 保存页面
                    </button>

                    {if isset($page_info)}
                    <a href="{$page_info.url}" target="_blank" class="btn btn-outline">
                        <i class="fas fa-eye"></i> 预览页面
                    </a>
                    {/if}

                    <a href="pages.php" class="btn btn-outline">
                        <i class="fas fa-arrow-left"></i> 返回列表
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>



<!-- UEditor初始化脚本 -->
<script type="text/javascript">
    // 页面加载完成后初始化UEditor
    document.addEventListener('DOMContentLoaded', function() {
        var ue = UE.getEditor('editor', {
            initialFrameWidth: '100%',  // 设置编辑器宽度为100%
            initialFrameHeight: 500,    // 增加编辑器高度到500px
            toolbars: [
                ['fullscreen', 'source', '|', 'undo', 'redo', '|',
                'bold', 'italic', 'underline', 'fontborder', 'strikethrough', 'superscript', 'subscript', 'removeformat', 'formatmatch', 'autotypeset', 'blockquote', 'pasteplain', '|', 'forecolor', 'backcolor', 'insertorderedlist', 'insertunorderedlist', 'selectall', 'cleardoc', '|',
                'rowspacingtop', 'rowspacingbottom', 'lineheight', '|',
                'customstyle', 'paragraph', 'fontfamily', 'fontsize', '|',
                'directionalityltr', 'directionalityrtl', 'indent', '|',
                'justifyleft', 'justifycenter', 'justifyright', 'justifyjustify', '|',
                'link', 'unlink', '|', 'imagenone', 'imageleft', 'imageright', 'imagecenter', '|',
                'simpleupload', 'insertimage', 'emotion', 'insertvideo', '|',
                'horizontal', 'date', 'time', 'spechars', '|',
                'inserttable', 'deletetable', 'insertparagraphbeforetable', 'insertrow', 'deleterow', 'insertcol', 'deletecol', 'mergecells', 'mergeright', 'mergedown', 'splittocells', 'splittorows', 'splittocols', '|',
                'preview']
            ],
            enableAutoSave: true,      // 启用自动保存
            saveInterval: 60000,        // 自动保存间隔，单位为毫秒
            zIndex: 9999,              // 编辑器层级
            autoHeightEnabled: false,  // 是否自动高度
            // 图片相关配置
            imageScaleEnabled: true,   // 启用图片拉伸缩放
            imagePopup: true,          // 启用图片操作浮层
            // 自定义CSS样式注入到编辑器内容区域
            iframeCssUrl: '/admin/static/css/ueditor-content.css',
            // 图片插入时的默认样式
            imageInsertAlign: 'center',  // 图片插入对齐方式：none, left, right, center
            // 粘贴图片时自动上传
            enablePasteUpload: true,
            // 拖拽上传
            enableDragUpload: true
        });

        // 编辑器加载完成后的回调
        ue.ready(function() {
            console.log('UEditor 编辑器加载完成');

            // 注入图片响应式样式到编辑器内容区域
            var editorDoc = ue.getDocument();
            if (editorDoc) {
                var style = editorDoc.createElement('style');
                style.type = 'text/css';
                style.innerHTML = `
                    img {
                        max-width: 100% !important;
                        height: auto !important;
                        display: block;
                        margin: 10px auto;
                        border-radius: 4px;
                        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                        cursor: pointer;
                        transition: all 0.3s ease;
                    }
                    img:hover {
                        box-shadow: 0 4px 16px rgba(0,0,0,0.15);
                        transform: scale(1.02);
                    }
                    img[align="left"] {
                        float: left !important;
                        margin: 10px 15px 10px 0 !important;
                        display: inline !important;
                    }
                    img[align="right"] {
                        float: right !important;
                        margin: 10px 0 10px 15px !important;
                        display: inline !important;
                    }
                    img[align="center"] {
                        display: block !important;
                        margin: 15px auto !important;
                        float: none !important;
                    }
                    p:after {
                        content: "";
                        display: table;
                        clear: both;
                    }
                `;
                editorDoc.head.appendChild(style);
            }

            // 监听图片插入事件，确保新插入的图片应用响应式样式
            ue.addListener('afterInsertImage', function(type, imgObjs) {
                if (imgObjs && imgObjs.length > 0) {
                    for (var i = 0; i < imgObjs.length; i++) {
                        var img = imgObjs[i];
                        if (img && img.style) {
                            // 移除固定宽高，应用响应式样式
                            img.style.maxWidth = '100%';
                            img.style.height = 'auto';
                            img.style.display = 'block';
                            img.style.margin = '10px auto';
                            img.style.borderRadius = '4px';
                            img.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
                            img.style.cursor = 'pointer';
                            img.style.transition = 'all 0.3s ease';

                            // 移除可能存在的固定尺寸属性
                            img.removeAttribute('width');
                            img.removeAttribute('height');
                        }
                    }
                }
            });

            // 强制设置编辑器宽度
            var editorContainer = ue.container;
            if (editorContainer) {
                editorContainer.style.width = '100%';
                editorContainer.style.maxWidth = 'none';

                // 设置编辑器各个组件的宽度
                var toolbarBox = editorContainer.querySelector('.edui-editor-toolbarbox');
                var iframeHolder = editorContainer.querySelector('.edui-editor-iframeholder');
                var bottomContainer = editorContainer.querySelector('.edui-editor-bottomContainer');

                if (toolbarBox) {
                    toolbarBox.style.width = '100%';
                    toolbarBox.style.maxWidth = 'none';
                }
                if (iframeHolder) {
                    iframeHolder.style.width = '100%';
                    iframeHolder.style.maxWidth = 'none';
                }
                if (bottomContainer) {
                    bottomContainer.style.width = '100%';
                    bottomContainer.style.maxWidth = 'none';
                }

                console.log('编辑器宽度已强制设置为100%');
                console.log('编辑器容器宽度:', editorContainer.offsetWidth + 'px');
            }
        });
    
    // 表单验证
    document.getElementById('pageForm').addEventListener('submit', function(e) {
        const title = document.querySelector('input[name="title"]').value.trim();
        const path = document.querySelector('input[name="path"]').value.trim();

        if (!title) {
            alert('请输入页面标题');
            e.preventDefault();
            return false;
        }

        if (!path) {
            alert('请输入页面路径');
            e.preventDefault();
            return false;
        }

        // 验证路径格式（伪静态格式，不需要.html后缀）
        if (!/^[a-zA-Z0-9\/\-_]+$/.test(path)) {
            alert('页面路径格式不正确，只能包含字母、数字、斜杠、横线和下划线');
            e.preventDefault();
            return false;
        }

        return true;
    });
    
    // 实时更新URL预览
    function updateUrlPreview() {
        const pathInput = document.getElementById('page_path');
        const urlPreview = document.getElementById('url_preview');

        if (pathInput && urlPreview) {
            const path = pathInput.value.trim() || 'your-path';
            urlPreview.textContent = '/page/' + path + '.html';
        }
    }

    // 监听路径输入
    const pathInput = document.getElementById('page_path');
    if (pathInput) {
        pathInput.addEventListener('input', updateUrlPreview);
        // 初始化预览
        updateUrlPreview();
    }
});
</script>



{include file="footer.htm"}
