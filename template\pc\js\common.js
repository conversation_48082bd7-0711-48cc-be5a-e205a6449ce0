$(function() {
    // 搜索下拉功能优化
    const searchBox = {
        init() {
            this.bindEvents();
        },
        bindEvents() {
            const $selectBox = $('.select_box');
            const $option = $('.option');
            const $selectTxt = $('.select_txt');
            const $tbname = $('input[name="tbname"]');

            // 点击选项时更新选中值
            $option.on('click', 'li', function() {
                const text = $(this).text();
                $selectTxt.text(text);
                $tbname.val(text === '信息' ? 'info' : 'bbs');
            });

            // 防止点击下拉框时触发文档点击事件
            $selectBox.on('click', function(e) {
                e.stopPropagation();
            });

            // 点击文档其他地方关闭下拉
            $(document).on('click', function() {
                $option.hide();
            });
        }
    };

    // 轮播图功能优化
    const slider = {
        config: {
            duration: 5000,
            animationSpeed: 300
        },
        init() {
            this.$wrap = $('#slider-wrap');
            if (!this.$wrap.length) return;
            
            this.$slider = $('#slider');
            this.$slides = this.$slider.find('li');
            this.slideCount = this.$slides.length;
            this.currentSlide = 0;
            this.slideWidth = this.$wrap.width();
            
            this.setupSlider();
            this.setupPagination();
            this.bindEvents();
            this.startAutoSlide();
        },
        setupSlider() {
            this.$slider.width(this.slideWidth * this.slideCount);
            this.$slides.width(this.slideWidth);
        },
        setupPagination() {
            const $pagination = $('#pagination-wrap ul').empty();
            for (let i = 0; i < this.slideCount; i++) {
                $pagination.append(`<li class="${i === 0 ? 'active' : ''}"></li>`);
            }
        },
        bindEvents() {
            $('#next').on('click', () => this.navigate('next'));
            $('#previous').on('click', () => this.navigate('prev'));
            $('#pagination-wrap').on('click', 'li', (e) => this.goToSlide($(e.target).index()));
            
            this.$wrap.hover(
                () => this.pauseAutoSlide(),
                () => this.startAutoSlide()
            );
            
            $(window).on('resize', () => {
                clearTimeout(this.resizeTimer);
                this.resizeTimer = setTimeout(() => this.handleResize(), 200);
            });
        },
        navigate(direction) {
            this.pauseAutoSlide();
            
            if (direction === 'next') {
                this.currentSlide = (this.currentSlide + 1) % this.slideCount;
            } else {
                this.currentSlide = (this.currentSlide - 1 + this.slideCount) % this.slideCount;
            }
            
            this.updateSlider();
            this.startAutoSlide();
        },
        goToSlide(index) {
            if (index === this.currentSlide) return;
            
            this.pauseAutoSlide();
            this.currentSlide = index;
            this.updateSlider();
            this.startAutoSlide();
        },
        updateSlider() {
            this.$slider.animate({
                left: -this.currentSlide * this.slideWidth
            }, this.config.animationSpeed);
            
            $('#pagination-wrap li')
                .removeClass('active')
                .eq(this.currentSlide)
                .addClass('active');
        },
        startAutoSlide() {
            this.pauseAutoSlide();
            this.slideInterval = setInterval(() => this.navigate('next'), this.config.duration);
        },
        pauseAutoSlide() {
            clearInterval(this.slideInterval);
        },
        handleResize() {
            this.slideWidth = this.$wrap.width();
            this.setupSlider();
            this.updateSlider();
        }
    };

    // 初始化功能
    searchBox.init();
    slider.init();
}); 