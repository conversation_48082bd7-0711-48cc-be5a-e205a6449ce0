{include file="header.htm"}

<style>
/* 页面标题样式 */
.page-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #ddd;
}

.page-title h1 {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.d-flex {
    display: flex !important;
}

.gap-2 {
    gap: 0.5rem !important;
}

/* 表单样式 - 紧凑布局 */
.form-group {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16px;
    gap: 20px;
}

.form-label, .control-label {
    flex: 0 0 120px;
    margin: 8px 0 0 0;
    font-weight: 600;
    color: #333;
    font-size: 14px;
    text-align: right;
}

.form-field {
    flex: 1;
    min-width: 0;
}

.form-control, .form-select {
    display: block;
    width: 100%;
    padding: 6px 12px;
    font-size: 14px;
    line-height: 1.5;
    color: #333;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus, .form-select:focus {
    border-color: #1b68ff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(27, 104, 255, 0.25);
}

textarea.form-control {
    min-height: 60px;
    resize: vertical;
}

/* 表单描述在右侧 */
.form-description {
    flex: 0 0 200px;
    margin: 8px 0 0 0;
    font-size: 12px;
    color: #666;
    line-height: 1.4;
}

/* 输入组样式 */
.input-group {
    display: flex;
    align-items: stretch;
    max-width: 400px;
}

.input-group .form-control {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-right: 0;
}

.input-group-append {
    display: flex;
}

.input-group-append .btn {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-left: 0;
    padding: 6px 12px;
    font-size: 13px;
}

/* 图标预览样式 */
.icon-preview {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background-color: #f8f9fa;
    border: 1px solid #ddd;
    border-left: 0;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    font-size: 14px;
    color: #666;
}

/* 单选按钮组样式 */
.radio-group {
    display: flex;
    gap: 20px;
    align-items: center;
}

.radio-group label {
    display: flex;
    align-items: center;
    gap: 6px;
    font-weight: normal;
    margin: 0;
    cursor: pointer;
    font-size: 14px;
}

.radio-group input[type="radio"] {
    margin: 0;
}

/* 帮助文本样式 - 已移到右侧 */
.help-block, .form-hint {
    display: none; /* 隐藏原有的帮助文本，使用右侧描述 */
}

/* 按钮样式 */
.btn {
    display: inline-block;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.5;
    text-align: center;
    text-decoration: none;
    vertical-align: middle;
    cursor: pointer;
    border: 1px solid transparent;
    border-radius: 4px;
    transition: all 0.15s ease-in-out;
}

.btn-primary {
    color: #fff;
    background-color: #1b68ff;
    border-color: #1b68ff;
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
}

.btn-outline {
    color: #666;
    background-color: #fff;
    border-color: #ddd;
}

.btn-outline:hover {
    background-color: #f8f9fa;
    border-color: #ccc;
}

.btn-light-success {
    background-color: #e6ffe6;
    color: #00aa00;
    border: 1px solid #b3ffb3;
}

.btn-light-success:hover {
    background-color: #d1ffd1;
    color: #008800;
}

.btn-light-danger {
    background-color: #ffe6e6;
    color: #ff3333;
    border: 1px solid #ffb3b3;
}

.btn-light-danger:hover {
    background-color: #ffd1d1;
    color: #cc0000;
}

/* 卡片样式 - 紧凑版 */
.section {
    margin-bottom: 20px;
}

.card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.card-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
}

/* 表单按钮区域 */
.form-actions {
    display: flex;
    gap: 12px;
    padding-top: 16px;
    border-top: 1px solid #eee;
    margin-top: 20px;
    margin-left: 140px; /* 与标签对齐 */
}

/* 特殊字段样式 */
.form-group.compact {
    margin-bottom: 12px;
}

.form-group.inline .form-field {
    display: flex;
    align-items: center;
    gap: 15px;
}

.form-group.inline .form-control {
    width: auto;
    flex: 0 0 auto;
}

/* 移除链接下划线 */
a, a:hover, a:focus, a:active {
    text-decoration: none !important;
}

/* SEO折叠样式 */
.seo-section .collapsible {
    user-select: none;
    transition: color 0.2s;
}

.seo-section .collapsible:hover {
    color: #1b68ff;
}

.seo-section .collapsible-content {
    overflow: hidden;
    transition: max-height 0.3s ease-out;
}

.seo-section .collapsible-content.collapsed {
    max-height: 0;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .page-title {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .form-group {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .form-label, .control-label {
        flex: none;
        text-align: left;
        margin: 0 0 4px 0;
    }

    .form-description {
        flex: none;
        margin: 4px 0 0 0;
    }

    .form-actions {
        margin-left: 0;
        flex-direction: column;
    }

    .input-group {
        flex-direction: column;
        max-width: none;
    }

    .input-group .form-control {
        border-radius: 4px;
        border-right: 1px solid #ddd;
        margin-bottom: 8px;
    }

    .input-group-append .btn {
        border-radius: 4px;
        border-left: 1px solid #ddd;
    }

    .form-group.inline .form-field {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }
}
</style>

<!-- 页面标题 -->
<div class="page-title">
    <h1>{if isset($category)}编辑分类{else}添加分类{/if}</h1>
    <div class="d-flex gap-2">
        <a href="category.php" class="btn btn-outline">
            <i class="fas fa-arrow-left"></i>
            返回列表
        </a>
    </div>
</div>

{if !empty($error)}
<div class="alert alert-danger">
    <i class="fas fa-exclamation-circle"></i>
    <span>{$error}</span>
</div>
{/if}

<div class="section">
    <div class="card">
        <h3 class="card-title">基本信息</h3>
        <form method="post" action="" id="categoryForm" class="form-horizontal" {if isset($category)}data-category-id="{$category.id}"{/if}>
            {if isset($category) && $category.parent_id > 0}
            <input type="hidden" name="return_parent_id" value="{$category.parent_id}">
            {elseif isset($selected_parent_id) && $selected_parent_id > 0}
            <input type="hidden" name="return_parent_id" value="{$selected_parent_id}">
            {/if}

            <div class="form-group">
                <label class="control-label" for="name">分类名称</label>
                <div class="form-field">
                    <input type="text" id="name" name="name" value="{if isset($category)}{$category.name}{/if}" class="form-control" required style="max-width: 300px;">
                </div>
                <div class="form-description">必填，分类的显示名称</div>
            </div>

            <div class="form-group">
                <label class="control-label" for="parent_id">父分类</label>
                <div class="form-field">
                    <select id="parent_id" name="parent_id" class="form-select" style="max-width: 300px;" {if isset($is_top_level) && $is_top_level}disabled{/if}>
                        <option value="0" {if (!isset($category) && !isset($selected_parent_id)) || (isset($category) && $category.parent_id == 0) || (isset($selected_parent_id) && $selected_parent_id == 0)}selected{/if}>-- 无父分类（作为一级分类）--</option>
                        {foreach $parent_categories as $parent}
                        <option value="{$parent.id}" {if (isset($category) && $category.parent_id == $parent.id) || (isset($selected_parent_id) && $selected_parent_id == $parent.id)}selected{/if}>
                            {$parent.name}
                        </option>
                        {/foreach}
                    </select>
                    {if isset($is_top_level) && $is_top_level}
                    <input type="hidden" name="parent_id" value="0">
                    {/if}
                </div>
                <div class="form-description">
                    {if isset($is_top_level) && $is_top_level}
                    一级分类不能修改父分类
                    {else}
                    选择父分类，如无则作为一级分类。只能选择一级分类作为父分类
                    {/if}
                </div>
            </div>
            
            <div class="form-group">
                <label class="form-label" for="pinyin">拼音标识</label>
                <div class="form-field">
                    <div class="input-group">
                        <input type="text" id="pinyin" name="pinyin" value="{if isset($category)}{$category.pinyin}{/if}" class="form-control">
                        <div class="input-group-append">
                            <button type="button" id="generatePinyin" class="btn btn-outline">生成拼音</button>
                        </div>
                    </div>
                </div>
                <div class="form-description">用于URL和标识，留空将自动生成。只能包含小写字母、数字和短横线</div>
            </div>

            <div class="form-group compact">
                <label class="form-label" for="sort_order">排序</label>
                <div class="form-field">
                    <input type="number" id="sort_order" name="sort_order" value="{if isset($category)}{$category.sort_order}{else}0{/if}" class="form-control" style="width: 120px;">
                </div>
                <div class="form-description">数字越小排序越靠前</div>
            </div>
            
            <div class="form-group compact">
                <label class="form-label" for="icon">图标</label>
                <div class="form-field">
                    <div class="input-group">
                        <input type="text" id="icon" name="icon" value="{if isset($category)}{$category.icon}{/if}" class="form-control" placeholder="例如: fas fa-home">
                        <div id="iconPreview" class="icon-preview">
                            {if isset($category) && !empty($category.icon)}<i class="{$category.icon}"></i>{/if}
                        </div>
                    </div>
                </div>
                <div class="form-description">可选，FontAwesome图标类名，如"fas fa-home"</div>
            </div>

            <div class="form-group compact">
                <label class="form-label">状态</label>
                <div class="form-field">
                    <div class="radio-group">
                        <label>
                            <input type="radio" name="status" value="1" {if !isset($category) || $category.status == 1}checked{/if}> 启用
                        </label>
                        <label>
                            <input type="radio" name="status" value="0" {if isset($category) && $category.status == 0}checked{/if}> 禁用
                        </label>
                    </div>
                </div>
                <div class="form-description">设置分类是否可用</div>
            </div>
            
            <div class="form-group">
                <label class="form-label" for="description">分类描述</label>
                <div class="form-field">
                    <textarea id="description" name="description" class="form-control" style="max-width: 400px;">{if isset($category)}{$category.description}{/if}</textarea>
                </div>
                <div class="form-description">可选，简短描述此分类，用于前台展示</div>
            </div>
            
            <div class="form-group inline">
                <label class="form-label" for="template">栏目模板</label>
                <div class="form-field">
                    <select id="template" name="template" class="form-control" style="width: 250px;">
                        <option value="">默认模板 (category.htm)</option>
                        {foreach $template_files as $template_file}
                        <option value="{$template_file.name}" {if isset($category) && $category.template == $template_file.name}selected{/if}>
                            {$template_file.name} {if !$template_file.exists_in_mobile}<span style="color:red;">[仅PC端]</span>{/if}
                        </option>
                        {/foreach}
                    </select>
                    <label style="white-space: nowrap; font-weight: normal;">
                        <input type="checkbox" name="sync_template" id="sync_template" value="1"> 同步子栏目
                    </label>
                </div>
                <div class="form-description">标记为[仅PC端]的模板在移动端访问时将自动使用默认模板</div>
            </div>

            <div class="form-group inline">
                <label class="form-label" for="detail_template">详情模板</label>
                <div class="form-field">
                    <select id="detail_template" name="detail_template" class="form-control" style="width: 250px;">
                        <option value="">默认模板 (view.htm)</option>
                        {foreach $detail_template_files as $template_file}
                        <option value="{$template_file.name}" {if isset($category) && $category.detail_template == $template_file.name}selected{/if}>
                            {$template_file.name} {if !$template_file.exists_in_mobile}<span style="color:red;">[仅PC端]</span>{/if}
                        </option>
                        {/foreach}
                    </select>
                    <label style="white-space: nowrap; font-weight: normal;">
                        <input type="checkbox" name="sync_detail_template" id="sync_detail_template" value="1"> 同步子栏目
                    </label>
                </div>
                <div class="form-description">标记为[仅PC端]的模板在移动端访问时将自动使用默认模板</div>
            </div>
            
            <div class="seo-section" style="margin-top: 20px; padding-top: 16px; border-top: 1px solid #eee;">
                <h3 class="collapsible" onclick="toggleSeoSection()" style="font-size: 14px; font-weight: 600; color: #666; cursor: pointer; margin-bottom: 12px;">
                    SEO设置 <span id="seo-toggle">▼</span>
                </h3>
                <div class="collapsible-content" id="seo-section">
                    <div class="form-group compact">
                        <label class="form-label" for="seo_title">SEO标题</label>
                        <div class="form-field">
                            <input type="text" id="seo_title" name="seo_title" value="{if isset($category)}{$category.seo_title}{/if}" class="form-control" style="max-width: 400px;">
                        </div>
                        <div class="form-description">可选，用于SEO的页面标题，留空则使用分类名称</div>
                    </div>

                    <div class="form-group compact">
                        <label class="form-label" for="seo_keywords">SEO关键词</label>
                        <div class="form-field">
                            <input type="text" id="seo_keywords" name="seo_keywords" value="{if isset($category)}{$category.seo_keywords}{/if}" class="form-control" style="max-width: 400px;">
                        </div>
                        <div class="form-description">可选，用于SEO的关键词，多个关键词用逗号分隔</div>
                    </div>

                    <div class="form-group compact">
                        <label class="form-label" for="seo_description">SEO描述</label>
                        <div class="form-field">
                            <textarea id="seo_description" name="seo_description" class="form-control" style="max-width: 400px;">{if isset($category)}{$category.seo_description}{/if}</textarea>
                        </div>
                        <div class="form-description">可选，用于SEO的描述信息，留空则使用分类描述</div>
                    </div>
                </div>
            </div>
            
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i>
                    保存分类
                </button>

                <a href="category.php" class="btn btn-outline">
                    <i class="fas fa-times"></i>
                    取消
                </a>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 生成拼音按钮事件
        document.getElementById('generatePinyin').addEventListener('click', function() {
            var name = document.getElementById('name').value;
            if (name) {
                fetch('category.php?action=generate_pinyin&name=' + encodeURIComponent(name))
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('网络响应异常');
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data && data.pinyin) {
                            document.getElementById('pinyin').value = data.pinyin;
                        } else {
                            alert('拼音生成失败，请手动输入');
                        }
                    })
                    .catch(error => {
                        console.error('生成拼音出错:', error);
                        alert('拼音生成失败，请手动输入');
                    });
            } else {
                alert('请先输入分类名称');
            }
        });
        
        // 检查拼音有效性
        document.getElementById('pinyin').addEventListener('blur', function() {
            var pinyin = this.value;
            if (pinyin) {
                var form = document.getElementById('categoryForm');
                var categoryId = form.dataset.categoryId || 0;
                fetch('category.php?action=check_pinyin&pinyin=' + encodeURIComponent(pinyin) + '&id=' + categoryId)
                    .then(response => response.json())
                    .then(data => {
                        if (!data.valid) {
                            alert(data.message || '拼音已存在，请更换');
                        }
                    });
            }
        });
        
        // 图标预览
        document.getElementById('icon').addEventListener('input', function() {
            var iconPreview = document.getElementById('iconPreview');
            iconPreview.innerHTML = this.value ? '<i class="' + this.value + '"></i>' : '';
        });
    });
    
    // SEO部分的折叠显示
    function toggleSeoSection() {
        var content = document.getElementById('seo-section');
        var toggle = document.getElementById('seo-toggle');

        if (content.classList.contains('collapsed')) {
            content.classList.remove('collapsed');
            content.style.maxHeight = content.scrollHeight + 'px';
            toggle.textContent = '▲';
        } else {
            content.classList.add('collapsed');
            content.style.maxHeight = '0';
            toggle.textContent = '▼';
        }
    }

    // 初始化SEO部分为折叠状态
    document.addEventListener('DOMContentLoaded', function() {
        var content = document.getElementById('seo-section');
        content.style.maxHeight = content.scrollHeight + 'px';
        // 默认展开，如果需要默认折叠，取消下面两行注释
        // content.classList.add('collapsed');
        // content.style.maxHeight = '0';
    });
</script>

{include file="footer.htm"} 