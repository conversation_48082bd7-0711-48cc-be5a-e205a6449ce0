/* 第一屏 */
.yui-index-l{ width: 878px; float: left; }
.yui-index-c{ width: 580px; overflow:hidden; padding-top: 10px; float: left;}
.yui-index-r{ width: 310px; float: right;}

/* 左侧第一屏布局 */
.left-top-section { display: flex; margin-bottom: 10px; }
.left-top-section .gonggao-wrap { width: 288px; }
.left-top-section .news-wrap { width: 580px; padding-top: 10px;margin-left: 10px;  }

/* 中间 区域 */
.yui-index-topnews{ height: 80px;}
.yui-top-img{ width: 81px; height: 81px;}
.yui-top-text{width: 485px; height: 81px; }
.yui-top-text a{ font-size: 20px; font-weight: bold; white-space: nowrap; word-break: break-all; overflow: hidden; text-overflow: ellipsis;}
.yui-top-text .intro{color: #f2f2f2;}
.yui-top-text p{color:#888;LINE-HEIGHT: 20PX;}


.yui-index-news ul{margin:10px;}
.yui-index-news ul li{line-height: 36px;}
.yui-index-news .yui-lm-name{ padding-right: 5px; border-right: 1px solid #f2f2f2; margin-right: 5px;color: red;}
.yui-index-news ul li a{font-size: 14px;}
.yui-index-news  .yui-post-time{ float: right; color: #888;}
.yui-one2-news{ 
    line-height: 60px; 
    font-size: 20px; 
    text-align: center; 
    font-weight: bold; 
    white-space: nowrap; 
    word-break: break-all; 
    overflow: hidden; 
    text-overflow: ellipsis;
    padding-left: 20px; 
    padding-right: 20px;
}

.gonggao {
    padding: 0px 10px 10px 10px;
}

.gonggao .yui-small-list ul {
    height: 128px;
    overflow: hidden;
}

.gonggao .yui-small-list ul li {
    position: relative;
    padding-left: 12px;
    line-height: 32px;
    border-bottom: 1px dashed #f0f0f0;
    height: 32px;
}

.gonggao .yui-small-list ul li:nth-child(3) {
    border-bottom: none;
}

.gonggao .yui-small-list ul li:before {
    content: "";
    position: absolute;
    left: 0;
    top: 50%;
    margin-top: -2px;
    width: 4px;
    height: 4px;
    background: #EE3131;
    border-radius: 50%;
}

.gonggao .yui-small-list ul li a {
    display: block;
    font-size: 13px;
    color: #666;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 便民、热帖 */
.bbs-hot{
    background-color: white;
    margin-bottom: 10px;
    padding: 0px 10px;
}
.bbs-hot a{color: #266392;}

/* 热门信息样式 */
.bbs-hot .yui-small-list ul {
    height: auto;
    overflow: hidden;
}

.bbs-hot .yui-small-list ul li {
    position: relative;
    padding-left: 12px;
    line-height: 28px;
    border-bottom: 1px dashed #f0f0f0;
    height: 28px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.bbs-hot .yui-small-list ul li:last-child {
    border-bottom: none;
}

.bbs-hot .yui-small-list ul li:before {
    content: "";
    position: absolute;
    left: 0;
    top: 50%;
    margin-top: -2px;
    width: 4px;
    height: 4px;
    background: #EE3131;
    border-radius: 50%;
}

.bbs-hot .yui-small-list ul li a {
    display: block;
    font-size: 13px;
    color: #666;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
    margin-right: 10px;
}

.bbs-hot .yui-small-list ul li .view-count {
    font-size: 11px;
    color: #999;
    white-space: nowrap;
}

.bbs-hot .yui-small-list ul li .news-date {
    font-size: 11px;
    color: #999;
    white-space: nowrap;
}
.bianmin {
    background-color: white;
    overflow: hidden;
    margin-bottom: 10px;
    padding: 0px 10px;
}

.bianmin .yui-h-title {
    margin-bottom: 12px;
}

.bianmin ul {
    margin: 0 -3px;
    overflow: hidden;  /* 清除浮动 */
}

.bianmin ul li {
    width: 86px;
    margin: 0 3px 10px;
    height: 36px;
    line-height: 36px;
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    text-align: center;
    background: #f8f8f8;
    float: left;
}

.bianmin ul li:hover {
    border-color: #EE3131;
    background: #fff;
}

.bianmin ul li a {
    color: #666;
    font-size: 13px;
    display: block;
    padding: 0 5px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.bianmin ul li:hover a {
    color: #EE3131;
    text-decoration: none !important;
}

.bianmin-tel {
    background: #fff;
    padding: 0px 10px 10px 10px;
    margin-bottom: 15px;
}

.bianmin-tel .yui-h-title {
    margin-bottom: 15px;
}

.bianmin-tel .tel-list {
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
    margin: 0 -5px;
}

.bianmin-tel .tel-item {
    width: calc(11.11% - 10px);
    margin: 0 5px;
    background: #f8f8f8;
    border: 1px solid #eee;
    border-radius: 4px;
    padding: 0px 8px;
    text-align: center;
}

.bianmin-tel .tel-item:hover {
    background: #EE3131;
}

.bianmin-tel .tel-item:hover .tel-name,
.bianmin-tel .tel-item:hover .tel-number {
    color: #fff;
}

.bianmin-tel .tel-name {
    color: #666;
    font-size: 13px;
}

.bianmin-tel .tel-number {
    color: #EE3131;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.2;
}

/* 轮播图优化 */
#slider-wrap {
    width: 100%;
    height: 200px;
    overflow: hidden;
    position: relative;
}

#slider-wrap #slider li {
    position: absolute;
    width: 100%;
    height: 200px;
    display: none;
}

#slider-wrap #slider li img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

/* 调整轮播图区域整体高度 */
.flash-img {
    height: 200px;
    position: relative;
}

/* 轮播图控制按钮样式 */
.btns {
    position: absolute;
    width: 30px;
    height: 30px;
    top: 50%;
    margin-top: -15px;
    line-height: 30px;
    text-align: center;
    cursor: pointer;
    background: rgba(0,0,0,0.2);
    color: #fff;
    font-size: 16px;
    border-radius: 50%;
    z-index: 10;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.flash-img:hover .btns {
    opacity: 1;
}

.btns:hover {
    background: rgba(0,0,0,0.5);
}

#next {
    right: 10px;
}

#previous {
    left: 10px;
}

/* 分页器样式优化 */
#pagination-wrap {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: 10px;
    z-index: 10;
}

#pagination-wrap ul {
    margin: 0;
    padding: 0;
    display: flex;
    gap: 6px;
}

#pagination-wrap ul li {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: rgba(255,255,255,0.5);
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-block;  /* 确保小圆点显示 */
}

#pagination-wrap ul li.active {
    background: #fff;
    width: 16px;  /* 活动状态下变长 */
    border-radius: 4px;  /* 活动状态下变成胶囊形状 */
}

/* 分类信息列表 */
.index-new-list { overflow: hidden; margin-top: 20px; }
.index-new-list-left { width: 858px; float: left; }
.index-new-list-right { width: 310px; float: right; }
.index-new-title{ height: 50px; line-height: 50px;border-bottom: 1px #ccc solid;font-size: 14px; color: #EE3131;}
.index-new-title h3{border-bottom: 2px solid #EE3131; display: inline-block;line-height: 49px;}
.index-new-title a{ font-size: 16px; margin-left: 20px;color:#0000FF;}
.index-new-list-left .ding  li{background:url(../images/icon_title2.gif) left no-repeat;}
.index-new-list-left .ding li a{ color: #EE3131}
.index-new-list-left ul li{ width: 413px; float: left; line-height: 30px; margin-bottom: 5px;background:url(../images/icon_title1.gif) left no-repeat; padding-left: 15px; color: #888888; }
.index-new-list-left span{ margin-left: 5px;}
.index-new-list-left ul li .ding{padding: 0; font-size: 12px;background-color: #EE3131; color: #fff; border-radius: 2px; margin-right: 10px; display: inline-block; width: 20px; height: 20px; line-height: 20px; text-align: center; vertical-align: middle;}
.index-new-list-left .text-dot{ 
    float: right;
    margin-right: 10px;
}

.index-new-list-left ul li a{font-size: 16px; color: #266392;  margin-right: 10px;}
.index-new-list-left ul li a:hover{text-decoration:underline}

/* 友情链接样式 */
.friend-links {
    margin: 10px 0;
    padding: 0px 10px;
}
.friend-links ul {
    display: flex;
    flex-wrap: wrap;
    padding: 5px 0;

}
.friend-links ul li {

    margin: 0 6px;
    line-height: 24px;
    text-align: center;
}
.friend-links ul li a {
    color: #266392;
    font-size: 13px;
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.friend-links ul li a:hover {
    color: #EE3131;
    text-decoration: underline;
}
.clearfix:after {content: ""; display: table; clear: both;}

/* 全局链接悬停样式 */
a {
    transition: all 0.3s ease;
    text-decoration: none;
    color: #333;
}

a:hover {
    color: #EE3131 !important;
    text-decoration: underline !important;
}

/* 移除之前的特定悬停样式 */
.bianmin ul li a:hover,
.friend-links ul li a:hover,
.index-new-list-left ul li a:hover {
    color: #EE3131;
    text-decoration: underline;
}

/* 特殊链接不需要下划线 */
.tel-item:hover a,
.yui-fabu a:hover,
.btns:hover {
    text-decoration: none !important;
}

/* 发布按钮文字颜色保持白色 */
.yui-fabu button a:hover {
    color: #fff !important;
}

/* 专题专栏 */
.zhuanti {
    background-color: white;
    overflow: hidden;
    margin-bottom: 10px;
    padding: 0px 10px;
}

.zhuanti-content {
    overflow: hidden;
}

.zhuanti-item {
    margin-bottom: 10px;
}

.zhuanti-red-box {
    border: 2px solid #EE3131;
    padding: 10px;
    background-color: #fff;
}

.zhuanti-red-box h4 {
    font-size: 14px;
    color: #EE3131;
    font-weight: bold;
    border-bottom: 1px dashed #f0f0f0;
    padding-bottom: 8px;
    margin-bottom: 8px;
    text-align: center;
}

.zhuanti-red-box p {
    font-size: 13px;
    line-height: 24px;
    margin-bottom: 5px;
    color: #333;
}

.zhuanti-red-box .label {
    font-weight: bold;
    color: #EE3131;
}

.zhuanti-game {
    border: 1px solid #eee;
    padding: 10px;
    background-color: #f9f9f9;
}

.zhuanti-game a {
    display: block;
    text-decoration: none;
    color: inherit;
}

.game-logo {
    width: 50px;
    height: 50px;
    background: url(../images/icon_title1.gif) no-repeat center;
    background-size: contain;
    float: left;
    margin-right: 10px;
}

.zhuanti-game h4 {
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 5px;
    color: #333;
}

.zhuanti-game p {
    font-size: 13px;
    line-height: 20px;
    color: #666;
}

.zhuanti-game .people-count {
    color: #999;
    font-size: 12px;
    margin-top: 5px;
}


.bianmin-query{ padding: 0px 10px;}
.bianmin-query .query-list ul {
    display: flex;
    flex-wrap: nowrap;
    padding: 0 5px;
    margin: 0;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    gap: 15px;
}

.bianmin-query .query-list ul::-webkit-scrollbar {
    display: none;
}

.bianmin-query .query-list li {
    flex: 0 0 auto;
    text-align: center;
    font-size: 14px;
    position: relative;
    white-space: nowrap;
}

.bianmin-query .query-list li:not(:last-child):after {
    content: "";
    position: absolute;
    right: -8px;
    top: 50%;
    transform: translateY(-50%);
    width: 1px;
    height: 12px;
    background: #ddd;
}

.bianmin-query .query-list li a {
    color: #333;
    text-decoration: none;
    display: block;
    padding: 5px 0;
    transition: color 0.3s ease;
}

.bianmin-query .query-list li a:hover {
    color: #EE3131;
    text-decoration: none;
}
