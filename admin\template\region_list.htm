{include file="header.htm"}

<style>
    .table-responsive {
        overflow-x: auto;
    }
    .region-actions {
        display: flex;
        flex-wrap: nowrap;
        justify-content: flex-end;
        gap: 5px;
        min-width: 120px;
    }
    .region-actions .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        white-space: nowrap;
        text-decoration: none !important;
    }
    .region-actions .btn-group {
        display: inline-block;
    }
    .dropdown-toggle::after {
        display: none !important;
    }
    .dropdown-item {
        text-decoration: none;
        padding: 0.25rem 1rem;
    }
    a, a:hover, a:focus, a:active {
        text-decoration: none !important;
    }
    .nav-link {
        text-decoration: none !important;
    }
    .nav-link.active {
        background-color: #3490dc;
        color: #fff;
    }

    /* 淡色按钮样式 */
    .btn-light-primary {
        background-color: #e6f0ff;
        color: #1b68ff;
        border: 1px solid #cce0ff;
    }
    .btn-light-primary:hover {
        background-color: #d1e3ff;
        color: #0056b3;
    }
    .btn-light-warning {
        background-color: #fff8e6;
        color: #ffa500;
        border: 1px solid #ffe6b3;
    }
    .btn-light-warning:hover {
        background-color: #fff0d1;
        color: #cc8400;
    }
    .btn-light-danger {
        background-color: #ffe6e6;
        color: #ff3333;
        border: 1px solid #ffb3b3;
    }
    .btn-light-danger:hover {
        background-color: #ffd1d1;
        color: #cc0000;
    }
    .btn-light-info {
        background-color: #e6f7ff;
        color: #00aaff;
        border: 1px solid #b3e0ff;
    }
    .btn-light-info:hover {
        background-color: #d1f0ff;
        color: #0088cc;
    }
    .btn-light-success {
        background-color: #e6ffe6;
        color: #00aa00;
        border: 1px solid #b3ffb3;
    }
    .btn-light-success:hover {
        background-color: #d1ffd1;
        color: #008800;
    }
    .btn-light-secondary {
        background-color: #f0f0f0;
        color: #666666;
        border: 1px solid #dddddd;
    }
    .btn-light-secondary:hover {
        background-color: #e0e0e0;
        color: #444444;
    }

    /* 分页样式 */
    .simple-pagination {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;
        gap: 5px;
    }
    .pagination-btn {
        display: inline-block;
        padding: 5px 12px;
        background: #fff;
        border: 1px solid #ddd;
        color: #333;
        text-decoration: none;
        border-radius: 3px;
        transition: all 0.2s;
    }
    .pagination-btn:hover {
        background: #f8f9fa;
        border-color: #ccc;
    }
    .pagination-btn.active {
        background: #1b68ff;
        color: white;
        border-color: #1b68ff;
    }
    .pagination-btn.disabled {
        color: #aaa;
        background: #f8f8f8;
        cursor: not-allowed;
    }

    /* 固定表格列宽 */
    .table {
        width: 100%;
        table-layout: auto;
        white-space: nowrap;
    }
    .table th {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .table td {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .search-form {
        display: flex;
        width: 300px;
    }
    .search-input {
        flex: 1;
        padding: 6px 12px;
        border: 1px solid #ced4da;
        border-radius: 4px 0 0 4px;
    }
    .search-btn {
        border-radius: 0 4px 4px 0;
    }


    .sort-input {
        width: 60px;
        text-align: center;
        padding: 2px 5px;
        border: 1px solid #ddd;
        border-radius: 3px;
    }
    
    .badge {
        display: inline-block;
        padding: 3px 6px;
        font-size: 12px;
        border-radius: 3px;
        color: #fff;
    }
    .badge-info {
        background-color: #17a2b8;
    }
    .badge-secondary {
        background-color: #6c757d;
    }
</style>

<!-- 消息提示 -->
{if !empty($message)}
<div class="alert alert-success">
    <i class="fa fa-check-circle"></i> {$message}
</div>
{/if}

{if !empty($error)}
<div class="alert alert-danger">
    <i class="fa fa-exclamation-circle"></i> {$error}
</div>
{/if}

<!-- 区域管理 -->
<div class="card mb-4">
    <div class="card-body">
        <!-- 头部导航 -->
        <div style="margin-bottom: 20px; padding-bottom: 15px; border-bottom: 1px solid #e9ecef;">
            <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 10px;">
                <div>
                    {if $parent_id > 0}
                        {if $parent_region}
                            <a href="region.php" class="btn btn-light-secondary">返回顶级区域</a>
                            <span style="margin-left: 10px;">当前位置：{$parent_region.level_text} - {$parent_region.name}</span>
                        {else}
                            <a href="region.php" class="btn btn-light-secondary">返回顶级区域</a>
                        {/if}
                    {else}
                        <h4 style="margin: 0; font-weight: 500;">顶级区域列表（省/直辖市）</h4>
                    {/if}
                </div>
                <div>
                    <form class="search-form" action="region.php" method="get">
                        {if $parent_id > 0}
                        <input type="hidden" name="parent_id" value="{$parent_id}">
                        {/if}
                        <input type="text" name="keyword" class="search-input" placeholder="搜索区域名称" value="{$keyword}">
                        <button type="submit" class="btn btn-light-primary search-btn">搜索</button>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- 操作按钮 -->
        <div style="margin-bottom: 20px; display: flex; justify-content: space-between; align-items: center;">
            <div>
                {if $parent_id > 0}
                    <a href="region.php?action=add&parent_id={$parent_id}" class="btn btn-light-primary">添加子区域</a>
                    <a href="region.php?action=batch&parent_id={$parent_id}" class="btn btn-light-success" style="margin-left: 8px;">批量添加</a>
                {else}
                    <a href="region.php?action=add" class="btn btn-light-primary">添加省份/直辖市</a>
                    <a href="region.php?action=batch" class="btn btn-light-success" style="margin-left: 8px;">批量添加</a>
                {/if}
            </div>
            
            <button type="button" id="save-sort-btn" class="btn btn-light-info">保存排序</button>
        </div>
        
        <!-- 区域列表 -->
        <form id="list-form" action="region.php?action=batch_delete" method="post">
            <input type="hidden" name="return_parent_id" value="{$parent_id}">
            <div class="table-responsive">
                <table class="table table-hover" id="region-table">
                    <thead>
                        <tr>
                            <th width="40"><input type="checkbox" id="check-all"></th>
                            <th width="60">排序</th>
                            <th width="60">ID</th>
                            <th>名称</th>
                            <th width="100">拼音</th>
                            <th width="80">级别</th>
                            <th width="80">子区域</th>
                            <th width="180" style="text-align: right;">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {if !empty($regions)}
                            {loop $regions $region}
                                <tr id="item-{$region.id}" data-id="{$region.id}">
                                    <td><input type="checkbox" name="region_ids[]" value="{$region.id}" class="check-item"></td>
                                    <td>
                                        <input type="number" class="sort-input" name="sort[{$region.id}]" value="{$region.sort_order}" min="0">
                                    </td>
                                    <td>{$region.id}</td>
                                    <td>{$region.name}</td>
                                    <td>{$region.pinyin}</td>
                                    <td>{$region.level_text}</td>
                                    <td>
                                        {if $region.child_count > 0}
                                            <a href="region.php?parent_id={$region.id}" class="badge badge-info">{$region.child_count} 个</a>
                                        {else}
                                            <span class="badge badge-secondary">0</span>
                                        {/if}
                                    </td>
                                    <td>
                                        <div class="region-actions">
                                            {if $region.child_count > 0}
                                                <a href="region.php?parent_id={$region.id}" class="btn btn-light-info">子区域</a>
                                            {else}
                                                <a href="region.php?action=add&parent_id={$region.id}" class="btn btn-light-success">添加子区域</a>
                                            {/if}
                                            <a href="region.php?action=edit&id={$region.id}" class="btn btn-light-primary">编辑</a>
                                            <a href="region.php?action=delete&id={$region.id}&return_parent_id={$parent_id}" class="btn btn-light-danger delete-btn">删除</a>
                                        </div>
                                    </td>
                                </tr>
                            {/loop}
                        {else}
                            <tr>
                                <td colspan="8" style="text-align: center;">没有找到区域记录</td>
                            </tr>
                        {/if}
                    </tbody>
                </table>
            </div>
            
            <!-- 分页和批量操作 -->
            <div style="margin-top: 20px; display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap;">
                <!-- 左侧全选和批量删除 -->
                <div style="margin-bottom: 15px;">
                    <label style="margin-right: 10px; display: inline-flex; align-items: center; cursor: pointer;">
                        <input type="checkbox" id="selectAll" style="margin-right: 5px;"> 全选
                    </label>
                    <button type="button" id="batch-delete-btn" class="btn btn-light-danger">批量删除</button>
                </div>
                
                <!-- 分页 -->
                <div style="flex: 1; text-align: right;">
                    {if isset($pagination) && $pagination.total_pages > 1}
                    <div>
                        <div class="simple-pagination" style="justify-content: flex-end;">
                            {if $pagination.current_page > 1}
                            <a href="{$pagination.first_link}" class="pagination-btn">&laquo;</a>
                            <a href="{$pagination.previous_link}" class="pagination-btn">&lsaquo;</a>
                            {else}
                            <span class="pagination-btn disabled">&laquo;</span>
                            <span class="pagination-btn disabled">&lsaquo;</span>
                            {/if}
                            
                            {if isset($pagination.page_links) && $pagination.page_links}
                                {loop $pagination.page_links $page $link}
                                    {if $page == $pagination.current_page}
                                    <span class="pagination-btn active">{$page}</span>
                                    {else}
                                    <a href="{$link}" class="pagination-btn">{$page}</a>
                                    {/if}
                                {/loop}
                            {/if}
                            
                            {if $pagination.current_page < $pagination.total_pages}
                            <a href="{$pagination.next_link}" class="pagination-btn">&rsaquo;</a>
                            <a href="{$pagination.last_link}" class="pagination-btn">&raquo;</a>
                            {else}
                            <span class="pagination-btn disabled">&rsaquo;</span>
                            <span class="pagination-btn disabled">&raquo;</span>
                            {/if}
                            
                            <span style="margin-left: 10px; color: #6c757d; font-size: 14px;">共 {$pagination.total_pages} 页</span>
                        </div>
                    </div>
                    {/if}
                </div>
            </div>
        </form>
    </div>
</div>

{include file="footer.htm"}



<script type="text/javascript">
// 简化版
window.onload = function() {
    console.log('页面加载完成');
    
    // 全选功能 - 头部复选框
    var checkAll = document.getElementById('check-all');
    if(checkAll) {
        checkAll.onclick = function() {
            var checked = this.checked;
            console.log('头部全选框点击:', checked);
            
            // 获取所有复选框
            var checkboxes = document.getElementsByClassName('check-item');
            for (var i = 0; i < checkboxes.length; i++) {
                checkboxes[i].checked = checked;
            }
            
            // 同步底部全选框
            var selectAll = document.getElementById('selectAll');
            if(selectAll) {
                selectAll.checked = checked;
            }
        };
    }
    
    // 底部全选框
    var selectAll = document.getElementById('selectAll');
    if(selectAll) {
        selectAll.onclick = function() {
            var checked = this.checked;
            console.log('底部全选框点击:', checked);
            
            // 获取所有复选框
            var checkboxes = document.getElementsByClassName('check-item');
            for (var i = 0; i < checkboxes.length; i++) {
                checkboxes[i].checked = checked;
            }
            
            // 同步顶部全选框
            var checkAll = document.getElementById('check-all');
            if(checkAll) {
                checkAll.checked = checked;
            }
        };
    }
    
    // 批量删除按钮
    var batchDeleteBtn = document.getElementById('batch-delete-btn');
    if(batchDeleteBtn) {
        batchDeleteBtn.onclick = function() {
            var checkboxes = document.getElementsByClassName('check-item');
            var checkedCount = 0;
            
            for (var i = 0; i < checkboxes.length; i++) {
                if (checkboxes[i].checked) {
                    checkedCount++;
                }
            }
            
            console.log('选中数量:', checkedCount);
            
            if (checkedCount === 0) {
                alert('请至少选择一个区域');
                return false;
            }
            
            if (confirm('确定要删除选中的' + checkedCount + '个区域吗？此操作不可恢复!')) {
                document.getElementById('list-form').submit();
            }
        };
    }
    
    // 保存排序按钮
    var saveSortBtn = document.getElementById('save-sort-btn');
    if(saveSortBtn) {
        saveSortBtn.onclick = function() {
            console.log('保存排序按钮点击');
            
            var sortData = [];
            var inputs = document.getElementsByClassName('sort-input');
            
            for (var i = 0; i < inputs.length; i++) {
                var input = inputs[i];
                var row = input.closest('tr');
                var id = row.getAttribute('data-id');
                var value = input.value;
                
                sortData.push({
                    id: parseInt(id),
                    sort: parseInt(value)
                });
            }
            
            // 生成JSON字符串
            var jsonString = JSON.stringify(sortData);
            console.log('排序数据JSON:', jsonString);
            
            // 创建请求数据对象
            var requestData = {
                sort_data: sortData,
                return_parent_id: {$parent_id}
            };
            
            // 使用XMLHttpRequest直接发送JSON
            var xhr = new XMLHttpRequest();
            xhr.open('POST', 'region.php?action=save_sort', true);
            xhr.setRequestHeader('Content-Type', 'application/json');
            
            // 处理响应
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        try {
                            var response = JSON.parse(xhr.responseText);
                            console.log('服务器响应:', response);
                            
                            if (response.success) {
                                alert('排序保存成功: ' + response.message);
                                // 刷新页面
                                window.location.reload();
                            } else {
                                alert('保存失败: ' + response.message);
                            }
                        } catch (e) {
                            console.error('解析响应失败:', e);
                            // 如果不是JSON响应，可能是重定向或HTML
                            window.location.reload();
                        }
                    } else {
                        alert('保存排序失败: 服务器错误 (' + xhr.status + ')');
                    }
                }
            };
            
            // 添加错误处理
            xhr.onerror = function() {
                console.error('请求错误');
                alert('网络错误，请稍后重试');
            };
            
            // 发送JSON数据
            xhr.send(JSON.stringify(requestData));
        };
    }
    

    


    // 单个删除功能
    var deleteButtons = document.getElementsByClassName('delete-btn');
    for (var i = 0; i < deleteButtons.length; i++) {
        deleteButtons[i].onclick = function(e) {
            e.preventDefault();
            var url = this.getAttribute('href');
            console.log('单项删除点击, URL:', url);
            
            if (confirm('确定要删除该区域吗？此操作不可恢复!')) {
                window.location.href = url;
            }
        };
    }
};
</script> 