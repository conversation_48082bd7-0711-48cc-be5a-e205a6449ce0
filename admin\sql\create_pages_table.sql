-- 创建单页表
CREATE TABLE IF NOT EXISTS `pages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL COMMENT '页面标题',
  `path` varchar(255) NOT NULL COMMENT '页面路径（相对于根目录）',
  `url` varchar(255) NOT NULL COMMENT '页面URL',
  `content` longtext COMMENT '页面内容',
  `meta_keywords` varchar(500) DEFAULT NULL COMMENT 'SEO关键词',
  `meta_description` varchar(500) DEFAULT NULL COMMENT 'SEO描述',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态: 0=禁用, 1=启用',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `created_at` int(10) NOT NULL COMMENT '创建时间',
  `updated_at` int(10) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `path` (`path`),
  UNIQUE KEY `url` (`url`),
  KEY `status` (`status`),
  KEY `sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='单页表';

-- 插入示例数据（伪静态格式）
INSERT INTO `pages` (`title`, `path`, `url`, `content`, `meta_keywords`, `meta_description`, `status`, `sort_order`, `created_at`, `updated_at`) VALUES
('关于我们', 'about/jianjie', '/page/about/jianjie.html', '<h1>关于我们</h1><p>这里是关于我们的内容...</p><p>我们是一家专业的分类信息网站，致力于为用户提供便捷的信息发布和查找服务。</p>', '关于我们,公司介绍', '了解我们公司的发展历程和企业文化', 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('联系我们', 'about/contactus', '/page/about/contactus.html', '<h1>联系我们</h1><p><strong>客服电话：</strong>400-123-4567</p><p><strong>邮箱：</strong><EMAIL></p><p><strong>地址：</strong>北京市朝阳区xxx路xxx号</p>', '联系我们,联系方式', '获取我们的联系方式和地址信息', 1, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('使用帮助', 'help/index', '/page/help/index.html', '<h1>使用帮助</h1><h2>如何发布信息</h2><p>1. 注册账号</p><p>2. 选择分类</p><p>3. 填写信息</p><p>4. 提交审核</p><h2>常见问题</h2><p>Q: 如何修改已发布的信息？</p><p>A: 登录后在个人中心进行修改。</p>', '使用帮助,操作指南', '详细的使用帮助和操作指南', 1, 3, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());
