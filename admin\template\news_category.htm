{include file="header.htm"}

<style>
    /* 基础样式 */
    .page-title {
        font-size: 20px;
        margin-bottom: 15px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .page-title h1 {
        margin: 0;
        font-size: 24px;
        font-weight: 500;
    }
    .content-table {
        width: 100%;
        border-collapse: collapse;
        background: #fff;
        border-radius: 4px;
        box-shadow: 0 1px 2px rgba(0,0,0,0.05);
    }
    .content-table th {
        background-color: #f8f9fa;
        padding: 10px 15px;
        text-align: left;
        font-weight: 500;
        color: #333;
        border-bottom: 1px solid #dee2e6;
    }
    .content-table td {
        padding: 10px 15px;
        border-bottom: 1px solid #dee2e6;
        vertical-align: middle;
    }
    .content-table tr:last-child td {
        border-bottom: none;
    }
    .content-table tr:hover {
        background-color: #f8f9fa;
    }

    /* 淡色按钮样式 */
    .btn-light-primary {
        background-color: #e6f0ff;
        color: #1b68ff;
        border: 1px solid #cce0ff;
    }
    .btn-light-primary:hover {
        background-color: #d1e3ff;
        color: #0056b3;
    }
    .btn-light-warning {
        background-color: #fff8e6;
        color: #ffa500;
        border: 1px solid #ffe6b3;
    }
    .btn-light-warning:hover {
        background-color: #fff0d1;
        color: #cc8400;
    }
    .btn-light-danger {
        background-color: #ffe6e6;
        color: #ff3333;
        border: 1px solid #ffb3b3;
    }
    .btn-light-danger:hover {
        background-color: #ffd1d1;
        color: #cc0000;
    }
    .btn-light-info {
        background-color: #e6f7ff;
        color: #00aaff;
        border: 1px solid #b3e0ff;
    }
    .btn-light-info:hover {
        background-color: #d1f0ff;
        color: #0088cc;
    }
    .btn-light-success {
        background-color: #e6ffe6;
        color: #00aa00;
        border: 1px solid #b3ffb3;
    }
    .btn-light-success:hover {
        background-color: #d1ffd1;
        color: #008800;
    }
    .btn-light-secondary {
        background-color: #f0f0f0;
        color: #666666;
        border: 1px solid #dddddd;
    }
    .btn-light-secondary:hover {
        background-color: #e0e0e0;
        color: #444444;
    }
    .btn {
        display: inline-block;
        font-weight: 400;
        text-align: center;
        white-space: nowrap;
        vertical-align: middle;
        user-select: none;
        border: 1px solid transparent;
        padding: 0.375rem 0.75rem;
        font-size: 0.9rem;
        line-height: 1.5;
        border-radius: 0.25rem;
        transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        cursor: pointer;
        text-decoration: none;
    }
    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        line-height: 1.5;
        border-radius: 0.2rem;
    }
    .alert {
        position: relative;
        padding: 12px 20px;
        margin-bottom: 16px;
        border: 1px solid transparent;
        border-radius: 4px;
    }
    .alert-success {
        color: #155724;
        background-color: #d4edda;
        border-color: #c3e6cb;
    }
    .alert-danger {
        color: #721c24;
        background-color: #f8d7da;
        border-color: #f5c6cb;
    }
    .badge {
        display: inline-block;
        padding: 0.25em 0.5em;
        font-size: 75%;
        font-weight: 700;
        line-height: 1;
        text-align: center;
        white-space: nowrap;
        vertical-align: baseline;
        border-radius: 0.25rem;
    }
    .bg-success {
        background-color: #28a745 !important;
        color: #fff;
    }
    .bg-danger {
        background-color: #dc3545 !important;
        color: #fff;
    }
    .text-center {
        text-align: center;
    }
    .text-right {
        text-align: right;
    }
    
    /* 分页样式 */
    .pagination-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin: 20px 0;
    }
    .simple-pagination {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;
        gap: 5px;
    }
    .pagination-btn {
        display: inline-block;
        padding: 5px 12px;
        background: #fff;
        border: 1px solid #ddd;
        color: #333;
        text-decoration: none;
        border-radius: 3px;
        transition: all 0.2s;
    }
    .pagination-btn:hover {
        background: #f8f9fa;
        border-color: #ccc;
    }
    .pagination-btn.active {
        background: #1b68ff;
        color: white;
        border-color: #1b68ff;
    }
    .pagination-btn.disabled {
        color: #aaa;
        background: #f8f8f8;
        cursor: not-allowed;
    }
    .category-actions {
        display: flex;
        flex-wrap: nowrap;
        justify-content: flex-end;
        gap: 5px;
        min-width: 120px;
    }
    .category-actions .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        white-space: nowrap;
        text-decoration: none !important;
    }
    a, a:hover, a:focus, a:active {
        text-decoration: none !important;
    }
    .category-level-1 { font-weight: 500; }
    .category-level-2 { padding-left: 15px; }
    .filter-section {
        background: #fff;
        border-radius: 4px;
        box-shadow: 0 1px 2px rgba(0,0,0,0.05);
        margin-bottom: 20px;
        padding: 15px;
    }
    .filter-form {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        gap: 10px;
    }
    .filter-form label {
        margin-bottom: 0;
        margin-right: 5px;
        font-weight: 500;
    }
    .filter-form .form-control {
        width: auto; 
        display: inline-block;
        padding: 4px 8px;
        font-size: 14px;
        line-height: 1.5;
        color: #333;
        background-color: #fff;
        border: 1px solid #ddd;
        border-radius: 4px;
        transition: border-color 0.15s ease-in-out;
    }
    .filter-form button, .filter-form .btn {
        margin-left: 5px;
    }
    .filter-form-item {
        display: flex;
        align-items: center;
        margin-right: 15px;
    }
    .category-status-badge {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 5px;
    }
    .status-active {
        background-color: #28a745;
    }
    .status-inactive {
        background-color: #dc3545;
    }
    /* 筛选标签样式 */
    .filter-tag {
        display: inline-block;
        padding: 6px 10px;
        font-size: 12px;
        font-weight: normal;
        color: #fff;
        text-decoration: none;
        border-radius: 4px;
        margin-right: 5px;
        margin-bottom: 5px;
    }
    .filter-tag.active-all { background-color: #007bff; }
    .filter-tag.inactive-all { background-color: #6c757d; }
    .filter-tag.active-first { background-color: #007bff; }
    .filter-tag.inactive-first { background-color: #6c757d; }
    .filter-tag.active-second { background-color: #007bff; }
    .filter-tag.inactive-second { background-color: #6c757d; }

    /* 状态标签样式 */
    .tag {
        display: inline-block;
        padding: 2px 6px;
        font-size: 11px;
        font-weight: normal;
        color: #fff;
        border-radius: 3px;
        text-align: center;
        white-space: nowrap;
    }
    .tag-success {
        background-color: #28a745;
        color: #fff;
    }
    .tag-danger {
        background-color: #dc3545;
        color: #fff;
    }
    .tag-warning {
        background-color: #ffc107;
        color: #212529;
    }
    .tag-primary {
        background-color: #007bff;
        color: #fff;
    }
</style>

<div class="container-fluid">
    <!-- 消息提示 -->
    {if isset($message) && $message != ''}
    <div class="alert alert-success">{$message}</div>
    {/if}
    {if isset($error) && $error != ''}
    <div class="alert alert-danger">{$error}</div>
    {/if}

    <!-- 新闻栏目管理 -->
    <div class="card mb-4">
        <div class="card-body">
            <!-- 高级筛选表单 -->
            <div style="margin-bottom: 20px; padding-bottom: 15px; border-bottom: 1px solid #e9ecef;">
                <form action="news_category.php" method="get" style="display: flex; align-items: center; flex-wrap: wrap; gap: 15px;">
                    <div style="display: flex; align-items: center; white-space: nowrap;">
                        <span style="margin-right: 5px; color: #333;">栏目级别:</span>
                        <select name="parent_id" id="parent_id" style="width: 150px; height: 32px; border: 1px solid #e9ecef; border-radius: 4px; padding: 0 8px;">
                            <option value="-1" {if $parent_id == -1}selected{/if}>一级栏目</option>
                            <option value="-2" {if $parent_id == -2}selected{/if}>二级栏目</option>
                            <option value="0" {if $parent_id == 0}selected{/if}>所有栏目</option>
                            {if isset($parentCategories) && count($parentCategories) > 0}
                            <optgroup label="特定父栏目的子栏目">
                                {foreach from=$parentCategories item=cat}
                                <option value="{$cat.catid}" {if $parent_id == $cat.catid}selected{/if}>{$cat.catname}的子栏目</option>
                                {/foreach}
                            </optgroup>
                            {/if}
                        </select>
                    </div>
                    
                    <div style="display: flex; align-items: center; white-space: nowrap;">
                        <span style="margin-right: 5px; color: #333;">栏目关键字:</span>
                        <input type="text" name="keyword" value="{$keyword}" placeholder="搜索栏目名称..." style="width: 200px; height: 32px; border: 1px solid #e9ecef; border-radius: 4px; padding: 0 8px;">
                    </div>

                    <div style="display: flex; align-items: center; white-space: nowrap;">
                        <span style="margin-right: 5px; color: #333;">状态:</span>
                        <select name="status" style="width: 120px; height: 32px; border: 1px solid #e9ecef; border-radius: 4px; padding: 0 8px;">
                            <option value="-1" {if $status == -1}selected{/if}>全部</option>
                            <option value="1" {if $status == 1}selected{/if}>启用</option>
                            <option value="0" {if $status == 0}selected{/if}>禁用</option>
                        </select>
                    </div>
                    
                    <div>
                        <button type="submit" class="btn btn-sm btn-light-primary" style="height: 32px; line-height: 32px; padding: 0 12px; display: inline-flex; align-items: center; justify-content: center;">筛选</button>
                        <a href="news_category.php" class="btn btn-sm btn-light-secondary" style="height: 32px; line-height: 32px; padding: 0 12px; display: inline-flex; align-items: center; justify-content: center;">重置</a>
                    </div>
                    
                    <div style="margin-left: auto;">
                        <a href="news_category.php?op=add" class="btn btn-sm btn-light-success" style="height: 32px; line-height: 32px; padding: 0 12px; display: inline-flex; align-items: center; justify-content: center;">添加栏目</a>
                        {if $parent_id > 0}
                        <a href="news_category.php?op=add&parent_id={$parent_id}" class="btn btn-sm btn-light-success" style="height: 32px; line-height: 32px; padding: 0 12px; display: inline-flex; align-items: center; justify-content: center;">添加子栏目</a>
                        {/if}
                    </div>
                </form>
            </div>

            <!-- 栏目列表 -->
            <!-- 批量操作表单 -->
            <form id="batchForm" action="news_category.php?op=batch_delete" method="post">
                <input type="hidden" name="return_parent_id" value="{$parent_id}">
                
                <div class="table-responsive">
                    <table class="content-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>栏目名称</th>
                                <th>排序</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {if isset($categories) && count($categories) > 0}
                            {foreach from=$categories item=cat}
                            <tr>
                                <td>{$cat.catid}</td>
                                <td class="category-level-{$cat.level}">{$cat.catname}</td>
                                <td>{$cat.sort_order}</td>
                                <td>
                                    {if $cat.is_show == 1}
                                    <span class="tag tag-success">启用</span>
                                    {else}
                                    <span class="tag tag-danger">禁用</span>
                                    {/if}
                                </td>
                                <td class="category-actions">
                                    <a href="../news.php?catid={$cat.catid}" class="btn btn-sm btn-light-info" target="_blank">前台</a>
                                    {if $cat.has_children > 0}
                                    <a href="news_category.php?parent_id={$cat.catid}" class="btn btn-sm btn-light-primary">子栏目</a>
                                    {/if}
                                    {if $cat.level == 1}
                                    <a href="news_category.php?op=add&parent_id={$cat.catid}" class="btn btn-sm btn-light-success">添加子栏目</a>
                                    {/if}
                                    <a href="news_category.php?op=edit&catid={$cat.catid}" class="btn btn-sm btn-light-primary">编辑</a>
                                    {if $cat.is_show == 1}
                                    <a href="news_category.php?op=toggle_status&catid={$cat.catid}&return_parent_id={$parent_id}" class="btn btn-sm btn-light-warning" onclick="return confirm('确定要禁用此栏目吗？');">禁用</a>
                                    {else}
                                    <a href="news_category.php?op=toggle_status&catid={$cat.catid}&return_parent_id={$parent_id}" class="btn btn-sm btn-light-success" onclick="return confirm('确定要启用此栏目吗？');">启用</a>
                                    {/if}
                                    <button type="button" class="btn btn-sm btn-light-danger" onclick="deleteCategory({$cat.catid})">删除</button>
                                </td>
                            </tr>
                            {/foreach}
                            {else}
                            <tr>
                                <td colspan="5" class="text-center">暂无栏目数据</td>
                            </tr>
                            {/if}
                        </tbody>
                    </table>
                </div>
                
                <!-- 批量操作按钮和分页 -->
                <div style="margin-top: 15px; display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap;">
                    <!-- 批量操作 -->
                    <div style="margin-bottom: 15px;">
                        <button type="button" id="batch_delete_btn" class="btn btn-sm btn-light-danger" style="height: 32px; line-height: 32px; padding: 0 12px; display: inline-flex; align-items: center; justify-content: center;">批量删除</button>
                        <div id="selection_count" style="color: #666; display: inline-block; margin-left: 10px;">已选择 0 个栏目</div>
                    </div>
                    
                    <!-- 分页 -->
                    {if isset($pagination) && $total > 0}
                    <div style="flex: 1; text-align: right;">
                        <div class="simple-pagination" style="justify-content: flex-end;">
                            {$pagination_html}
                            <span style="margin-left: 10px; color: #6c757d; font-size: 14px;">共 {$pagination.total_page} 页</span>
                        </div>
                    </div>
                    {/if}
                </div>
            </form>
        </div>
    </div>
</div>

{include file="footer.htm"}

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 设置当前菜单选中状态
    var menuElement = document.getElementById("menu_news_category");
    if (menuElement) {
        menuElement.className += " active";
    }
    
    // 全选功能
    var selectAllCheckbox = document.getElementById('select_all');
    var categoryCheckboxes = document.querySelectorAll('.category-checkbox');
    var selectionCount = document.getElementById('selection_count');
    
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            var isChecked = this.checked;
            categoryCheckboxes.forEach(function(checkbox) {
                checkbox.checked = isChecked;
            });
            updateSelectionCount();
        });
    }
    
    // 更新选中数量
    categoryCheckboxes.forEach(function(checkbox) {
        checkbox.addEventListener('change', function() {
            updateSelectionCount();
            
            // 检查是否所有复选框都被选中
            var allChecked = true;
            categoryCheckboxes.forEach(function(cb) {
                if (!cb.checked) {
                    allChecked = false;
                }
            });
            
            // 更新全选复选框状态
            if (selectAllCheckbox) {
                selectAllCheckbox.checked = allChecked;
            }
        });
    });
    
    // 更新选中计数
    function updateSelectionCount() {
        var count = 0;
        categoryCheckboxes.forEach(function(checkbox) {
            if (checkbox.checked) {
                count++;
            }
        });
        
        if (selectionCount) {
            selectionCount.textContent = '已选择 ' + count + ' 个栏目';
        }
    }
    
    // 批量删除按钮
    var batchDeleteBtn = document.getElementById('batch_delete_btn');
    if (batchDeleteBtn) {
        batchDeleteBtn.addEventListener('click', function() {
            var checkedCount = 0;
            var hasChecked = false;
            
            categoryCheckboxes.forEach(function(checkbox) {
                if (checkbox.checked) {
                    hasChecked = true;
                    checkedCount++;
                }
            });
            
            if (!hasChecked) {
                alert('请先选择要删除的栏目');
                return false;
            }
            
            if (confirm('确定要删除选中的 ' + checkedCount + ' 个栏目吗？此操作不可恢复！')) {
                // 提交表单前确保有被选中的checkbox
                if (checkedCount > 0) {
                    document.getElementById('batchForm').submit();
                } else {
                    alert('请先选择要删除的栏目');
                    return false;
                }
            }
        });
    }
    
    // 过滤器表单下拉框自动提交
    var parentIdSelect = document.getElementById('parent_id');
    if (parentIdSelect) {
        parentIdSelect.addEventListener('change', function() {
            this.form.submit();
        });
    }
});

// 删除栏目
function deleteCategory(catid) {
    if (confirm('确定要删除该栏目吗？此操作不可恢复！')) {
        location.href = 'news_category.php?op=delete&catid=' + catid;
    }
}
</script> 