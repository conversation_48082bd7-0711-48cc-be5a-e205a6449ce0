/**
 * 通用图片压缩库
 * 支持前台、后台、手机端的图片压缩功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

class ImageCompressor {
    constructor(options = {}) {
        // 默认配置
        this.config = {
            // 压缩质量 (0-1)
            quality: 0.8,
            // 最大宽度
            maxWidth: 1920,
            // 最大高度  
            maxHeight: 1080,
            // 最大文件大小 (字节)
            maxSize: 4 * 1024 * 1024, // 4MB
            // 压缩后的最大文件大小 (字节)
            targetSize: 2 * 1024 * 1024, // 2MB
            // 输出格式
            outputFormat: 'image/jpeg',
            // 是否保持宽高比
            maintainAspectRatio: true,
            // 是否启用渐进式压缩
            enableProgressiveCompression: true,
            // 压缩步长
            qualityStep: 0.1,
            // 最小质量
            minQuality: 0.3,
            // 调试模式
            debug: false
        };

        // 合并用户配置
        Object.assign(this.config, options);
        
        // 支持的图片格式
        this.supportedFormats = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'];
        
        // 回调函数
        this.callbacks = {
            onStart: null,
            onProgress: null,
            onSuccess: null,
            onError: null
        };
    }

    /**
     * 设置回调函数
     */
    on(event, callback) {
        if (this.callbacks.hasOwnProperty('on' + event.charAt(0).toUpperCase() + event.slice(1))) {
            this.callbacks['on' + event.charAt(0).toUpperCase() + event.slice(1)] = callback;
        }
        return this;
    }

    /**
     * 压缩单个文件
     */
    async compressFile(file) {
        return new Promise((resolve, reject) => {
            try {
                // 触发开始回调
                if (this.callbacks.onStart) {
                    this.callbacks.onStart(file);
                }

                // 检查文件类型
                if (!this.isValidImageType(file.type)) {
                    throw new Error(`不支持的图片格式: ${file.type}`);
                }

                // 检查文件大小
                if (file.size > this.config.maxSize) {
                    throw new Error(`文件过大: ${this.formatFileSize(file.size)}, 最大允许: ${this.formatFileSize(this.config.maxSize)}`);
                }

                // 如果文件已经小于目标大小，直接返回
                if (file.size <= this.config.targetSize) {
                    this.log('文件已经符合大小要求，无需压缩');
                    resolve({
                        file: file,
                        originalSize: file.size,
                        compressedSize: file.size,
                        compressionRatio: 1,
                        quality: 1
                    });
                    return;
                }

                // 开始压缩
                this.performCompression(file)
                    .then(result => {
                        if (this.callbacks.onSuccess) {
                            this.callbacks.onSuccess(result);
                        }
                        resolve(result);
                    })
                    .catch(error => {
                        if (this.callbacks.onError) {
                            this.callbacks.onError(error);
                        }
                        reject(error);
                    });

            } catch (error) {
                if (this.callbacks.onError) {
                    this.callbacks.onError(error);
                }
                reject(error);
            }
        });
    }

    /**
     * 批量压缩文件
     */
    async compressFiles(files) {
        const results = [];
        const total = files.length;

        for (let i = 0; i < files.length; i++) {
            try {
                if (this.callbacks.onProgress) {
                    this.callbacks.onProgress({
                        current: i + 1,
                        total: total,
                        percentage: Math.round(((i + 1) / total) * 100)
                    });
                }

                const result = await this.compressFile(files[i]);
                results.push(result);
            } catch (error) {
                results.push({
                    error: error.message,
                    file: files[i]
                });
            }
        }

        return results;
    }

    /**
     * 执行图片压缩
     */
    async performCompression(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = (e) => {
                const img = new Image();
                
                img.onload = () => {
                    try {
                        const result = this.compressImage(img, file);
                        resolve(result);
                    } catch (error) {
                        reject(error);
                    }
                };
                
                img.onerror = () => {
                    reject(new Error('图片加载失败'));
                };
                
                img.src = e.target.result;
            };
            
            reader.onerror = () => {
                reject(new Error('文件读取失败'));
            };
            
            reader.readAsDataURL(file);
        });
    }

    /**
     * 压缩图片
     */
    compressImage(img, originalFile) {
        // 计算新的尺寸
        const dimensions = this.calculateDimensions(img.width, img.height);
        
        // 创建Canvas
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        canvas.width = dimensions.width;
        canvas.height = dimensions.height;
        
        // 设置图片平滑
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = 'high';
        
        // 绘制图片
        ctx.drawImage(img, 0, 0, dimensions.width, dimensions.height);
        
        // 压缩图片
        let quality = this.config.quality;
        let compressedFile = null;
        let attempts = 0;
        const maxAttempts = 10;
        
        while (attempts < maxAttempts) {
            const dataUrl = canvas.toDataURL(this.config.outputFormat, quality);
            compressedFile = this.dataURLToFile(dataUrl, originalFile.name);

            
            // 如果文件大小符合要求或质量已经很低，停止压缩
            if (compressedFile.size <= this.config.targetSize || quality <= this.config.minQuality) {
                break;
            }
            
            // 降低质量继续压缩
            quality -= this.config.qualityStep;
            attempts++;
        }
        
        return {
            file: compressedFile,
            originalSize: originalFile.size,
            compressedSize: compressedFile.size,
            compressionRatio: (compressedFile.size / originalFile.size).toFixed(2),
            quality: quality.toFixed(2),
            dimensions: dimensions,
            originalDimensions: {
                width: img.width,
                height: img.height
            }
        };
    }

    /**
     * 计算压缩后的尺寸
     */
    calculateDimensions(width, height) {
        let newWidth = width;
        let newHeight = height;

        if (this.config.maintainAspectRatio) {
            // 保持宽高比
            const aspectRatio = width / height;
            
            if (width > this.config.maxWidth) {
                newWidth = this.config.maxWidth;
                newHeight = newWidth / aspectRatio;
            }
            
            if (newHeight > this.config.maxHeight) {
                newHeight = this.config.maxHeight;
                newWidth = newHeight * aspectRatio;
            }
        } else {
            // 不保持宽高比
            newWidth = Math.min(width, this.config.maxWidth);
            newHeight = Math.min(height, this.config.maxHeight);
        }

        return {
            width: Math.round(newWidth),
            height: Math.round(newHeight)
        };
    }

    /**
     * 检查图片类型是否支持
     */
    isValidImageType(type) {
        return this.supportedFormats.includes(type.toLowerCase());
    }

    /**
     * DataURL转File对象
     */
    dataURLToFile(dataURL, filename) {
        const arr = dataURL.split(',');
        const mime = arr[0].match(/:(.*?);/)[1];
        const bstr = atob(arr[1]);
        let n = bstr.length;
        const u8arr = new Uint8Array(n);
        
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n);
        }
        
        return new File([u8arr], filename, { type: mime });
    }

    /**
     * 格式化文件大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * 日志输出
     */
    log(message) {
        if (this.config.debug) {
            console.log('[ImageCompressor]', message);
        }
    }
}

    /**
     * 创建压缩进度显示
     */
    createProgressIndicator(container) {
        const progressHtml = `
            <div class="image-compress-progress" style="display: none;">
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 0%"></div>
                </div>
                <div class="progress-text">正在压缩图片...</div>
            </div>
        `;

        if (typeof container === 'string') {
            container = document.querySelector(container);
        }

        if (container) {
            container.insertAdjacentHTML('beforeend', progressHtml);
            return container.querySelector('.image-compress-progress');
        }

        return null;
    }

    /**
     * 更新进度显示
     */
    updateProgress(progressElement, percentage, text) {
        if (!progressElement) return;

        const fill = progressElement.querySelector('.progress-fill');
        const textElement = progressElement.querySelector('.progress-text');

        if (fill) fill.style.width = percentage + '%';
        if (textElement && text) textElement.textContent = text;

        progressElement.style.display = percentage > 0 ? 'block' : 'none';
    }

    /**
     * 获取图片EXIF信息
     */
    getImageOrientation(file) {
        return new Promise((resolve) => {
            const reader = new FileReader();

            reader.onload = function(e) {
                const view = new DataView(e.target.result);

                if (view.getUint16(0, false) !== 0xFFD8) {
                    resolve(1); // 不是JPEG文件
                    return;
                }

                const length = view.byteLength;
                let offset = 2;

                while (offset < length) {
                    const marker = view.getUint16(offset, false);
                    offset += 2;

                    if (marker === 0xFFE1) {
                        const exifLength = view.getUint16(offset, false);
                        offset += 2;

                        if (view.getUint32(offset, false) === 0x45786966) {
                            const orientation = this.getOrientationFromExif(view, offset + 4);
                            resolve(orientation);
                            return;
                        }
                    } else {
                        offset += view.getUint16(offset, false);
                    }
                }

                resolve(1); // 默认方向
            };

            reader.readAsArrayBuffer(file.slice(0, 64 * 1024));
        });
    }

    /**
     * 从EXIF数据中获取方向信息
     */
    getOrientationFromExif(view, offset) {
        const littleEndian = view.getUint16(offset, false) === 0x4949;

        if (view.getUint16(offset + 2, littleEndian) !== 0x002A) {
            return 1;
        }

        const firstIFDOffset = view.getUint32(offset + 4, littleEndian);
        const count = view.getUint16(offset + firstIFDOffset, littleEndian);

        for (let i = 0; i < count; i++) {
            const entryOffset = offset + firstIFDOffset + 2 + (i * 12);
            const tag = view.getUint16(entryOffset, littleEndian);

            if (tag === 0x0112) { // Orientation tag
                return view.getUint16(entryOffset + 8, littleEndian);
            }
        }

        return 1;
    }

    /**
     * 根据EXIF方向旋转图片
     */
    rotateImageByOrientation(canvas, ctx, img, orientation) {
        const { width, height } = canvas;

        switch (orientation) {
            case 2:
                ctx.transform(-1, 0, 0, 1, width, 0);
                break;
            case 3:
                ctx.transform(-1, 0, 0, -1, width, height);
                break;
            case 4:
                ctx.transform(1, 0, 0, -1, 0, height);
                break;
            case 5:
                ctx.transform(0, 1, 1, 0, 0, 0);
                break;
            case 6:
                ctx.transform(0, 1, -1, 0, height, 0);
                break;
            case 7:
                ctx.transform(0, -1, -1, 0, height, width);
                break;
            case 8:
                ctx.transform(0, -1, 1, 0, 0, width);
                break;
            default:
                break;
        }
    }

    /**
     * 智能压缩 - 根据图片内容调整压缩参数
     */
    smartCompress(img, originalFile) {
        // 分析图片复杂度
        const complexity = this.analyzeImageComplexity(img);

        // 根据复杂度调整压缩参数
        let adjustedConfig = { ...this.config };

        if (complexity.isPhoto) {
            // 照片类图片，保持较高质量
            adjustedConfig.quality = Math.max(0.7, this.config.quality);
            adjustedConfig.minQuality = Math.max(0.5, this.config.minQuality);
        } else if (complexity.hasText) {
            // 包含文字的图片，保持清晰度
            adjustedConfig.quality = Math.max(0.8, this.config.quality);
            adjustedConfig.minQuality = Math.max(0.6, this.config.minQuality);
        } else {
            // 简单图片，可以更大程度压缩
            adjustedConfig.quality = Math.min(0.6, this.config.quality);
            adjustedConfig.minQuality = Math.min(0.3, this.config.minQuality);
        }

        // 临时更新配置
        const originalConfig = this.config;
        this.config = adjustedConfig;

        const result = this.compressImage(img, originalFile);

        // 恢复原配置
        this.config = originalConfig;

        return result;
    }

    /**
     * 分析图片复杂度
     */
    analyzeImageComplexity(img) {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        // 缩小图片用于分析
        const analyzeSize = 100;
        canvas.width = analyzeSize;
        canvas.height = analyzeSize;

        ctx.drawImage(img, 0, 0, analyzeSize, analyzeSize);

        const imageData = ctx.getImageData(0, 0, analyzeSize, analyzeSize);
        const data = imageData.data;

        let colorVariance = 0;
        let edgeCount = 0;
        const colors = new Set();

        // 分析颜色变化和边缘
        for (let i = 0; i < data.length; i += 4) {
            const r = data[i];
            const g = data[i + 1];
            const b = data[i + 2];

            colors.add(`${r},${g},${b}`);

            // 简单的边缘检测
            if (i > analyzeSize * 4) {
                const prevR = data[i - analyzeSize * 4];
                const prevG = data[i - analyzeSize * 4 + 1];
                const prevB = data[i - analyzeSize * 4 + 2];

                const diff = Math.abs(r - prevR) + Math.abs(g - prevG) + Math.abs(b - prevB);
                if (diff > 50) {
                    edgeCount++;
                }
            }
        }

        const uniqueColors = colors.size;
        const totalPixels = analyzeSize * analyzeSize;

        return {
            isPhoto: uniqueColors > totalPixels * 0.3 && edgeCount > totalPixels * 0.1,
            hasText: edgeCount > totalPixels * 0.2,
            colorComplexity: uniqueColors / totalPixels,
            edgeComplexity: edgeCount / totalPixels
        };
    }
}

// 工具函数
ImageCompressor.utils = {
    /**
     * 检测浏览器支持的图片格式
     */
    getSupportedFormats() {
        const canvas = document.createElement('canvas');
        const formats = [];

        // 检测WebP支持
        if (canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0) {
            formats.push('image/webp');
        }

        // 检测JPEG支持
        if (canvas.toDataURL('image/jpeg').indexOf('data:image/jpeg') === 0) {
            formats.push('image/jpeg');
        }

        // 检测PNG支持
        if (canvas.toDataURL('image/png').indexOf('data:image/png') === 0) {
            formats.push('image/png');
        }

        return formats;
    },

    /**
     * 获取最佳输出格式
     */
    getBestOutputFormat(inputFormat) {
        const supported = this.getSupportedFormats();

        // 如果支持WebP且输入不是PNG（保持透明度），优先使用WebP
        if (supported.includes('image/webp') && inputFormat !== 'image/png') {
            return 'image/webp';
        }

        // 否则使用JPEG
        if (supported.includes('image/jpeg')) {
            return 'image/jpeg';
        }

        // 最后使用PNG
        return 'image/png';
    }
};

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ImageCompressor;
} else if (typeof window !== 'undefined') {
    window.ImageCompressor = ImageCompressor;
}
