/* ====================
   1. 页面布局与导航
===================== */

/* 面包屑导航样式 */
.breadcrumb-container { margin: 0px 0 10px 0; padding: 10px; border: none; background-color: #fff; width: 100%; box-sizing: border-box; margin-top: 10px; }
.breadcrumb { font-size: 14px; color: #666; display: flex; align-items: center; flex-wrap: wrap; }
.breadcrumb a { color: #666; text-decoration: none; transition: color 0.3s; }
.breadcrumb a:hover { color: #EE3131; text-decoration: none; }
.breadcrumb .separator { margin: 0 8px; color: #ccc; }
.breadcrumb .current { color: #EE3131; }

/* 主内容区域布局 */
.yui-info { display: flex; justify-content: space-between; }
.yui-info-left { width: 890px; min-height: 400px; float: left; position: relative; }
.yui-info-right { width: 260px; min-height: 600px; float: right; }

/* ====================
   2. 信息展示模块
===================== */

/* 信息标题 */
.yui-info-title h1 { font-size: 24px; color: #333; line-height: 50px; }
.yui-info-qt { border-bottom: 1px #ebebeb solid; height: 40px; color: #888; }
.yui-info-qt p { line-height: 35px; }
.yui-info-qt p span { margin-right: 20px; }
.yui-info-qt a { margin-left: 10px; font-size: 14px; transition: all 0.3s ease; }
.yui-info-qt a:hover { opacity: 0.8; text-decoration: none; }

/* 管理链接样式 */
.yui-info-qt .manage-links a {
    padding: 2px 8px;
    border-radius: 3px;
    margin-left: 8px;
}
.yui-info-qt .manage-links a:hover {
    text-decoration: none;
    opacity: 0.8;
}

/* 信息内容 */
.yui-content-wrap { line-height: 30px; padding: 10px 10px 10px 0; font-size: 16px; word-break: break-all; margin-bottom: 10px; }
.yui-content-wrap video { max-width: 890px; }
.yui-content-wrap img, .yui-content-wrap p img { max-width: 500px; margin-bottom: 10px; }

/* 使用须知样式 */
.infoquote { margin-bottom: 25px; }
.infoquote p { line-height: 1.6; }
.infoquote .t { font-weight: bold; }

/* 标签样式 */
.info-tags { display: flex; flex-wrap: wrap; gap: 8px; margin: 10px 0 15px; }
.info-tag { display: inline-flex; align-items: center; padding: 4px 10px; background-color: #e1f5fe; color: #0288d1; border-radius: 4px; font-size: 12px; line-height: 1.5; }
.info-tag.main-area { background-color: #e8f5e9; color: #2e7d32; }
.info-tag.sub-area { background-color: #f1f8e9; color: #558b2f; margin-left: 8px; position: relative; }
.info-tag.main-category { background-color: #e3f2fd; color: #1565c0; }
.info-tag.sub-category { background-color: #e8eaf6; color: #3949ab; margin-left: 8px; position: relative; }
.info-tag.sub-area:before, .info-tag.sub-category:before { content: ""; position: absolute; left: -10px; top: 50%; transform: translateY(-50%); width: 0; height: 0; border-top: 4px solid transparent; border-bottom: 4px solid transparent; }
.info-tag.sub-area:before { border-left: 4px solid #2e7d32; }
.info-tag.sub-category:before { border-left: 4px solid #1565c0; }

/* 图片画廊样式 - 用于展示信息图片集 */
.image-gallery { margin: 20px 0; width: 100%; }
.gallery-grid { display: flex; flex-wrap: wrap; margin: -10px; }
.gallery-item { width: calc(33.333% - 20px); margin: 10px; border-radius: 4px; overflow: hidden; box-shadow: 0 2px 5px rgba(0,0,0,0.1); position: relative; }
.gallery-item img { width: 100%; height: 200px; object-fit: cover; display: block; transition: transform 0.3s ease; cursor: pointer; }
.gallery-item img:hover { transform: scale(1.05); }

/* 联系信息 */
.contact-box { display: flex; gap: 20px; margin-bottom: 15px; }
.contact-item { font-size: 14px; }
.contact-item strong { color: #333; font-weight: bold; }

/* 信息状态 */
.expired-stamp { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%) rotate(-25deg); z-index: 10; opacity: 0.7; }
.expired-text { color: #e74c3c; }

/* ====================
   3. 右侧边栏
===================== */

/* 右侧模块样式 */
.bbs-hot { background: #fff; margin-bottom: 10px; padding: 0px 10px; }
.yui-img-list img { width: 100%; margin: 10px 0; }
.yui-small-list ul { padding: 5px 0; }
.yui-small-list ul li { line-height: 28px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; }
.yui-small-list ul li a { color: #666; font-size: 14px; }
.yui-small-list ul li a:hover { color: #EE3131; }

/* ====================
   4. 交互元素
===================== */

/* 弹窗样式 */
#yui-popup { position: absolute; top: 50%; left: 50%; transform: translate(-50%,-50%); width: 250px; height: auto; background-color: #FFF; border: 1px solid #ccc; box-shadow: 0 2px 4px rgba(0,0,0,0.2); z-index: 9999; padding: 0 0 10px; }
#yui-popup .title { height: 42px; line-height: 42px; border-bottom: 1px solid #eee; font-size: 14px; color: #333; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; background-color: #F8F8F8; border-radius: 2px 2px 0 0; padding: 0 80px 0 20px; }
#yui-popup .image { text-align: center; }
#yui-popup .image img { width: 250px; height: 230px; }
#yui-popup .button { text-align: right; margin-right: 10px; margin-top: 10px; }
#yui-popup button { background-color: #2e8ded; color: #fff; height: 28px; line-height: 28px; border: 1px solid #dedede; border-radius: 2px; font-weight: 400; cursor: pointer; text-decoration: none; border-color: #4898d5; margin: 0 6px; padding: 0 15px; }
.yui_mask { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); z-index: 9998; display: none; }

/* 灯箱样式 - 用于图片放大预览 */
.lightbox-overlay { position: fixed; top: 0; left: 0; right: 0; bottom: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.8); display: flex; align-items: center; justify-content: center; z-index: 9999; flex-direction: column; cursor: pointer; }
.lightbox-container { position: relative; max-width: 90%; max-height: 90vh; background-color: #fff; border-radius: 6px; box-shadow: 0 0 30px rgba(0, 0, 0, 0.5); overflow: hidden; cursor: default; margin: 0 auto; padding: 10px; transition: all 0.3s ease; opacity: 0; }
.lightbox-content { max-width: 100%; max-height: 70vh; object-fit: contain; display: block; border: none; margin: 0 auto; }
.lightbox-caption { color: #333; font-size: 16px; padding: 10px; text-align: center; width: 100%; background-color: #fff; margin-top: 10px; }
.lightbox-close { position: absolute; top: 5px; right: 10px; color: #333; font-size: 30px; font-weight: bold; cursor: pointer; transition: 0.3s; z-index: 10000; line-height: 30px; width: 30px; height: 30px; text-align: center; border-radius: 50%; }
.lightbox-close:hover { color: #EE3131; background-color: rgba(0, 0, 0, 0.1); }
/* 切换箭头固定在视窗两侧，不随图片大小变化位置 */
.lightbox-prev, .lightbox-next { position: fixed; top: 50%; transform: translateY(-50%); color: #fff; font-size: 28px; font-weight: bold; cursor: pointer; padding: 15px; transition: all 0.3s ease; background-color: rgba(0, 0, 0, 0.6); border-radius: 50%; width: 50px; height: 50px; display: flex; align-items: center; justify-content: center; z-index: 10001; border: 2px solid rgba(255, 255, 255, 0.3); }
.lightbox-prev { left: 30px; }
.lightbox-next { right: 30px; }
.lightbox-prev:hover, .lightbox-next:hover { background-color: rgba(0, 0, 0, 0.8); border-color: rgba(255, 255, 255, 0.6); transform: translateY(-50%) scale(1.1); }
/* 只有一张图片时隐藏切换箭头 */
.lightbox-overlay.single-image .lightbox-prev,
.lightbox-overlay.single-image .lightbox-next { display: none; }
/* 响应式设计：在小屏幕上调整箭头位置 */
@media (max-width: 768px) {
    .lightbox-prev { left: 15px; width: 45px; height: 45px; font-size: 24px; }
    .lightbox-next { right: 15px; width: 45px; height: 45px; font-size: 24px; }
}

/* 操作提示弹出层样式 */
.modal-overlay { position: fixed; top: 0; left: 0; right: 0; bottom: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.6); display: flex; align-items: center; justify-content: center; z-index: 10000; }
.modal-content { background: #fff; border-radius: 8px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3); max-width: 400px; width: 90%; text-align: center; animation: modalFadeIn 0.3s ease; }
.modal-icon { font-size: 48px; margin: 20px 0 15px; }
.error-icon { color: #ff4757; }
.success-icon { color: #2ed573; }
.modal-title { font-size: 18px; font-weight: bold; margin-bottom: 10px; color: #333; }
.modal-message { font-size: 14px; color: #666; margin-bottom: 20px; padding: 0 20px; line-height: 1.5; }
.modal-buttons { padding: 0 20px 20px; }
.modal-btn { background: #2e8ded; color: #fff; border: none; padding: 10px 30px; border-radius: 4px; cursor: pointer; font-size: 14px; transition: background-color 0.3s; }
.modal-btn:hover { background: #1e7bd7; }
.modal-btn.primary { background: #2e8ded; }

/* 加载提示样式 */
.loading-modal { padding: 30px; }
.loading-spinner { width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #2e8ded; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 15px; }
.loading-text { font-size: 14px; color: #666; }

/* 动画效果 */
@keyframes modalFadeIn { from { opacity: 0; transform: scale(0.8); } to { opacity: 1; transform: scale(1); } }
@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }

/* ====================
   5. 通用样式
===================== */

/* 通用按钮样式 */
button { background: none; border: 0; outline: none; padding: 0; cursor: pointer; }

/* 辅助类 */
.mt10 { margin-top: 10px; }
.pt20 { padding-top: 20px; }
.yui-clear { clear: both; }

/* 弹出层样式 */
.password-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    z-index: 9999;
    align-items: center;
    justify-content: center;
}

.password-modal-content {
    position: relative;
    width: 360px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
    animation: modalFadeIn 0.3s;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    position: relative;
    padding: 20px;
    border-bottom: 1px solid #eee;
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    color: #333;
    font-weight: bold;
}

.close-modal {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 24px;
    color: #999;
    cursor: pointer;
    transition: color 0.3s;
    width: 24px;
    height: 24px;
    line-height: 24px;
    text-align: center;
}

.close-modal:hover {
    color: #666;
}

.modal-body {
    padding: 20px;
}

.password-input-group {
    margin-bottom: 15px;
}

.password-input-group input {
    width: 100%;
    height: 40px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: all 0.3s;
    box-sizing: border-box;
}

.password-input-group input:focus {
    border-color: #0066cc;
    box-shadow: 0 0 0 2px rgba(0, 102, 204, 0.2);
    outline: none;
}

.error-message {
    color: #f44336;
    font-size: 13px;
    margin-top: 8px;
    display: none;
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #eee;
    text-align: right;
    background: #f9f9f9;
    border-radius: 0 0 8px 8px;
}

.modal-footer button {
    display: inline-block;
    padding: 8px 20px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s;
    border: none;
    margin-left: 10px;
}

.btn-cancel {
    background: #f5f5f5;
    color: #666;
    border: 1px solid #ddd;
}

.btn-cancel:hover {
    background: #e8e8e8;
}

.btn-submit {
    background: #0066cc;
    color: #fff;
}

.btn-submit:hover {
    background: #0052a3;
}

/* 管理链接样式 */
.manage-links a {
    margin-left: 15px !important;
    text-decoration: none;
}

.manage-links a:first-child {
    margin-left: 0 !important;
}

.manage-links a:hover {
    opacity: 0.8;
}