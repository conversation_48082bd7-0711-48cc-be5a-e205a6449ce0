<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{if $title}{$title} - {$site_name}{else}{$site_name}{/if}</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; line-height: 1.6; margin: 0; padding: 0; color: #333; background: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background: #2c3e50; color: white; padding: 20px; border-radius: 5px 5px 0 0; margin-bottom: 20px; }
        .header h1 { margin: 0; font-size: 24px; }
        .content { background: white; padding: 20px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .footer { background: #34495e; color: white; padding: 15px; text-align: center; border-radius: 0 0 5px 5px; }
        
        .section { margin-bottom: 30px; border-bottom: 1px solid #eee; padding-bottom: 20px; }
        .section h2 { color: #2c3e50; border-left: 4px solid #3498db; padding-left: 10px; }
        .section h3 { color: #3498db; }
        
        .demo-box { background: #f5f7fa; border: 1px solid #ddd; border-radius: 4px; padding: 15px; margin-bottom: 15px; }
        .code { font-family: Consolas, Monaco, monospace; background: #f0f0f0; padding: 10px; border-radius: 3px; overflow-x: auto; }
        
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        table, th, td { border: 1px solid #ddd; }
        th, td { padding: 10px; text-align: left; }
        th { background-color: #3498db; color: white; }
        
        .card { border: 1px solid #ddd; border-radius: 4px; padding: 15px; margin-bottom: 15px; background: white; }
        .card-header { background: #f5f7fa; padding: 10px; margin: -15px -15px 15px; border-bottom: 1px solid #ddd; font-weight: bold; }
        
        .success { color: #27ae60; }
        .error { color: #e74c3c; }
        .warning { color: #f39c12; }
        .info { color: #3498db; }
        
        .btn { display: inline-block; padding: 8px 15px; background: #3498db; color: white; border-radius: 3px; text-decoration: none; }
        .btn:hover { background: #2980b9; }
        
        .pagination { text-align: center; margin: 20px 0; }
        .pagination a, .pagination span { display: inline-block; padding: 5px 10px; margin: 0 2px; border: 1px solid #ddd; text-decoration: none; }
        .pagination .active { background: #3498db; color: white; border-color: #3498db; }
        
        .tag { display: inline-block; padding: 3px 8px; background: #e0e0e0; border-radius: 3px; margin: 0 5px 5px 0; }
        .tag-primary { background-color: #3498db; color: white; }
        .tag-warning { background-color: #f39c12; color: white; }
        .tag-default { background-color: #e0e0e0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{$title}</h1>
            <p>高级模板引擎功能演示 - {$current_time|date_format:'Y-m-d H:i:s'}</p>
        </div>
        
        <div class="content">
            <!-- 1. 基本变量和条件判断 -->
            <div class="section">
                <h2>1. 基本变量和条件判断</h2>
                
                <div class="demo-box">
                    <h3>变量输出</h3>
                    <table>
                        <tr><th>变量名</th><th>值</th><th>模板代码</th></tr>
                        <tr><td>title</td><td>{$title}</td><td class="code">&#123;$title&#125;</td></tr>
                        <tr><td>site_name</td><td>{$site_name}</td><td class="code">&#123;$site_name&#125;</td></tr>
                        <tr><td>用户名</td><td>{$user.name}</td><td class="code">&#123;$user.name&#125;</td></tr>
                        <tr><td>价格</td><td>{$price}</td><td class="code">&#123;$price&#125;</td></tr>
                        <tr><td>价格（格式化）</td><td>{$price|money}</td><td class="code">&#123;$price|money&#125;</td></tr>
                    </table>
                </div>
                
                <div class="demo-box">
                    <h3>条件判断</h3>
                    <div class="card">
                        <div class="card-header">简单条件判断</div>
                        <div class="code">
                            &#123;if $is_vip&#125;会员用户&#123;else&#125;普通用户&#123;/if&#125;
                        </div>
                        <p>结果: {if $is_vip}<span class="success">会员用户</span>{else}<span class="error">普通用户</span>{/if}</p>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">复杂条件判断</div>
                        <div class="code">
                            &#123;if $user.age > 18 && $user.verified&#125;成年已认证用户&#123;elseif $user.age > 18&#125;成年未认证用户&#123;else&#125;未成年用户&#123;/if&#125;
                        </div>
                        <p>结果: 
                            {if $user.age > 18 && $user.verified}
                                <span class="success">成年已认证用户</span>
                            {elseif $user.age > 18}
                                <span class="warning">成年未认证用户</span>
                            {else}
                                <span class="error">未成年用户</span>
                            {/if}
                        </p>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">判断数组中的值</div>
                        <div class="code">
                            &#123;if $user.permissions.0 == 'admin' || $user.role == 'admin'&#125;管理员权限&#123;else&#125;普通权限&#123;/if&#125;
                        </div>
                        <p>结果: 
                            {if $user.permissions.0 == 'admin' || $user.role == 'admin'}
                                <span class="success">管理员权限</span>
                            {else}
                                <span class="error">普通权限</span>
                            {/if}
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- 2. 循环语句 -->
            <div class="section">
                <h2>2. 循环语句</h2>
                
                <div class="demo-box">
                    <h3>普通循环</h3>
                    <div class="code">
                        &#123;loop $posts $post&#125;
    <div>文章: &#123;$post.title&#125;</div>
&#123;/loop&#125;
                    </div>
                    <div class="card">
                        {loop $posts $post}
                        <div style="margin-bottom: 5px;">文章: {$post.title} - {$post.created_at|date_format:'Y-m-d'}</div>
                        {/loop}
                    </div>
                </div>
                
                <div class="demo-box">
                    <h3>带键名的循环</h3>
                    <div class="code">
                        &#123;loop $user.profile $key $value&#125;
    <div>&#123;$key&#125;: &#123;$value&#125;</div>
&#123;/loop&#125;
                    </div>
                    <table>
                        <tr><th>键名</th><th>值</th></tr>
                        {loop $user.profile $key $value}
                        <tr><td>{$key}</td><td>{$value}</td></tr>
                        {/loop}
                    </table>
                </div>
                
                <div class="demo-box">
                    <h3>嵌套循环</h3>
                    <div class="code">
                        &#123;loop $categories $category&#125;
    <div>分类: &#123;$category.name&#125;</div>
    &#123;loop $category.items $item&#125;
        <div>- 商品: &#123;$item.name&#125;</div>
    &#123;/loop&#125;
&#123;/loop&#125;
                    </div>
                    <div class="card">
                        {loop $categories $category}
                        <div style="margin-bottom: 10px;">
                            <strong>分类: {$category.name}</strong>
                            {loop $category.items $item}
                            <div style="margin-left: 20px;">- 商品: {$item.name} ({$item.price|money})</div>
                            {/loop}
                        </div>
                        {/loop}
                    </div>
                </div>
                
                <div class="demo-box">
                    <h3>For循环</h3>
                    <div class="code">
                        &#123;for $i=1; $i<=5; $i++&#125;
    <span>&#123;$i&#125;</span>
&#123;/for&#125;
                    </div>
                    <div class="card">
                        {for $i=1; $i<=5; $i++}
                        <span class="tag">{$i}</span>
                        {/for}
                    </div>
                </div>
            </div>
            
            <!-- 3. 修饰符（过滤器） -->
            <div class="section">
                <h2>3. 修饰符（过滤器）</h2>
                
                <div class="demo-box">
                    <table>
                        <tr><th>描述</th><th>模板代码</th><th>结果</th></tr>
                        <tr>
                            <td>日期格式化</td>
                            <td class="code">&#123;$current_time|date_format:'Y-m-d H:i:s'&#125;</td>
                            <td>{$current_time|date_format:'Y-m-d H:i:s'}</td>
                        </tr>
                        <tr>
                            <td>友好时间</td>
                            <td class="code">&#123;$created_at|format_time&#125;</td>
                            <td>{$created_at|format_time}</td>
                        </tr>
                        <tr>
                            <td>数字格式化</td>
                            <td class="code">&#123;$number|number_format:2&#125;</td>
                            <td>{$number|number_format:2}</td>
                        </tr>
                        <tr>
                            <td>货币格式</td>
                            <td class="code">&#123;$price|money&#125;</td>
                            <td>{$price|money}</td>
                        </tr>
                        <tr>
                            <td>文本截取</td>
                            <td class="code">&#123;$long_text|truncate,30,'...'&#125;</td>
                            <td>{$long_text|truncate,30,'...'}</td>
                        </tr>
                        <tr>
                            <td>HTML转义</td>
                            <td class="code">&#123;$html_content|escape&#125;</td>
                            <td>{$html_content|escape}</td>
                        </tr>
                        <tr>
                            <td>文件大小</td>
                            <td class="code">&#123;$file_size|filesize&#125;</td>
                            <td>{$file_size|filesize}</td>
                        </tr>
                    </table>
                </div>
            </div>
            
            <!-- 4. 包含文件 -->
            <div class="section">
                <h2>4. 包含文件</h2>
                
                <div class="demo-box">
                    <h3>包含其他模板</h3>
                    <div class="code">
                        &#123;include file="common/sidebar.htm"&#125;
                    </div>
                    <div class="card">
                        <p>包含文件示例：</p>
                        {include file="footer.htm"}
                    </div>
                </div>
            </div>
            
            <!-- 5. 高级应用 -->
            <div class="section">
                <h2>5. 高级应用</h2>
                
                <div class="demo-box">
                    <h3>分页控件</h3>
                    <div class="code">
                        &#123;if $page > 1&#125;
    <a href="?page=&#123;$page-1&#125;">上一页</a>
&#123;/if&#125;

&#123;for $i=1; $i<=$total_pages; $i++&#125;
    &#123;if $i == $page&#125;
        <span class="active">&#123;$i&#125;</span>
    &#123;else&#125;
        <a href="?page=&#123;$i&#125;">&#123;$i&#125;</a>
    &#123;/if&#125;
&#123;/for&#125;

&#123;if $page < $total_pages&#125;
    <a href="?page=&#123;$page+1&#125;">下一页</a>
&#123;/if&#125;
                    </div>
                    <div class="pagination">
                        {if $page > 1}
                        <a href="?page={$page-1}">上一页</a>
                        {/if}
                        
                        {for $i=1; $i<=$total_pages; $i++}
                            {if $i == $page}
                            <span class="active">{$i}</span>
                            {else}
                            <a href="?page={$i}">{$i}</a>
                            {/if}
                        {/for}
                        
                        {if $page < $total_pages}
                        <a href="?page={$page+1}">下一页</a>
                        {/if}
                    </div>
                </div>
                
                <div class="demo-box">
                    <h3>标签云</h3>
                    <div class="code">
                        &#123;loop $tags $tag&#125;
    <span class="tag">&#123;$tag.name&#125; (&#123;$tag.count&#125;)</span>
&#123;/loop&#125;
                    </div>
                    <div class="card">
                        {loop $tags $tag}
                        <span class="tag 
                              {if $tag.count > 20}tag-primary{elseif $tag.count > 10}tag-warning{else}tag-default{/if}">
                            {$tag.name} ({$tag.count})
                        </span>
                        {/loop}
                    </div>
                </div>
                
                <div class="demo-box">
                    <h3>商品列表</h3>
                    <div class="code">
                        &#123;loop $products $product&#125;
    <div class="product">
        <h3>&#123;$product.name&#125;</h3>
        <p>价格: &#123;$product.price|money&#125;</p>
        &#123;if $product.stock > 0&#125;
            <span class="in-stock">有货</span>
        &#123;else&#125;
            <span class="out-stock">缺货</span>
        &#123;/if&#125;
    </div>
&#123;/loop&#125;
                    </div>
                    <div style="display: flex; flex-wrap: wrap; gap: 15px;">
                        {loop $products $product}
                        <div style="border: 1px solid #ddd; padding: 15px; border-radius: 4px; width: calc(33.333% - 15px); box-sizing: border-box;">
                            <h3>{$product.name}</h3>
                            <p>价格: {$product.price|money}</p>
                            <p>描述: {$product.description}</p>
                            {if $product.stock > 0}
                                <span class="success">有货 ({$product.stock}件)</span>
                            {else}
                                <span class="error">缺货</span>
                            {/if}
                            <p><a href="#" class="btn">查看详情</a></p>
                        </div>
                        {/loop}
                    </div>
                </div>
            </div>
            
            <!-- 6. 系统信息 -->
            <div class="section">
                <h2>6. 系统信息</h2>
                
                <div class="demo-box">
                    <table>
                        <tr><th>名称</th><th>值</th></tr>
                        <tr><td>当前年份</td><td>{year}</td></tr>
                        <tr><td>PHP版本</td><td>{$system_info.php_version}</td></tr>
                        <tr><td>MySQL版本</td><td>{$system_info.mysql_version}</td></tr>
                        <tr><td>服务器软件</td><td>{$system_info.server}</td></tr>
                        <tr><td>最大上传大小</td><td>{$system_info.upload_max_size}</td></tr>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>&copy; {year} {$site_name} - 版权所有</p>
            <p>模板引擎版本: 1.0.0</p>
        </div>
    </div>
</body>
</html> 