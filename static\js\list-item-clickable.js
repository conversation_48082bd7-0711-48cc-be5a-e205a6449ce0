/**
 * 列表项整行点击功能
 */
function initPostItemsClickable() {
    // 先清除所有列表项元素的所有点击事件
    document.querySelectorAll('.post-item').forEach(function(item) {
        const oldItem = item.cloneNode(true);
        item.parentNode.replaceChild(oldItem, item);
    });
    
    // 重新添加点击事件
    document.querySelectorAll('.post-item').forEach(function(item) {
        // 找到这个列表项中的链接
        const link = item.querySelector('a.post-title');
        if (link) {
            const url = link.getAttribute('href');
            
            // 设置整个列表项的点击事件
            item.style.cursor = 'pointer';
            
            // 使用冒泡事件委托模式处理点击事件
            item.addEventListener('click', function(e) {
                // 检查目标元素是否为链接
                if (e.target.tagName === 'A' || e.target.closest('a.post-title')) {
                    return;
                }
                
                // 阻止事件冒泡
                e.stopPropagation();
                
                // 跳转到链接地址
                window.location.href = url;
            });
        }
    });
}

document.addEventListener('DOMContentLoaded', function() {
    // 初始化图文列表项点击事件
    initPostItemsClickable();
    
    // 初始化文本列表点击事件
    initSimpleItemsClickable();
    
    // 监听视图切换事件，重新初始化点击事件
    window.addEventListener('viewSwitched', function(event) {
        console.log('View switched to: ' + event.detail.viewType);
        // 重新初始化所有列表项点击事件
        setTimeout(function() {
            initPostItemsClickable();
            initSimpleItemsClickable();
        }, 10); // 延迟几毫秒，确保视图已切换
    });
});

/**
 * 初始化文本列表项点击事件
 */
function initSimpleItemsClickable() {
    // 先清除所有列表项元素的所有点击事件
    document.querySelectorAll('.simple-item, .list-item').forEach(function(item) {
        const oldItem = item.cloneNode(true);
        item.parentNode.replaceChild(oldItem, item);
    });

    // 重新添加点击事件
    document.querySelectorAll('.simple-item, .list-item').forEach(function(item) {
        // 找到这个列表项中的链接
        const link = item.querySelector('.simple-title a, .item-title a');
        if (link) {
            const url = link.getAttribute('href');

            // 设置整个列表项的点击事件
            item.style.cursor = 'pointer';

            // 使用冒泡事件委托模式处理点击事件
            item.addEventListener('click', function(e) {
                // 检查目标元素是否为链接
                if (e.target.tagName === 'A' || e.target.closest('.simple-title a') || e.target.closest('.item-title a')) {
                    return;
                }

                // 阻止事件冒泡
                e.stopPropagation();

                // 跳转到链接地址
                window.location.href = url;
            });
        }
    });
}