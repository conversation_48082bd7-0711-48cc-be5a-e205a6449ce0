{include file="header.htm"}

<script type="text/javascript" src="static/ueditor/ueditor.config.js"></script>
<script type="text/javascript" src="static/ueditor/ueditor.all.min.js"></script>
<!-- 引入UEditor样式表 -->
<link rel="stylesheet" type="text/css" href="static/ueditor/themes/default/_css/ueditor.css">
<!-- 引入UEditor自定义样式 -->
<link rel="stylesheet" type="text/css" href="static/css/ueditor-custom.css">

<!-- 表单样式 -->
<style>
/* 表单样式 - 统一布局 */
.form-group {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
    gap: 20px;
}

.form-group.full-width {
    flex-direction: row; /* 改为行布局，保持与其他表单组一致 */
    align-items: flex-start;
    gap: 20px;
}

.form-group.full-width .form-label {
    text-align: right; /* 保持与其他标签一致的对齐方式 */
    margin-bottom: 8px;
    flex: 0 0 140px; /* 保持与其他标签相同的宽度 */
}

.form-label {
    flex: 0 0 140px; /* 标签宽度 */
    margin: 8px 0 0 0; /* 调整上边距以对齐输入框 */
    font-weight: 600;
    color: #333;
    font-size: 14px;
    text-align: right;
    line-height: 1.5;
}

.form-field {
    flex: 1;
    min-width: 0;
    max-width: 600px; /* 限制输入框最大宽度，为描述留出空间 */
}

.form-description {
    flex: 0 0 200px; /* 描述文本固定宽度 */
    margin: 8px 0 0 0; /* 与输入框顶部对齐 */
    font-size: 12px;
    color: #666;
    line-height: 1.4;
    padding-left: 15px;
}

.form-hint {
    margin-top: 4px;
    font-size: 12px;
    color: #666;
    line-height: 1.4;
}

/* 全宽表单组的描述文本保持在右侧 */
.form-group.full-width .form-description {
    flex: 0 0 200px; /* 保持固定宽度 */
    margin: 8px 0 0 0; /* 与输入框对齐 */
    padding-left: 15px;
}

.form-group.full-width .form-hint {
    margin: 4px 0 0 0;
    padding-left: 0;
}

.form-control, .form-select {
    display: block;
    width: 100%;
    max-width: 600px; /* 增加最大宽度 */
    padding: 8px 12px; /* 增加内边距 */
    font-size: 14px;
    line-height: 1.5;
    color: #333;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus, .form-select:focus {
    border-color: #1b68ff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(27, 104, 255, 0.25);
}

textarea.form-control {
    min-height: 60px;
    resize: vertical;
}

/* 复选框和单选框样式 */
.checkbox-inline, .radio-inline {
    display: inline-block;
    margin-right: 20px;
    margin-bottom: 0;
    font-weight: normal;
    vertical-align: middle;
    cursor: pointer;
}

.checkbox-inline input[type="checkbox"],
.radio-inline input[type="radio"] {
    margin-right: 6px;
    margin-top: 2px;
}

/* 特别针对编辑器的宽度优化 */
.form-group.full-width .form-field {
    max-width: none !important;
    width: 100% !important;
}

.form-group.full-width {
    width: 100%;
}

/* 确保卡片容器不限制宽度 */
.card-body {
    width: 100%;
}

/* 编辑器容器特殊处理 */
.editor-container {
    width: 100% !important;
    max-width: none !important;
}
</style>

<div class="container-fluid">
    <!-- 消息提示 -->
    {if isset($error) && $error}
    <div class="alert alert-danger">
        {$error}
        <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {/if}
    
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div>
                <h3 class="card-title">添加新闻文章</h3>
            </div>
        </div>
        
        <div class="card-body card-body-narrow">
            <form action="news.php?op=add" method="post">
                <div class="form-group">
                    <label class="form-label">文章栏目：</label>
                    <div class="form-field">
                        <select name="catid" class="form-control" required>
                            <option value="">请选择栏目</option>
                            {if isset($cat_options)}
                            {foreach from=$cat_options item=cat}
                                {if isset($cat.disabled) && $cat.disabled}
                                <optgroup label="{$cat.catname}">
                                {elseif isset($cat.level) && $cat.level == 1}
                                <option value="{$cat.catid}" {if isset($post_data.catid) && $post_data.catid == $cat.catid}selected{/if}>{$cat.catname}</option>
                                {else}
                                <option value="{$cat.catid}" {if isset($post_data.catid) && $post_data.catid == $cat.catid}selected{/if}>|— {$cat.catname}</option>
                                {if isset($cat.is_last) && $cat.is_last}
                                </optgroup>
                                {/if}
                                {/if}
                            {/foreach}
                            {/if}
                        </select>
                    </div>
                    <div class="form-description">如果栏目有二级分类，必须选择二级栏目；如果没有二级分类，可以选择一级栏目</div>
                </div>

                <div class="form-group">
                    <label class="form-label">文章标题：</label>
                    <div class="form-field">
                        <input type="text" name="title" class="form-control" value="{if isset($post_data.title)}{$post_data.title}{/if}" required>
                    </div>
                    <div class="form-description">必填，文章的标题</div>
                </div>

                <div class="form-group">
                    <label class="form-label">作者：</label>
                    <div class="form-field">
                        <input type="text" name="author" class="form-control" value="{if isset($post_data.author)}{$post_data.author}{/if}">
                    </div>
                    <div class="form-description">选填，文章的作者</div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">摘要：</label>
                    <div class="form-field">
                        <textarea name="description" class="form-control" style="height: 80px; max-height: 80px; min-height: 80px; resize: none;">{if isset($post_data.description)}{$post_data.description}{/if}</textarea>
                    </div>
                    <div class="form-description">选填，文章的简短描述，不填将自动提取正文前200个字符</div>
                </div>

                <div class="form-group">
                    <label class="form-label">关键词：</label>
                    <div class="form-field">
                        <input type="text" name="keywords" class="form-control" value="{if isset($post_data.keywords)}{$post_data.keywords}{/if}">
                    </div>
                    <div class="form-description">选填，用于SEO优化，多个关键词用英文逗号分隔</div>
                </div>

                <div class="form-group">
                    <label class="form-label">缩略图：</label>
                    <div class="form-field">
                        <input type="text" name="thumb" class="form-control" value="{if isset($post_data.thumb)}{$post_data.thumb}{/if}">
                    </div>
                    <div class="form-description">选填，文章缩略图URL，建议尺寸300x200</div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">推荐/置顶：</label>
                    <div class="form-field">
                        <div>
                            <label class="checkbox-inline">
                                <input type="checkbox" name="is_recommend" value="1" {if isset($post_data.is_recommend) && $post_data.is_recommend == 1}checked{/if}> 推荐
                            </label>
                            <label class="checkbox-inline">
                                <input type="checkbox" name="is_top" value="1" {if isset($post_data.is_top) && $post_data.is_top == 1}checked{/if}> 置顶
                            </label>
                        </div>
                        <div class="form-hint">选择文章是否推荐或置顶显示</div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">是否显示：</label>
                    <div class="form-field">
                        <div>
                            <label class="radio-inline">
                                <input type="radio" name="is_show" value="1" {if !isset($post_data.is_show) || $post_data.is_show == 1}checked{/if}> 显示
                            </label>
                            <label class="radio-inline">
                                <input type="radio" name="is_show" value="0" {if isset($post_data.is_show) && $post_data.is_show == 0}checked{/if}> 隐藏
                            </label>
                        </div>
                        <div class="form-hint">选择文章在前台是否显示</div>
                    </div>
                </div>
                
                <div class="form-group full-width">
                    <label class="form-label">文章内容</label>
                    <div class="form-field">
                        <!-- 使用UEditor替换原始的textarea -->
                        <div class="editor-container">
                            <script id="editor" name="content" type="text/plain">{if isset($post_data) && isset($post_data.content)}{$post_data.content}{/if}</script>
                        </div>
                    </div>
                    <div class="form-description">文章的详细内容，支持富文本编辑</div>
                </div>
                
                <div class="form-group form-btn-group">
                    <div class="form-field">
                        <button type="submit" class="btn btn-primary">发布文章</button>
                        <button type="button" class="btn btn-secondary" onclick="history.back();">返回</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- UEditor初始化脚本 -->
<script type="text/javascript">
    // 页面加载完成后初始化UEditor
    document.addEventListener('DOMContentLoaded', function() {
        var ue = UE.getEditor('editor', {
            initialFrameWidth: '100%',  // 设置编辑器宽度为100%
            initialFrameHeight: 500,    // 增加编辑器高度到500px
            toolbars: [
                ['fullscreen', 'source', '|', 'undo', 'redo', '|',
                'bold', 'italic', 'underline', 'fontborder', 'strikethrough', 'superscript', 'subscript', 'removeformat', 'formatmatch', 'autotypeset', 'blockquote', 'pasteplain', '|', 'forecolor', 'backcolor', 'insertorderedlist', 'insertunorderedlist', 'selectall', 'cleardoc', '|',
                'rowspacingtop', 'rowspacingbottom', 'lineheight', '|',
                'customstyle', 'paragraph', 'fontfamily', 'fontsize', '|',
                'directionalityltr', 'directionalityrtl', 'indent', '|',
                'justifyleft', 'justifycenter', 'justifyright', 'justifyjustify', '|',
                'link', 'unlink', '|', 'imagenone', 'imageleft', 'imageright', 'imagecenter', '|',
                'simpleupload', 'insertimage', 'emotion', 'insertvideo', '|',
                'horizontal', 'date', 'time', 'spechars', '|',
                'inserttable', 'deletetable', 'insertparagraphbeforetable', 'insertrow', 'deleterow', 'insertcol', 'deletecol', 'mergecells', 'mergeright', 'mergedown', 'splittocells', 'splittorows', 'splittocols', '|',
                'preview']
            ],
            enableAutoSave: true,      // 启用自动保存
            saveInterval: 60000,        // 自动保存间隔，单位为毫秒
            zIndex: 9999,              // 编辑器层级
            autoHeightEnabled: false,  // 是否自动高度
            // 图片相关配置
            imageScaleEnabled: true,   // 启用图片拉伸缩放
            imagePopup: true,          // 启用图片操作浮层
            // 自定义CSS样式注入到编辑器内容区域
            iframeCssUrl: '/admin/static/css/ueditor-content.css',
            // 图片插入时的默认样式
            imageInsertAlign: 'center',  // 图片插入对齐方式：none, left, right, center
            // 粘贴图片时自动上传
            enablePasteUpload: true,
            // 拖拽上传
            enableDragUpload: true
        });

        // 编辑器加载完成后的回调
        ue.ready(function() {
            console.log('UEditor 编辑器加载完成');

            // 注入图片响应式样式到编辑器内容区域
            var editorDoc = ue.getDocument();
            if (editorDoc) {
                var style = editorDoc.createElement('style');
                style.type = 'text/css';
                style.innerHTML = `
                    img {
                        max-width: 100% !important;
                        height: auto !important;
                        display: block;
                        margin: 10px auto;
                        border-radius: 4px;
                        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                        cursor: pointer;
                        transition: all 0.3s ease;
                    }
                    img:hover {
                        box-shadow: 0 4px 16px rgba(0,0,0,0.15);
                        transform: scale(1.02);
                    }
                    img[align="left"] {
                        float: left !important;
                        margin: 10px 15px 10px 0 !important;
                        display: inline !important;
                    }
                    img[align="right"] {
                        float: right !important;
                        margin: 10px 0 10px 15px !important;
                        display: inline !important;
                    }
                    img[align="center"] {
                        display: block !important;
                        margin: 15px auto !important;
                        float: none !important;
                    }
                    p:after {
                        content: "";
                        display: table;
                        clear: both;
                    }
                `;
                editorDoc.head.appendChild(style);
            }

            // 监听图片插入事件，确保新插入的图片应用响应式样式
            ue.addListener('afterInsertImage', function(type, imgObjs) {
                if (imgObjs && imgObjs.length > 0) {
                    for (var i = 0; i < imgObjs.length; i++) {
                        var img = imgObjs[i];
                        if (img && img.style) {
                            // 移除固定宽高，应用响应式样式
                            img.style.maxWidth = '100%';
                            img.style.height = 'auto';
                            img.style.display = 'block';
                            img.style.margin = '10px auto';
                            img.style.borderRadius = '4px';
                            img.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
                            img.style.cursor = 'pointer';
                            img.style.transition = 'all 0.3s ease';

                            // 移除可能存在的固定尺寸属性
                            img.removeAttribute('width');
                            img.removeAttribute('height');
                        }
                    }
                }
            });

            // 强制设置编辑器宽度
            var editorContainer = ue.container;
            if (editorContainer) {
                editorContainer.style.width = '100%';
                editorContainer.style.maxWidth = 'none';

                // 设置编辑器各个组件的宽度
                var toolbarBox = editorContainer.querySelector('.edui-editor-toolbarbox');
                var iframeHolder = editorContainer.querySelector('.edui-editor-iframeholder');
                var bottomContainer = editorContainer.querySelector('.edui-editor-bottomContainer');

                if (toolbarBox) {
                    toolbarBox.style.width = '100%';
                    toolbarBox.style.maxWidth = 'none';
                }
                if (iframeHolder) {
                    iframeHolder.style.width = '100%';
                    iframeHolder.style.maxWidth = 'none';
                }
                if (bottomContainer) {
                    bottomContainer.style.width = '100%';
                    bottomContainer.style.maxWidth = 'none';
                }

                console.log('编辑器宽度已强制设置为100%');
                console.log('编辑器容器宽度:', editorContainer.offsetWidth + 'px');
            }
        });
    });
</script>

{include file="footer.htm"} 