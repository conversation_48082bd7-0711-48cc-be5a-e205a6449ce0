<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>泊头生活网 - 信息置顶</title>
    <meta name="keywords" content="泊头生活网,泊头信息网,泊头信息港,泊头生活信息网站" />
    <meta name="description" content="泊头生活网(泊头信息网)，河北泊头生活信息网站。" />
    <link rel="stylesheet" href="/template/pc/css/common.css">
    <link rel="stylesheet" href="/static/font-awesome/css/all.min.css">
    <script type="text/javascript" src="/template/pc/js/m.js"></script>
    <script src="/template/pc/js/jquery.min.js"></script>
    <style>
        /* 置顶页面特定样式 */
        .posting-notice {
            background-color: #f9f9f9;
            border: 1px solid #e5e5e5;
            border-radius: 4px;
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .notice-title {
            padding: 12px 15px;
            background-color: #f3f3f3;
            cursor: pointer;
            margin: 0;
            font-size: 16px;
            display: flex;
            align-items: center;
        }
        
        .notice-title i {
            color: #ff6600;
            margin-right: 10px;
        }
        
        .notice-content {
            padding: 15px;
            font-size: 14px;
            line-height: 1.8;
        }
        
        .notice-content.collapsed {
            display: none;
        }
        
        .notice-content p {
            margin: 5px 0;
        }
        
        /* 通栏布局 */
        .yui-content {
            padding: 10px 0;
        }
        
        .content-wrap {
            width: 100%;
            margin: 0;
        }
        
        .left-column {
            width: 100%;
            padding: 0;
        }
        
        .form-panel {
            background-color: #fff;
            border: 1px solid #e5e5e5;
            border-radius: 4px;
            padding: 20px;
            width: 100%;
            margin: 0;
            box-sizing: border-box;
        }
        
        .category-row {
            margin-bottom: 20px;
            display: flex;
        }
        
        .form-label {
            width: 150px;
            flex-shrink: 0;
            line-height: 38px;
            text-align: right;
            padding-right: 20px;
            font-weight: bold;
            font-size: 15px;
        }
        
        .category-content {
            flex-grow: 1;
            line-height: 38px;
        }
        
        .category-content a {
            color: #3366cc;
            text-decoration: none;
        }
        
        .category-content a:hover {
            text-decoration: underline;
        }
        
        .form-group {
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        
        .form-group.required .form-label:after {
            content: "*";
            color: #f44336;
            margin-left: 4px;
        }
        
        .form-right {
            flex: 0 0 450px;
            width: 450px;
        }
        
        .form-input {
            width: 100%;
            height: 38px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 0 10px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        .form-input:focus {
            border-color: #3399ff;
            outline: none;
        }
        
        .inputTip {
            display: inline-block;
            margin-left: 15px;
            font-size: 12px;
            color: #888;
            white-space: nowrap;
        }
        
        .inputTip.error {
            color: #f44336;
        }
        
        .inputTip.success {
            color: #4caf50;
        }
        
        .inputTip.focus {
            color: #3399ff;
        }
        
        /* 美化单选按钮组 */
        .radio-group {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .radio-label {
            display: inline-block;
            position: relative;
            padding: 8px 16px;
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
            color: #333;
            user-select: none;
        }
        
        .radio-label:hover {
            background-color: #e9e9e9;
            border-color: #ccc;
        }
        
        .radio-label input[type="radio"] {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        /* 单选框选中状态 */
        .radio-label input[type="radio"]:checked + span {
            color: #fff;
        }
        
        .radio-label input[type="radio"]:checked ~ span {
            color: #fff;
        }
        
        .radio-label input[type="radio"]:checked {
            color: white;
        }
        
        /* 只有在JavaScript支持:has选择器的浏览器才会应用此样式 */
        .radio-label:has(input[type="radio"]:checked) {
            background-color: #ff6600;
            color: white;
            border-color: #ff6600;
        }
        
        .submit-group {
            text-align: center;
            margin-top: 30px;
        }
        
        .submit-button {
            min-width: 180px;
            height: 44px;
            background-color: #ff6600;
            color: #fff;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .submit-button:hover {
            background-color: #ff8533;
        }
        
        /* 自定义下拉框样式 */
        .custom-select {
            position: relative;
            width: 100%;
        }
        
        .select-trigger {
            height: 38px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 0 10px;
            line-height: 38px;
            cursor: pointer;
            background-color: #fff;
            position: relative;
            transition: border-color 0.3s;
        }
        
        .select-trigger:after {
            content: "\f107";
            font-family: "Font Awesome 5 Free";
            font-weight: 900;
            position: absolute;
            right: 10px;
            top: 0;
        }
        
        .custom-select.active .select-trigger {
            border-color: #3399ff;
        }
        
        .custom-select.active .select-trigger:after {
            transform: rotate(180deg);
        }
        
        .select-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            border: 1px solid #ddd;
            border-top: none;
            border-radius: 0 0 4px 4px;
            background-color: #fff;
            z-index: 10;
            display: none;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .custom-select.active .select-dropdown {
            display: block;
        }
        
        .select-option {
            padding: 10px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .select-option:hover {
            background-color: #f5f5f5;
        }
        
        .select-option.selected {
            background-color: #f0f0f0;
            font-weight: bold;
        }
        
        /* 加载中遮罩样式 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.8);
            z-index: 9999;
            display: none;
            justify-content: center;
            align-items: center;
        }
        
        .loading-spinner {
            text-align: center;
        }
        
        .loading-spinner i {
            font-size: 40px;
            color: #ff6600;
            margin-bottom: 15px;
        }
        
        .loading-text {
            font-size: 16px;
            color: #333;
        }
    </style>
</head>
<body>
    <!-- 顶部 -->
    {include file="header.htm"}

    <!-- 主内容区域 -->
    <div class="yui-content yui-1200  bg-white">
        <div class="content-wrap">
            <!-- 左侧表单区域 -->
            <div class="left-column">
                <div class="pd10">
                    <div class="yui-h-title">
                        <h3>信息置顶</h3><span></span>
                    </div>
                </div>
                <div class="posting-notice m">
                    <h4 class="notice-title">
                        <i class="fas fa-exclamation-circle"></i>
                        <span>置顶须知</span>
                    </h4>
                    <div class="notice-content">
                        <p>置顶服务可以让您的信息获得更多曝光</p>
                        <p>栏目置顶：信息将显示在对应栏目的顶部位置</p>
                        <p>首页置顶：信息将同时显示在网站首页的顶部位置</p>
                        <p>置顶时间到期后，信息会自动回到正常排序位置</p>
                        <p>请使用发布信息时设置的管理密码进行操作</p>
                    </div>
                </div>
                
                <form id="top-form" action="/top.php?id={$post.id}" method="post" novalidate>
                    <!-- 当前信息展示 -->
                    <div class="form-panel">
                        <div class="category-row">
                            <label class="form-label">当前信息</label>
                            <div class="category-content">
                                <a href="/{$post.category_pinyin}/{$post.id}.html" target="_blank">{$post.title}</a>
                            </div>
                        </div>

                        <!-- 置顶类型 -->
                        <div class="form-group required">
                            <label for="top_type" class="form-label">置顶类型</label>
                            <div class="form-right">
                                <div class="radio-group">
                                    <label class="radio-label">
                                        <input type="radio" name="top_type" value="0" required>
                                        <span>取消置顶</span>
                                    </label>
                                    <label class="radio-label">
                                        <input type="radio" name="top_type" value="1" required>
                                        <span>栏目置顶</span>
                                    </label>
                                    <label class="radio-label">
                                        <input type="radio" name="top_type" value="2" required>
                                        <span>首页置顶</span>
                                    </label>
                                </div>
                                <span class="inputTip" data-default="请选择需要的置顶类型">请选择需要的置顶类型</span>
                            </div>
                        </div>
                        
                        <!-- 置顶天数 -->
                        <div class="form-group required" id="top_days_group" style="display:none;">
                            <label for="top_days" class="form-label">置顶天数</label>
                            <div class="form-right">
                                <div class="radio-group">
                                    {loop $top_days_options $value $text}
                                    <label class="radio-label">
                                        <input type="radio" name="top_days" value="{$value}" required>
                                        <span>{$text}</span>
                                    </label>
                                    {/loop}
                                </div>
                                <span class="inputTip" data-default="请选择信息置顶的天数">请选择信息置顶的天数</span>
                            </div>
                        </div>
                        
                        <!-- 管理密码 -->
                        <div class="form-group required">
                            <label for="password" class="form-label">管理密码</label>
                            <div class="form-right">
                                <input type="password" name="password" id="password" class="form-input" placeholder="请输入管理密码" required>
                                <span class="inputTip" data-default="请输入发布此信息时设置的管理密码">请输入发布此信息时设置的管理密码</span>
                            </div>
                        </div>
                        
                        <!-- 提交按钮 -->
                        <div class="submit-group">
                            <button type="submit" name="submit" class="submit-button" id="submit-btn">确认操作</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 加载中遮罩 -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <div class="loading-text">正在提交中...</div>
        </div>
    </div>

    <!-- 底部 -->
    {include file="footer.htm"}
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery-validate/1.19.3/localization/messages_zh.min.js"></script>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // 初始控制置顶天数表单显示
        const topTypeInputs = document.querySelectorAll('input[name="top_type"]');
        const topDaysGroup = document.getElementById('top_days_group');
        
        // 为所有单选按钮添加选中状态处理
        const radioLabels = document.querySelectorAll('.radio-label');
        
        radioLabels.forEach(label => {
            const input = label.querySelector('input[type="radio"]');
            
            // 初始化选中状态
            if(input.checked) {
                label.style.backgroundColor = '#ff6600';
                label.style.color = 'white';
                label.style.borderColor = '#ff6600';
            }
            
            // 点击事件
            input.addEventListener('change', function() {
                // 重置同组的所有label样式
                document.querySelectorAll(`input[name="${this.name}"]`).forEach(radio => {
                    const parentLabel = radio.closest('.radio-label');
                    parentLabel.style.backgroundColor = '#f5f5f5';
                    parentLabel.style.color = '#333';
                    parentLabel.style.borderColor = '#ddd';
                });
                
                // 设置当前选中的label样式
                if(this.checked) {
                    label.style.backgroundColor = '#ff6600';
                    label.style.color = 'white';
                    label.style.borderColor = '#ff6600';
                    
                    // 特殊处理置顶类型的选择
                    if(this.name === 'top_type') {
                        if(this.value === '0') {
                            // 取消置顶，隐藏天数选择
                            topDaysGroup.style.display = 'none';
                        } else {
                            // 置顶选项，显示天数选择
                            topDaysGroup.style.display = 'flex';
                        }
                    }
                }
            });
        });
        
        // 表单验证
        const form = document.getElementById('top-form');
        const loadingOverlay = document.getElementById('loading-overlay');
        
        // 置顶须知折叠功能
        const noticeTitle = document.querySelector('.notice-title');
        const noticeContent = document.querySelector('.notice-content');
        
        noticeTitle.addEventListener('click', function() {
            noticeContent.classList.toggle('collapsed');
        });
        
        // jQuery表单验证
        $(document).ready(function() {
            // 验证配置
            $("#top-form").validate({
                // 错误信息处理
                errorPlacement: function(error, element) {
                    // 获取对应的inputTip
                    var $tip = element.parent().siblings(".inputTip");
                    
                    // 保存原始提示文本（如果还没保存）
                    if (!$tip.data('originalText')) {
                        $tip.data('originalText', $tip.attr('data-default'));
                    }
                    
                    // 显示错误信息
                    $tip.removeClass('success focus').addClass('error').html('<i class="fas fa-exclamation-circle"></i> <span class="tip-text">' + error.text() + '</span>');
                },
                // 高亮显示错误字段
                highlight: function(element) {
                    var $element = $(element);
                    $element.addClass('error').removeClass('valid');
                },
                // 移除错误高亮
                unhighlight: function(element) {
                    var $element = $(element);
                    var $tip = $element.parent().siblings(".inputTip");
                    
                    // 恢复原始提示文本
                    var originalText = $tip.data('originalText') || $tip.attr('data-default');
                    
                    $element.removeClass('error');
                    if ($element.is(':checked')) {
                        // 成功状态只显示图标，不显示文本
                        $tip.removeClass('error').addClass('success').html('<i class="fas fa-check-circle"></i> <span class="tip-text">' + originalText + '</span>');
                    } else {
                        // 默认状态显示图标和文本
                        $tip.removeClass('error success').html('<i class="fas fa-info-circle"></i> <span class="tip-text">' + originalText + '</span>');
                    }
                },
                // 验证规则
                rules: {
                    top_type: "required",
                    password: "required",
                    top_days: {
                        required: function() {
                            return $('input[name="top_type"]:checked').val() !== '0';
                        }
                    }
                },
                // 错误信息
                messages: {
                    top_type: "请选择置顶类型",
                    password: "请输入管理密码",
                    top_days: "请选择置顶天数"
                }
            });
            
            // 添加焦点状态处理
            $("#top-form input[type='password']").on("focus", function() {
                var $this = $(this);
                var $tip = $this.siblings(".inputTip");
                
                // 如果字段没有错误状态且不是成功状态
                if (!$this.hasClass("error") && !$tip.hasClass("success")) {
                    var originalText = $tip.data('originalText') || $tip.attr('data-default');
                    // 焦点状态显示蓝色图标和提示文字
                    $tip.removeClass('success error').addClass('focus').html('<i class="fas fa-info-circle"></i> <span class="tip-text">' + originalText + '</span>');
                }
            }).on("blur", function() {
                var $this = $(this);
                var $tip = $this.siblings(".inputTip");
                var originalText = $tip.data('originalText') || $tip.attr('data-default');
                
                // 移除焦点状态
                $tip.removeClass('focus');
                
                // 如果字段有值且验证通过，显示成功图标
                if ($this.val() && !$this.hasClass("error")) {
                    $this.addClass("valid");
                    // 成功状态只显示图标，不显示文本
                    $tip.removeClass('error').addClass('success').html('<i class="fas fa-check-circle"></i> <span class="tip-text">' + originalText + '</span>');
                } else if (!$this.val() && !$this.hasClass("error")) {
                    // 如果没有值且没有错误，隐藏提示
                    $tip.removeClass('error success focus');
                }
            });
            
            // 表单提交处理
            $('#top-form').on('submit', function(e) {
                // 表单验证
                if (!$(this).valid()) {
                    return false;
                }
                
                // 显示加载遮罩
                $('#loading-overlay').css('display', 'flex').fadeIn();
                
                // 正常提交表单（不使用AJAX）
                return true;
            });
        });
    });
    </script>
    
    
</body>
</html> 