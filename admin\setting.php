<?php
// 定义安全常量
define('IN_BTMPS', true);
/**
 * 系统设置管理页面
 */
// 引入公共文件
require_once('../include/common.inc.php');

// 检查管理员是否登录
if (!isset($_SESSION['admin']) || $_SESSION['admin']['is_login'] !== true) {
    header("Location: login.php");
    exit;
}

// 当前页面
$current_page = 'setting';

// 如果settings表中没有数据，则添加默认设置
init_settings();

// 处理表单提交
$message = '';
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'save_settings') {
    // 获取设置组
    $group = isset($_POST['group']) ? $_POST['group'] : 'basic';
    
    // 获取所有设置
    $settings = get_settings_by_group($group);
    
    // 更新设置
    foreach ($settings as $setting) {
        $key = $setting['setting_key'];
        if (isset($_POST[$key])) {
            $value = $_POST[$key];
            
            // 特殊处理对网站开启状态的更新
            if ($key == 'site_status') {
                // 确保值为0或1
                $value = $value == '1' ? '1' : '0';
                // 记录日志但不显示额外提示
                $log_message = "管理员更新了网站状态为: " . ($value == '1' ? '开启' : '关闭');
                log_message($log_message, 'admin');
            }
            
            update_setting($key, $value);
        }
    }
    
    // 生成配置文件
    if (generate_config_file()) {
        $message = '设置已成功保存';
    } else {
        $message = '设置已保存到数据库，但更新配置文件失败，请检查文件权限';
    }
}

// 获取当前设置组
$group = isset($_GET['group']) ? $_GET['group'] : 'basic';

// 获取所有设置组
$groups = [
    'basic' => '基本设置',
    'user' => '用户设置',
    'upload' => '上传设置',
    'content' => '信息设置',
    'search' => '搜索设置',
    'cache' => '缓存设置',
    'news_cache' => '新闻缓存'
];

// 获取所有分组，以防设置有其他分组
$sql = "SELECT DISTINCT setting_group FROM settings";
$result = $db->query($sql);
$db_groups = [];
while ($row = $db->fetch_array($result)) {
    if (!empty($row['setting_group']) && !isset($groups[$row['setting_group']])) {
        $db_groups[$row['setting_group']] = $row['setting_group'].'设置';
    }
}
// 合并数据库中的分组和预定义分组
$groups = array_merge($groups, $db_groups);

// 如果当前分组在所有分组中不存在，则使用第一个分组
if (!isset($groups[$group])) {
    $group_keys = array_keys($groups);
    $group = !empty($group_keys) ? $group_keys[0] : 'basic';
}

// 获取当前组的设置
$settings = get_settings_by_group($group);

// 如果是搜索分组且没有设置项，则创建搜索设置
if (empty($settings) && $group === 'search') {
    create_search_settings();
    $settings = get_settings_by_group($group);
}

// 如果是新闻缓存分组且没有设置项，则创建新闻缓存设置
if (empty($settings) && $group === 'news_cache') {
    create_news_cache_settings();
    $settings = get_settings_by_group($group);
}

// 如果当前组没有设置项，且不是搜索分组，则尝试获取所有设置
if (empty($settings) && $group !== 'search') {
    $settings = get_all_settings();
    // 如果真的没有设置数据，创建初始设置
    if (empty($settings)) {
        create_default_settings();
        $settings = get_all_settings();
    }
}

// 调试信息，输出到页面
$debug_info = '';
$debug_info .= "当前分组: " . $group . "<br>";
$debug_info .= "当前分组名称: " . (isset($groups[$group]) ? $groups[$group] : '未找到') . "<br>";
$debug_info .= "可用分组: " . implode(', ', array_keys($groups)) . "<br>";
$debug_info .= "查询语句: SELECT * FROM settings WHERE setting_group = '" . $db->escape($group) . "'<br>";
$debug_info .= "找到设置项数量: " . count($settings) . "<br>";

// 修改模板目录为admin/template目录
$tpl->setTemplateDir(dirname(__FILE__) . '/template/');

// 传递数据到模板
$tpl->assign('current_page', $current_page);
$tpl->assign('breadcrumb', '系统设置');
$tpl->assign('message', $message);
$tpl->assign('settings', $settings);
$tpl->assign('groups', $groups);
$tpl->assign('current_group', $group);
$tpl->assign('page_title', '系统设置');
$tpl->assign('admin', $_SESSION['admin']);
$tpl->assign('debug_info', $debug_info);

// 显示页面
$tpl->display('setting.htm');

/**
 * 创建搜索设置
 */
function create_search_settings() {
    global $db;

    $search_settings = array(
        array('search_interval', '5', 'search', '搜索时间间隔，用户两次搜索之间的最小间隔时间（秒）'),
        array('search_cache_time', '600', 'search', '搜索结果缓存时间（秒），0表示不缓存'),
        array('search_max_results', '2000', 'search', '搜索结果最大数量限制'),
        array('search_keyword_min_length', '2', 'search', '搜索关键词最小长度'),
        array('search_keyword_max_length', '50', 'search', '搜索关键词最大长度'),
        array('search_enable_highlight', '1', 'search', '是否启用搜索结果关键词高亮，1表示启用，0表示禁用')
    );

    foreach ($search_settings as $setting) {
        list($key, $value, $group, $description) = $setting;

        // 检查是否已存在
        $check_sql = "SELECT COUNT(*) as count FROM settings WHERE setting_key = '" . $db->escape($key) . "'";
        $check_result = $db->query($check_sql);
        $check_row = $db->fetch_array($check_result);

        if ($check_row['count'] == 0) {
            $sql = "INSERT INTO settings (setting_key, setting_value, setting_group, description) VALUES (?, ?, ?, ?)";
            $db->query($sql, array($key, $value, $group, $description));
        }
    }
}

/**
 * 根据设置组获取设置
 */
function get_settings_by_group($group) {
    global $db;

    $sql = "SELECT * FROM settings WHERE setting_group = '" . $db->escape($group) . "'";
    $result = $db->query($sql);

    $settings = [];
    while ($row = $db->fetch_array($result)) {
        // 为了兼容模板，添加缺少的字段
        $row['setting_title'] = get_setting_title($row['setting_key']);
        $row['setting_description'] = $row['description'];
        $row['setting_type'] = get_setting_type($row['setting_key']);
        $settings[] = $row;
    }

    return $settings;
}

/**
 * 获取设置的显示标题
 */
function get_setting_title($key) {
    $titles = [
        'site_name' => '网站名称',
        'site_title' => '网站标题',
        'site_keywords' => '网站关键词',
        'site_description' => '网站描述',
        'site_copyright' => '版权信息',
        'site_icp' => '备案号',
        'site_analytics' => '统计代码',
        'site_status' => '网站状态',
        'site_close_reason' => '关闭原因',
        'allow_register' => '允许注册',
        'verify_register' => '注册验证',
        'upload_image_size' => '图片上传大小',
        'upload_image_count' => '图片上传数量',
        'allowed_extensions' => '允许的扩展名',
        'post_expiry_days' => '信息有效期',
        'mobile_daily_post_limit' => '手机号每日发布限制',
        'list_page_size' => '列表分页数量',
        'home_list_count' => '首页列表数量',
        'index_size' => '首页信息数量',
        'hot_posts_count' => '首页热门信息数量',
        'news_index_count' => '首页资讯索引数量',
        'mobile_pagination_mode' => '移动端分页模式',
        'post_review' => '信息审核设置',
        'user_review' => '用户审核设置',
        'comment_review' => '评论审核设置',
        'email_notification' => '邮件通知',
        'sms_notification' => '短信通知',
        'map_api_key' => '地图API密钥',
        'payment_gateway' => '支付网关',
        'fee_structure' => '收费标准',
        'vip_discount' => 'VIP折扣比例',
        'cache_time' => '缓存时间',
        'log_retention' => '日志保留天数',
        'default_lang' => '默认语言',
        'time_zone' => '时区设置',
        'date_format' => '日期格式',
        'time_format' => '时间格式',
        'thumbnail_size' => '缩略图尺寸',
        'image_quality' => '图片质量',
        'watermark' => '水印设置',
        'captcha' => '验证码设置',
        'smtp_server' => 'SMTP服务器',
        'smtp_port' => 'SMTP端口',
        'smtp_user' => 'SMTP用户名',
        'smtp_pass' => 'SMTP密码',
        'mail_from' => '发件人地址',
        'mail_name' => '发件人名称',
        
        // 缓存相关设置
        'cache_category' => '分类页缓存时间(秒)',
        'cache_region' => '地区页缓存时间(秒)',
        'cache_index' => '首页缓存时间(秒)',
        'cache_post' => '信息页缓存时间(秒)',
        'cache_list' => '列表页缓存时间(秒)',
        'cache_search' => '搜索结果缓存时间(秒)',
        'cache_data' => '数据缓存时间(秒)',
        'cache_enable' => '启用缓存功能',
        'cache_compress' => '缓存压缩',
        'clear_cache_time' => '缓存清理周期(小时)',

        // 新闻模块专用缓存设置
        'cache_news_home' => '新闻首页缓存时间(秒)',
        'cache_news_list' => '新闻列表页缓存时间(秒)',
        'cache_news_detail' => '新闻详情页缓存时间(秒)',
        'cache_news_category' => '新闻分类缓存时间(秒)',
        'cache_news_hot' => '热门新闻缓存时间(秒)',

        // 搜索相关设置
        'search_interval' => '搜索时间间隔(秒)',
        'search_cache_time' => '搜索结果缓存时间(秒)',
        'search_max_results' => '搜索结果最大数量',
        'search_keyword_min_length' => '搜索关键词最小长度',
        'search_keyword_max_length' => '搜索关键词最大长度',
        'search_enable_highlight' => '启用搜索结果高亮'
    ];
    
    // 如果没有预定义标题，尝试生成友好的标题，或者使用描述信息
    if (!isset($titles[$key])) {
        global $db;
        
        // 首先尝试从数据库中获取描述信息
        $sql = "SELECT description FROM settings WHERE setting_key = '" . $db->escape($key) . "' LIMIT 1";
        $result = $db->query($sql);
        if ($result && $db->num_rows($result) > 0) {
            $row = $db->fetch_array($result);
            if (!empty($row['description'])) {
                return $row['description'];
            }
        }
        
        // 使用预处理转换规则
        $friendly = str_replace('_', ' ', $key);
        $friendly = ucwords($friendly);
        
        // 特殊单词替换规则
        $replacements = [
            // 常见前缀替换为中文
            'Site ' => '网站',
            'User ' => '用户',
            'Post ' => '信息',
            'Comment ' => '评论',
            'Upload ' => '上传',
            'Email ' => '邮件',
            'Sms ' => '短信',
            'Payment ' => '支付',
            'Cache ' => '缓存',
            'Log ' => '日志',
            'Default ' => '默认',
            'Time ' => '时间',
            'Date ' => '日期',
            'Image ' => '图片',
            
            // 特殊单词替换
            'Category' => '分类',
            'Region' => '地区',
            'Index' => '首页',
            'List' => '列表',
            'Search' => '搜索',
            'Data' => '数据',
            'Enable' => '启用',
            'Compress' => '压缩',
            'Clear' => '清理'
        ];
        
        foreach ($replacements as $en => $cn) {
            $friendly = str_replace($en, $cn, $friendly);
        }
        
        return $friendly;
    }
    
    return $titles[$key];
}

/**
 * 获取设置类型
 */
function get_setting_type($key) {
    $types = [
        'site_description' => 'textarea',
        'site_copyright' => 'textarea',
        'site_status' => 'switch',
        'site_close_reason' => 'textarea',
        'site_analytics' => 'textarea',
        'allow_register' => 'switch',
        'verify_register' => 'switch',
        'mobile_pagination_mode' => 'select',

        // 搜索设置类型
        'search_interval' => 'number',
        'search_cache_time' => 'number',
        'search_max_results' => 'number',
        'search_keyword_min_length' => 'number',
        'search_keyword_max_length' => 'number',
        'search_enable_highlight' => 'switch',
        'mobile_daily_post_limit' => 'number',

        // 缓存设置类型
        'cache_enable' => 'switch',
        'cache_index' => 'number',
        'cache_list' => 'number',
        'cache_post' => 'number',
        'cache_category' => 'number',
        'cache_region' => 'number',
        'cache_search' => 'number',
        'cache_data' => 'number',
        'cache_compress' => 'switch',
        'clear_cache_time' => 'number',

        // 新闻缓存设置类型
        'cache_news_home' => 'number',
        'cache_news_list' => 'number',
        'cache_news_detail' => 'number',
        'cache_news_category' => 'number',
        'cache_news_hot' => 'number',
    ];

    return isset($types[$key]) ? $types[$key] : 'text';
}

/**
 * 更新设置值
 */
function update_setting($key, $value) {
    global $db;
    
    // 对特殊字段进行处理
    if ($key == 'site_status') {
        // 确保site_status只能是0或1
        $value = ($value == '1' || $value === 1) ? '1' : '0';
    } else if (in_array($key, ['list_page_size', 'home_list_count', 'index_size', 'hot_posts_count', 'news_index_count', 'post_expiry_days', 'upload_image_size', 'upload_image_count', 'cache_index', 'cache_list', 'cache_post', 'cache_category', 'cache_region', 'cache_search', 'cache_data', 'clear_cache_time', 'cache_news_home', 'cache_news_list', 'cache_news_detail', 'cache_news_category', 'cache_news_hot'])) {
        // 确保数字类型的设置项值为正整数
        $value = max(1, intval($value));
    }
    
    // 检查设置是否存在
    $sql = "SELECT id FROM settings WHERE setting_key = '" . $db->escape($key) . "' LIMIT 1";
    $result = $db->query($sql);
    
    if ($db->num_rows($result) > 0) {
        // 更新现有设置
        $sql = "UPDATE settings SET setting_value = '" . $db->escape($value) . "' WHERE setting_key = '" . $db->escape($key) . "'";
    } else {
        // 添加新设置，使用默认分组
        $sql = "INSERT INTO settings (setting_key, setting_value, setting_group) VALUES ('" . $db->escape($key) . "', '" . $db->escape($value) . "', 'basic')";
    }
    
    return $db->query($sql);
}

/**
 * 生成配置文件
 */
function generate_config_file() {
    global $db;
    
    // 获取所有设置
    $sql = "SELECT setting_key, setting_value FROM settings";
    $result = $db->query($sql);
    
    $settings = [];
    while ($row = $db->fetch_array($result)) {
        $settings[] = $row;
    }
    
    // 准备配置内容
    $time = date('Y-m-d H:i:s');
    $config_content = "<?php\n/**\n * 自动生成的配置文件\n * 最后更新时间：{$time}\n */\n\n";
    $config_content .= "// 手动配置区域 - 请勿修改此行和此行以上内容\n// 此区域用于手动添加配置，不会被自动生成的配置覆盖\n\n";
    
    // 检查配置文件是否存在
    $config_file = dirname(dirname(__FILE__)) . '/config/config.inc.php';
    
    // 如果存在，则保留手动配置区域
    if (file_exists($config_file)) {
        $original_content = file_get_contents($config_file);
        
        // 提取手动配置部分 - 改进正则表达式以匹配更精确
        if (preg_match('/\/\/ 手动配置区域.*?\/\/ 自动生成的配置/s', $original_content, $matches)) {
            // 提取手动配置部分
            $manual_config = $matches[0];
            // 添加手动配置部分
            $config_content = "<?php\n/**\n * 自动生成的配置文件\n * 最后更新时间：{$time}\n */\n\n";
            $config_content .= "// 手动配置区域 - 请勿修改此行和此行以上内容\n// 此区域用于手动添加配置，不会被自动生成的配置覆盖\n\n";
            
            // 从手动配置中提取有效的PHP配置代码
            if (preg_match_all('/\$config\[\'[^\']+\'\]\s*=\s*[^;]+;/', $original_content, $config_matches, PREG_SET_ORDER)) {
                foreach ($config_matches as $match) {
                    // 提取键名
                    if (preg_match('/\$config\[\'([^\']+)\'\]/', $match[0], $key_match)) {
                        $key = $key_match[1];
                        // 只保留与设置表无关的手动配置项
                        $skip = false;
                        foreach ($settings as $setting) {
                            if ($setting['setting_key'] == $key) {
                                $skip = true;
                                break;
                            }
                        }
                        if (!$skip) {
                            $config_content .= $match[0] . "\n";
                        }
                    }
                }
            }
            
            $config_content .= "\n// 自动生成的配置\n";
        }
    }
    
    // 添加所有设置
    foreach ($settings as $setting) {
        $key = $setting['setting_key'];
        $value = $setting['setting_value'];
        
        // 特殊处理site_status，确保它是数字
        if ($key == 'site_status') {
            // 确保它是整数 0 或 1
            $value = ($value == '1' || $value === 1) ? 1 : 0;
            $config_content .= "\$config['{$key}'] = {$value};\n";
        }
        // 适当格式化其他值
        else if (is_numeric($value) && !in_array($key, ['site_icp', 'site_phone'])) {
            $config_content .= "\$config['{$key}'] = {$value};\n";
        } 
        else {
            $value = str_replace("'", "\'", $value); // 转义单引号
            $config_content .= "\$config['{$key}'] = '{$value}';\n";
        }
    }
    
    // 写入文件
    return file_put_contents($config_file, $config_content) !== false;
}

/**
 * 检查settings表并初始化默认设置
 */
function init_settings() {
    global $db;
    
    // 检查表中是否有数据
    $sql = "SELECT COUNT(*) as count FROM settings";
    $result = $db->query($sql);
    $row = $db->fetch_array($result);
    
    // 如果没有数据，添加默认设置
    if ($row['count'] == 0) {
        create_default_settings();
    }
    
    return true;
}

/**
 * 创建默认设置
 */
function create_default_settings() {
    global $db;
    
    $default_settings = [
        ['site_name', '分类信息网站', 'basic', '网站名称'],
        ['site_title', '分类信息网站 - 免费发布信息平台', 'basic', '网站标题，显示在浏览器标签页'],
        ['site_keywords', '分类信息,免费发布,信息平台', 'basic', '网站关键词，用于搜索引擎优化'],
        ['site_description', '分类信息网站是一个免费发布信息的平台，提供房产、招聘、二手交易等各类信息服务。', 'basic', '网站描述，用于搜索引擎优化'],
        ['site_copyright', 'Copyright © 2024 分类信息网站 All Rights Reserved', 'basic', '网站版权信息'],
        ['site_icp', '冀ICP备12345678号', 'basic', 'ICP备案号'],
        ['site_analytics', '', 'basic', '网站统计代码'],
        ['site_status', '1', 'basic', '网站状态，1表示开启，0表示关闭'],
        ['site_close_reason', '网站维护中，请稍后再访问...', 'basic', '网站关闭原因，网站关闭时显示'],
        ['allow_register', '1', 'user', '是否允许用户注册，1表示允许，0表示禁止'],
        ['verify_register', '0', 'user', '是否需要验证注册，1表示需要，0表示不需要'],
        ['upload_image_size', '2', 'upload', '图片上传大小限制，单位MB'],
        ['upload_image_count', '10', 'upload', '每次最多上传图片数量'],
        ['allowed_extensions', 'jpg,jpeg,png,gif', 'upload', '允许上传的文件扩展名，多个用逗号分隔'],
        ['post_expiry_days', '30', 'content', '信息过期天数，发布后多少天过期'],
        ['mobile_daily_post_limit', '10', 'content', '手机号每日发布限制，每个手机号码每天最多可发布的信息数量'],
        ['list_page_size', '10', 'content', '列表分页大小，每页显示多少条信息'],
        ['index_size', '20', 'content', '首页显示多少条信息'],
        ['hot_posts_count', '8', 'content', '首页热门信息显示数量'],
        ['news_index_count', '10', 'content', '首页资讯索引显示数量'],
        ['mobile_pagination_mode', 'pagination', 'content', '移动端分页模式：pagination=传统分页，loadmore=点击加载更多，infinite=滚动无限加载'],
        ['search_interval', '5', 'search', '搜索时间间隔，用户两次搜索之间的最小间隔时间（秒）'],
        ['search_cache_time', '600', 'search', '搜索结果缓存时间（秒），0表示不缓存'],
        ['search_max_results', '2000', 'search', '搜索结果最大数量限制'],
        ['search_keyword_min_length', '2', 'search', '搜索关键词最小长度'],
        ['search_keyword_max_length', '50', 'search', '搜索关键词最大长度'],
        ['search_enable_highlight', '1', 'search', '是否启用搜索结果关键词高亮，1表示启用，0表示禁用'],

        // 缓存相关设置
        ['cache_enable', '1', 'cache', '是否启用缓存功能，1表示启用，0表示禁用'],
        ['cache_index', '3600', 'cache', '首页缓存时间（秒），0表示不缓存'],
        ['cache_list', '1800', 'cache', '列表页缓存时间（秒），0表示不缓存'],
        ['cache_post', '1800', 'cache', '详情页缓存时间（秒），0表示不缓存'],
        ['cache_category', '0', 'cache', '分类数据缓存时间（秒），0表示永不过期（推荐），只在修改分类时清理'],
        ['cache_region', '0', 'cache', '区域数据缓存时间（秒），0表示永不过期（推荐），只在修改区域时清理'],
        ['cache_search', '600', 'cache', '搜索结果缓存时间（秒），0表示不缓存'],
        ['cache_data', '3600', 'cache', '数据缓存时间（秒），0表示不缓存'],
        ['cache_compress', '0', 'cache', '是否启用缓存压缩，1表示启用，0表示禁用'],
        ['clear_cache_time', '24', 'cache', '缓存清理周期（小时），自动清理过期缓存'],

        // 新闻模块专用缓存设置
        ['cache_news_home', '1800', 'news_cache', '新闻首页缓存时间（秒），0表示不缓存，为空则使用首页缓存时间'],
        ['cache_news_list', '1800', 'news_cache', '新闻列表页缓存时间（秒），0表示不缓存，为空则使用列表页缓存时间'],
        ['cache_news_detail', '86400', 'news_cache', '新闻详情页缓存时间（秒），0表示不缓存，为空则使用详情页缓存时间'],
        ['cache_news_category', '21600', 'news_cache', '新闻分类缓存时间（秒），0表示不缓存，为空则使用分类缓存时间'],
        ['cache_news_hot', '7200', 'news_cache', '热门新闻缓存时间（秒），0表示不缓存']
    ];

    foreach ($default_settings as $setting) {
        $sql = "INSERT INTO settings (setting_key, setting_value, setting_group, description) VALUES (?, ?, ?, ?)";
        $db->query($sql, $setting);
    }
}

/**
 * 获取所有设置
 */
function get_all_settings() {
    global $db;

    $sql = "SELECT * FROM settings ORDER BY id ASC";
    $result = $db->query($sql);

    $settings = [];
    while ($row = $db->fetch_array($result)) {
        // 为了兼容模板，添加缺少的字段
        $row['setting_title'] = get_setting_title($row['setting_key']);
        $row['setting_description'] = $row['description'];
        $row['setting_type'] = get_setting_type($row['setting_key']);
        $settings[] = $row;
    }

    return $settings;
}

/**
 * 创建新闻缓存设置
 */
function create_news_cache_settings() {
    global $db;

    $news_cache_settings = array(
        array('cache_news_home', '1800', 'news_cache', '新闻首页缓存时间（秒），0表示不缓存，为空则使用首页缓存时间'),
        array('cache_news_list', '1800', 'news_cache', '新闻列表页缓存时间（秒），0表示不缓存，为空则使用列表页缓存时间'),
        array('cache_news_detail', '86400', 'news_cache', '新闻详情页缓存时间（秒），0表示不缓存，为空则使用详情页缓存时间'),
        array('cache_news_category', '21600', 'news_cache', '新闻分类缓存时间（秒），0表示不缓存，为空则使用分类缓存时间'),
        array('cache_news_hot', '7200', 'news_cache', '热门新闻缓存时间（秒），0表示不缓存')
    );

    foreach ($news_cache_settings as $setting) {
        list($key, $value, $group, $description) = $setting;

        // 检查是否已存在
        $check_sql = "SELECT COUNT(*) as count FROM settings WHERE setting_key = '" . $db->escape($key) . "'";
        $check_result = $db->query($check_sql);
        $check_row = $db->fetch_array($check_result);

        if ($check_row['count'] == 0) {
            $sql = "INSERT INTO settings (setting_key, setting_value, setting_group, description) VALUES (?, ?, ?, ?)";
            $db->query($sql, array($key, $value, $group, $description));
        }
    }
}