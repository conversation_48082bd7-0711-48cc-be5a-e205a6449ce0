{include file="header.htm"}

<style>
/* 筛选表单样式 - 与新闻分类管理保持一致 */
.filter-form {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 20px;
}

.filter-form-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-form-item label {
    font-weight: 600;
    color: #333;
    white-space: nowrap;
    margin: 0;
    font-size: 14px;
}

.filter-form-item .form-control {
    min-width: 120px;
    height: 32px;
    padding: 4px 8px;
    font-size: 14px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.filter-form-item .form-control:focus {
    border-color: #1b68ff;
    box-shadow: 0 0 0 0.2rem rgba(27, 104, 255, 0.25);
}

.filter-form-item .btn {
    height: 32px;
    padding: 4px 12px;
    font-size: 14px;
    line-height: 1.5;
}

/* 快速筛选标签样式 */
.filter-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 20px;
}

.filter-tag {
    display: inline-block;
    padding: 6px 12px;
    font-size: 13px;
    font-weight: 500;
    text-decoration: none;
    border-radius: 20px;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.filter-tag.active-all {
    background: #1b68ff;
    color: white;
    border-color: #1b68ff;
}

.filter-tag.inactive-all {
    background: #f8f9fa;
    color: #6c757d;
    border-color: #e9ecef;
}

.filter-tag.active-enabled {
    background: #3ad29f;
    color: white;
    border-color: #3ad29f;
}

.filter-tag.inactive-enabled {
    background: #f8f9fa;
    color: #3ad29f;
    border-color: #3ad29f;
}

.filter-tag.active-disabled {
    background: #f82f58;
    color: white;
    border-color: #f82f58;
}

.filter-tag.inactive-disabled {
    background: #f8f9fa;
    color: #f82f58;
    border-color: #f82f58;
}

.filter-tag:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 表格样式 - 与新闻分类管理保持一致 */
.content-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.content-table thead {
    background: #f8f9fa;
}

.content-table th {
    padding: 12px 15px;
    text-align: left;
    font-weight: 600;
    color: #333;
    border-bottom: 2px solid #e9ecef;
    font-size: 14px;
}

.content-table td {
    padding: 12px 15px;
    border-bottom: 1px solid #f0f0f0;
    vertical-align: middle;
    font-size: 14px;
}

.content-table tbody tr:hover {
    background-color: #f8f9fa;
}

.content-table tbody tr:last-child td {
    border-bottom: none;
}

/* 状态标签样式 */
.tag {
    display: inline-block;
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 500;
    border-radius: 12px;
    text-align: center;
    min-width: 50px;
}

.tag-success {
    background: rgba(58, 210, 159, 0.1);
    color: #3ad29f;
    border: 1px solid rgba(58, 210, 159, 0.2);
}

.tag-danger {
    background: rgba(248, 47, 88, 0.1);
    color: #f82f58;
    border: 1px solid rgba(248, 47, 88, 0.2);
}

/* 操作按钮样式 */
.page-actions {
    display: flex;
    flex-wrap: nowrap;
    justify-content: flex-end;
    gap: 4px;
    min-width: 180px;
}

.page-actions .btn {
    padding: 4px 8px;
    font-size: 12px;
    white-space: nowrap;
    text-decoration: none !important;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.btn-light-info {
    color: #17a2b8;
    background-color: rgba(23, 162, 184, 0.1);
    border: 1px solid rgba(23, 162, 184, 0.2);
}

.btn-light-info:hover {
    color: #fff;
    background-color: #17a2b8;
    border-color: #17a2b8;
}

.btn-light-primary {
    color: #1b68ff;
    background-color: rgba(27, 104, 255, 0.1);
    border: 1px solid rgba(27, 104, 255, 0.2);
}

.btn-light-primary:hover {
    color: #fff;
    background-color: #1b68ff;
    border-color: #1b68ff;
}

.btn-light-danger {
    color: #f82f58;
    background-color: rgba(248, 47, 88, 0.1);
    border: 1px solid rgba(248, 47, 88, 0.2);
}

.btn-light-danger:hover {
    color: #fff;
    background-color: #f82f58;
    border-color: #f82f58;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .filter-form {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .filter-form-item {
        justify-content: space-between;
    }

    .page-actions {
        flex-direction: column;
        gap: 3px;
        min-width: auto;
    }

    .content-table {
        font-size: 13px;
    }

    .content-table th,
    .content-table td {
        padding: 8px 10px;
    }
}
</style>

<!-- 页面标题 -->
<div class="page-title">
    <h1>单页管理</h1>
    <div class="d-flex gap-2">
        <a href="pages.php?action=add" class="btn btn-primary">
            <i class="fas fa-plus"></i> 添加单页
        </a>
    </div>
</div>

<!-- 单页管理 -->
<div class="card mb-4">
    <div class="card-body">
        <!-- 快速筛选标签 -->
        <div class="filter-tags">
            <a href="pages.php" class="filter-tag {if $status == -1 && !$keyword}active-all{else}inactive-all{/if}">全部单页</a>
            <a href="pages.php?status=1" class="filter-tag {if $status == 1}active-enabled{else}inactive-enabled{/if}">已启用</a>
            <a href="pages.php?status=0" class="filter-tag {if $status == 0}active-disabled{else}inactive-disabled{/if}">已禁用</a>
        </div>

        <!-- 紧凑筛选表单 -->
        <form method="GET" action="pages.php" class="filter-form">
            <input type="hidden" name="action" value="list">

            <div class="filter-form-item">
                <label>关键词:</label>
                <input type="text" name="keyword" class="form-control" placeholder="标题、路径或URL" value="{$keyword}" style="width: 200px;">
            </div>

            <div class="filter-form-item">
                <label>状态:</label>
                <select name="status" class="form-control" style="width: 100px;">
                    <option value="-1" {if $status == -1}selected{/if}>全部</option>
                    <option value="1" {if $status == 1}selected{/if}>启用</option>
                    <option value="0" {if $status == 0}selected{/if}>禁用</option>
                </select>
            </div>

            <div class="filter-form-item">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i> 搜索
                </button>
                <a href="pages.php" class="btn btn-outline">
                    <i class="fas fa-redo"></i> 重置
                </a>
            </div>
        </form>

<!-- 消息提示 -->
{if $message}
<div class="alert alert-success">
    <i class="fas fa-check-circle"></i> {$message}
</div>
{/if}

{if $error}
<div class="alert alert-danger">
    <i class="fas fa-exclamation-circle"></i> {$error}
</div>
{/if}

        <!-- 数据表格 -->
        <div class="table-responsive">
            <table class="content-table">
                <thead>
                    <tr>
                        <th width="40">
                            <input type="checkbox" id="checkAll" onchange="toggleAll(this)">
                        </th>
                        <th width="60">ID</th>
                        <th>页面标题</th>
                        <th width="200">访问路径</th>
                        <th width="80">状态</th>
                        <th width="60">排序</th>
                        <th width="100">创建时间</th>
                        <th width="160">操作</th>
                    </tr>
                </thead>
                <tbody>
                    {if $pages}
                    <form id="batchForm" method="POST" action="pages.php?action=batch_delete">
                    {foreach $pages as $page}
                    <tr>
                        <td>
                            <input type="checkbox" name="ids[]" value="{$page.id}" class="item-checkbox">
                        </td>
                        <td><strong>{$page.id}</strong></td>
                        <td>
                            <div style="font-weight: 600; color: #333; margin-bottom: 2px;">{$page.title}</div>
                            {if $page.meta_description}
                            <div style="font-size: 12px; color: #666; line-height: 1.4;">
                                {php}echo mb_substr($page['meta_description'], 0, 60, 'utf-8') . (mb_strlen($page['meta_description'], 'utf-8') > 60 ? '...' : '');{/php}
                            </div>
                            {/if}
                        </td>
                        <td>
                            <div style="margin-bottom: 3px;">
                                <code style="font-size: 12px; background: #f8f9fa; padding: 2px 4px; border-radius: 3px;">{$page.path}</code>
                            </div>
                            <a href="{$page.url}" target="_blank" class="btn btn-sm btn-light-info" style="font-size: 11px; padding: 2px 6px;">
                                <i class="fas fa-external-link-alt"></i> 预览
                            </a>
                        </td>
                        <td>
                            {if $page.status == 1}
                                <span class="tag tag-success">启用</span>
                            {else}
                                <span class="tag tag-danger">禁用</span>
                            {/if}
                        </td>
                        <td style="text-align: center; font-weight: 600;">{$page.sort_order}</td>
                        <td style="font-size: 12px; color: #666;">
                            {php}echo date('m-d H:i', $page['created_at']);{/php}
                        </td>
                        <td>
                            <div class="page-actions">
                                <a href="{$page.url}" target="_blank" class="btn btn-light-info" title="查看">
                                    <i class="fas fa-eye"></i> 查看
                                </a>
                                <a href="pages.php?action=edit&id={$page.id}" class="btn btn-light-primary" title="编辑">
                                    <i class="fas fa-edit"></i> 编辑
                                </a>
                                <a href="javascript:if(confirm('确定要删除这个页面吗？'))location='pages.php?action=delete&id={$page.id}'" class="btn btn-light-danger" title="删除">
                                    <i class="fas fa-trash"></i> 删除
                                </a>
                            </div>
                        </td>
                    </tr>
                    {/foreach}
                    </form>
                    {else}
                    <tr>
                        <td colspan="8" style="text-align: center; padding: 40px; color: #666;">
                            <i class="fas fa-file-alt fa-2x" style="margin-bottom: 10px; opacity: 0.5;"></i>
                            <div>暂无单页数据</div>
                            <div style="margin-top: 10px;">
                                <a href="pages.php?action=add" class="btn btn-primary btn-sm">
                                    <i class="fas fa-plus"></i> 添加单页
                                </a>
                            </div>
                        </td>
                    </tr>
                    {/if}
                </tbody>
            </table>
        </div>

        <!-- 批量操作和分页 -->
        {if $pages}
        <div style="display: flex; justify-content: space-between; align-items: center; padding: 15px; border-top: 1px solid #f0f0f0; background: #fafafa;">
            <div>
                <button type="button" class="btn btn-sm btn-danger" onclick="batchDelete()">
                    <i class="fas fa-trash"></i> 批量删除
                </button>
                <span style="margin-left: 15px; color: #666; font-size: 13px;">
                    共 {$total} 条记录
                </span>
            </div>

            <!-- 分页 -->
            {if $total_pages > 1}
            <div class="pagination" style="margin: 0;">
                {if $page > 1}
                    <a href="pages.php?action=list&page=1{if $keyword}&keyword={$keyword}{/if}{if $status >= 0}&status={$status}{/if}" class="page-link">首页</a>
                    <a href="pages.php?action=list&page={$page-1}{if $keyword}&keyword={$keyword}{/if}{if $status >= 0}&status={$status}{/if}" class="page-link">上一页</a>
                {/if}

                <!-- 页码显示 -->
                {if $page > 2}
                    <a href="pages.php?action=list&page={$page-1}{if $keyword}&keyword={$keyword}{/if}{if $status >= 0}&status={$status}{/if}" class="page-link">{$page-1}</a>
                {/if}

                <span class="page-link active">{$page}</span>

                {if $page < $total_pages - 1}
                    <a href="pages.php?action=list&page={$page+1}{if $keyword}&keyword={$keyword}{/if}{if $status >= 0}&status={$status}{/if}" class="page-link">{$page+1}</a>
                {/if}

                {if $page < $total_pages}
                    <a href="pages.php?action=list&page={$page+1}{if $keyword}&keyword={$keyword}{/if}{if $status >= 0}&status={$status}{/if}" class="page-link">下一页</a>
                    <a href="pages.php?action=list&page={$total_pages}{if $keyword}&keyword={$keyword}{/if}{if $status >= 0}&status={$status}{/if}" class="page-link">末页</a>
                {/if}
            </div>
            {/if}
        </div>
        {/if}
    </div>
</div>

<script>
// 全选/取消全选
function toggleAll(checkbox) {
    const checkboxes = document.querySelectorAll('.item-checkbox');
    checkboxes.forEach(cb => cb.checked = checkbox.checked);
}

// 批量删除
function batchDelete() {
    const checkedBoxes = document.querySelectorAll('.item-checkbox:checked');
    if (checkedBoxes.length === 0) {
        alert('请选择要删除的页面');
        return;
    }
    
    if (confirm(`确定要删除选中的 ${checkedBoxes.length} 个页面吗？`)) {
        document.getElementById('batchForm').submit();
    }
}

// 监听复选框变化
document.addEventListener('DOMContentLoaded', function() {
    const checkboxes = document.querySelectorAll('.item-checkbox');
    const checkAll = document.getElementById('checkAll');
    
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const checkedCount = document.querySelectorAll('.item-checkbox:checked').length;
            checkAll.checked = checkedCount === checkboxes.length;
            checkAll.indeterminate = checkedCount > 0 && checkedCount < checkboxes.length;
        });
    });
});
</script>

{include file="footer.htm"}
