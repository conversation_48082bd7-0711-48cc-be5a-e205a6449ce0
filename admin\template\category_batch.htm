{include file="header.htm"}

<style>
    /* 表单样式 */
    .form-group {
        margin-bottom: 15px;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
    }
    .form-label {
        display: block;
        font-weight: 500;
        width: 100px;
        text-align: right;
        padding-right: 15px;
    }
    .form-field {
        flex: 1;
        min-width: 300px;
    }
    .form-hint {
        width: 100%;
        margin-left: 115px;
        margin-top: 3px;
        font-size: 12px;
        color: var(--text-secondary);
    }
    .form-control {
        display: block;
        width: 100%;
        padding: 6px 10px;
        font-size: 14px;
        line-height: 1.5;
        color: var(--text-color);
        background-color: #fff;
        border: 1px solid var(--border-color);
        border-radius: 4px;
        transition: border-color 0.15s ease-in-out;
    }
    .form-control:focus {
        border-color: var(--primary-color);
        outline: 0;
    }
    textarea.form-control {
        min-height: 250px;
        font-family: monospace;
    }
    
    /* 淡色按钮样式 */
    .btn-light-primary {
        background-color: #e6f0ff;
        color: #1b68ff;
        border: 1px solid #cce0ff;
    }
    .btn-light-primary:hover {
        background-color: #d1e3ff;
        color: #0056b3;
    }
    .btn-light-success {
        background-color: #e6ffe6;
        color: #00aa00;
        border: 1px solid #b3ffb3;
    }
    .btn-light-success:hover {
        background-color: #d1ffd1;
        color: #008800;
    }
    .btn-light-secondary {
        background-color: #f0f0f0;
        color: #666666;
        border: 1px solid #dddddd;
    }
    .btn-light-secondary:hover {
        background-color: #e0e0e0;
        color: #444444;
    }
    
    /* 分类层级样式 */
    .indent-1:before { content: "— "; }
    .indent-2:before { content: "—— "; }
    .indent-3:before { content: "——— "; }
    .indent-4:before { content: "———— "; }
    .indent-5:before { content: "————— "; }
    
    @media (max-width: 768px) {
        .form-group {
            flex-direction: column;
            align-items: flex-start;
        }
        .form-label {
            width: 100%;
            text-align: left;
            padding-right: 0;
            margin-bottom: 5px;
        }
        .form-field {
            width: 100%;
        }
        .form-hint {
            margin-left: 0;
            margin-top: 5px;
            width: 100%;
        }
    }
</style>

<!-- 消息提示 -->
{if !empty($error)}
<div class="alert alert-danger">
    <p style="margin: 0;">{$error}</p>
</div>
{/if}

<!-- 批量添加分类 -->
<div class="card mb-4">
    <div class="card-header" style="padding: 10px 15px;">
        <h5 style="margin: 0; font-size: 16px;">批量添加分类</h5>
    </div>
    <div class="card-body" style="padding: 15px;">
        <!-- 当前位置 -->
        <div style="margin-bottom: 15px;">
            <div style="display: flex; align-items: center; font-size: 13px; color: #666;">
                <span>当前位置: </span>
                <a href="category.php" style="color: #007bff; text-decoration: none; margin: 0 5px;">分类管理</a>
                <span>></span>
                <span style="margin: 0 5px; font-weight: 500;">批量添加分类</span>
            </div>
        </div>
        
        <!-- 批量添加表单 -->
        <form method="post" action="" id="batchForm">
            <div class="form-group">
                <label class="form-label" for="parent_id">父分类</label>
                <div class="form-field">
                    <select id="parent_id" name="parent_id" class="form-control">
                        <option value="0">-- 作为一级分类 --</option>
                        {foreach $parent_categories as $parent}
                        {if $parent.parent_id == 0}
                        <option value="{$parent.id}">
                            {$parent.name}
                        </option>
                        {/if}
                        {/foreach}
                    </select>
                </div>
                <span class="form-hint">选择一个一级分类作为父分类，或选择"作为一级分类"创建顶级分类</span>
            </div>
            
            <div class="form-group">
                <label class="form-label" for="categories">分类列表</label>
                <div class="form-field">
                    <textarea id="categories" name="categories" class="form-control" placeholder="每行输入一个分类名称，例如：
家居用品
电子数码
服装服饰"></textarea>
                </div>
                <span class="form-hint">每行输入一个分类名称，每个分类将自动生成拼音标识</span>
            </div>
            
            <div class="form-group" style="margin-top: 20px; margin-bottom: 0;">
                <div class="form-label"></div>
                <div class="form-field">
                    <button type="submit" class="btn btn-sm btn-light-success" style="padding: 4px 10px; font-size: 13px;">
                        批量添加
                    </button>
                    
                    <a href="category.php" class="btn btn-sm btn-light-secondary" style="margin-left: 8px; padding: 4px 10px; font-size: 13px;">
                        取消
                    </a>
                </div>
            </div>
        </form>
        
        <!-- 简单说明 -->
        <div style="margin-top: 20px; margin-left: 115px; font-size: 12px; color: #666;">
            <p style="margin: 0 0 3px 0;">1. 批量添加的分类将使用"启用"状态，排序值默认为0</p>
            <p style="margin: 0 0 3px 0;">2. 系统会自动为每个分类生成拼音标识</p>
            <p style="margin: 0 0 3px 0;">3. 添加完成后，您可以在分类列表中分别编辑各个分类的详细信息</p>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 表单提交前验证
        document.getElementById('batchForm').addEventListener('submit', function(e) {
            var categoriesInput = document.getElementById('categories');
            
            if (!categoriesInput.value.trim()) {
                e.preventDefault();
                alert('请输入至少一个分类名称');
                categoriesInput.focus();
                return;
            }
        });
    });
</script>

{include file="footer.htm"} 