<?php if(null !== ($posts ?? null) && !empty($posts)): ?>
    <?php if(null !== ($posts ?? null) && is_array($posts)): foreach($posts as $post): ?>
    <?php
    // 计算有效期
    if (!empty($post['expired_at'])) {
        $days = getRemainingDaysInt($post['expired_at']);
        $validity = $days > 0 ? $days.'天' : '已过期';
    } else {
        $validity = '长期';
    }
    ?>
    <div class="list-item <?php if(null !== ($post ?? null) && is_array($post) && array_key_exists('is_top_category', $post) && !empty($post['is_top_category']) || null !== ($post ?? null) && is_array($post) && array_key_exists('is_top_subcategory', $post) && !empty($post['is_top_subcategory'])): ?>is-top<?php endif; ?>">
        <div class="item-row">
            <div class="item-left">
                <div class="item-title">
                    <?php if ($validity == '已过期'): ?>
                    <a href="/<?php echo (isset($category['pinyin'])) ? $category['pinyin'] : ""; ?>/<?php echo (isset($post['id'])) ? $post['id'] : ""; ?>.html" class="expired"><?php echo (isset($post['title'])) ? $post['title'] : ""; ?></a>
                    <?php else: ?>
                    <a href="/<?php echo (isset($category['pinyin'])) ? $category['pinyin'] : ""; ?>/<?php echo (isset($post['id'])) ? $post['id'] : ""; ?>.html"><?php echo (isset($post['title'])) ? $post['title'] : ""; ?></a>
                    <?php endif; ?>
                    <?php if(null !== ($post ?? null) && is_array($post) && array_key_exists('is_top_category', $post) && !empty($post['is_top_category']) || null !== ($post ?? null) && is_array($post) && array_key_exists('is_top_subcategory', $post) && !empty($post['is_top_subcategory'])): ?>
                    <span class="top-tag">顶</span>
                    <?php endif; ?>
                </div>
            </div>
            <div class="item-right">
                <div class="item-time">
                    <?php echo null !== ((null !== ($post ?? null)) ? ($post['updated_at']) : null) ? friendlyTime((null !== ($post ?? null)) ? ($post['updated_at']) : null) : ""; ?>
                </div>
            </div>
        </div>
    </div>
    <?php endforeach; endif; ?>
<?php endif; ?>