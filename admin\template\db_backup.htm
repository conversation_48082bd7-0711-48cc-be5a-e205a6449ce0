{include file="header.htm"}

<style>
/* 页面标题样式 */
.page-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #ddd;
}

.page-title h1 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

/* 操作面板样式 - 紧凑版 */
.action-panel {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 16px;
    border: 1px solid #dee2e6;
}

.action-info {
    font-size: 14px;
    color: #666;
}

.action-info strong {
    color: #333;
}

/* 表格样式 - 紧凑版 */
.table-container {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: 16px;
}

.table-header {
    background: #f8f9fa;
    padding: 12px 16px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.table-header h3 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.table-responsive {
    overflow-x: auto;
}

.backup-table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
}

.backup-table th,
.backup-table td {
    padding: 8px 12px;
    text-align: left;
    border-bottom: 1px solid #f1f3f4;
    vertical-align: middle;
    font-size: 13px;
}

.backup-table th {
    background-color: #fafbfc;
    font-weight: 600;
    color: #495057;
    font-size: 12px;
}

.backup-table tbody tr:hover {
    background-color: #f8f9fa;
}

.backup-table tbody tr:last-child td {
    border-bottom: none;
}

/* 按钮样式 - 紧凑版 */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 6px 12px;
    font-size: 12px;
    font-weight: 500;
    line-height: 1.5;
    text-align: center;
    text-decoration: none;
    vertical-align: middle;
    cursor: pointer;
    border: 1px solid transparent;
    border-radius: 4px;
    transition: all 0.15s ease-in-out;
    white-space: nowrap;
}

.btn-primary {
    color: #fff;
    background-color: #1b68ff;
    border-color: #1b68ff;
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
    color: #fff;
    text-decoration: none;
}

.btn-success {
    color: #fff;
    background-color: #28a745;
    border-color: #28a745;
}

.btn-success:hover {
    background-color: #1e7e34;
    border-color: #1e7e34;
    color: #fff;
    text-decoration: none;
}

.btn-danger {
    color: #fff;
    background-color: #dc3545;
    border-color: #dc3545;
}

.btn-danger:hover {
    background-color: #c82333;
    border-color: #c82333;
    color: #fff;
    text-decoration: none;
}

/* 操作按钮组 */
.btn-actions {
    display: flex;
    gap: 6px;
    justify-content: flex-end;
}

/* 徽章样式 */
.badge {
    display: inline-block;
    padding: 2px 6px;
    font-size: 11px;
    font-weight: 500;
    border-radius: 3px;
    text-align: center;
    white-space: nowrap;
}

.badge-primary {
    color: #fff;
    background-color: #1b68ff;
}

.badge-success {
    color: #fff;
    background-color: #28a745;
}

.badge-warning {
    color: #212529;
    background-color: #ffc107;
}

/* 空状态样式 - 紧凑版 */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.empty-state h3 {
    font-size: 16px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

.empty-state p {
    font-size: 13px;
    color: #6c757d;
    margin-bottom: 16px;
}

/* 消息提示样式 */
.alert {
    padding: 12px 16px;
    border-radius: 6px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    display: flex;
    align-items: center;
    gap: 8px;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

/* 移除链接下划线 */
a, a:hover, a:focus, a:active {
    text-decoration: none !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .action-panel {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    .action-item {
        justify-content: center;
        text-align: center;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .table-header {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }

    .btn-actions {
        flex-direction: column;
        gap: 6px;
    }

    .backup-table th,
    .backup-table td {
        padding: 8px 12px;
        font-size: 13px;
    }
}
</style>

<!-- 页面标题 -->
<div class="page-title">
    <h1><i class="fas fa-database"></i> 数据库备份管理</h1>
</div>

<!-- 消息提示 -->
{if $message}
<div class="alert alert-success">
    <i class="fas fa-check-circle"></i>
    {$message}
</div>
{/if}
{if $error}
<div class="alert alert-danger">
    <i class="fas fa-exclamation-circle"></i>
    {$error}
</div>
{/if}

<!-- 操作面板 -->
<div class="action-panel">
    <div class="action-info">
        <strong>备份文件：</strong>{if $backups}{$backups|count}{else}0{/if} 个
        {if $total_backup_size} | <strong>总大小：</strong>{$total_backup_size}{/if}
    </div>
    <form action="db_backup.php?action=backup" method="post" style="margin: 0;">
        <input type="hidden" name="csrf_token" value="{$csrf_token}">
        <button type="submit" class="btn btn-primary" onclick="return confirm('确定要创建新的数据库备份吗？\n\n备份过程可能需要几分钟时间，请耐心等待。')">
            <i class="fas fa-plus"></i>
            创建备份
        </button>
    </form>
</div>
<!-- 备份列表 -->
<div class="table-container">
    <div class="table-header">
        <h3>备份文件列表</h3>
        {if $backups}
        <span class="badge badge-primary">{$backups|count}</span>
        {/if}
    </div>

    {if !$backups}
    <div class="empty-state">
        <h3>暂无备份文件</h3>
        <p>点击上方"创建备份"按钮创建第一个备份</p>
    </div>
    {else}
    <div class="table-responsive">
        <table class="backup-table">
            <thead>
                <tr>
                    <th>备份时间</th>
                    <th>数据库</th>
                    <th>表数量</th>
                    <th>文件大小</th>
                    <th width="120">操作</th>
                </tr>
            </thead>
            <tbody>
                {foreach $backups as $backup}
                <tr>
                    <td>
                        <div style="font-weight: 500;">
                            {$backup.formatted_date}
                        </div>
                    </td>
                    <td>
                        <code style="font-size: 11px;">{if $backup.db_name}{$backup.db_name}{else}未知{/if}</code>
                    </td>
                    <td>
                        {if $backup.table_count > 0}
                        <span class="badge badge-primary">{$backup.table_count}</span>
                        {else}
                        <span class="badge badge-warning">0</span>
                        {/if}
                    </td>
                    <td>
                        <strong>{$backup.formatted_size}</strong>
                    </td>
                    <td>
                        <div class="btn-actions">
                            <a href="db_backup.php?action=restore&dir={$backup.dir}&token={$csrf_token}"
                               class="btn btn-success"
                               onclick="return confirm('警告：恢复备份将覆盖现有数据！\n\n确定继续吗？')">
                                <i class="fas fa-undo"></i>
                                恢复
                            </a>
                            <a href="db_backup.php?action=delete&dir={$backup.dir}&token={$csrf_token}"
                               class="btn btn-danger"
                               onclick="return confirm('确定删除此备份吗？\n\n此操作不可逆！')">
                                <i class="fas fa-trash"></i>
                                删除
                            </a>
                        </div>
                    </td>
                </tr>
                {/foreach}
            </tbody>
        </table>
    </div>
    {/if}
</div>
{include file="footer.htm"}