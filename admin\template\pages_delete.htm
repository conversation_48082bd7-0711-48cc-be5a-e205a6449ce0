{include file="header.htm"}

<!-- 页面标题 -->
<div class="page-title">
    <h1>删除单页</h1>
    <div class="d-flex gap-2">
        <a href="pages.php" class="btn btn-outline">
            <i class="fas fa-arrow-left"></i> 返回列表
        </a>
    </div>
</div>

<!-- 删除确认 -->
<div class="section">
    <div class="card">
        <div class="card-header bg-danger text-white">
            <h3 class="card-title">
                <i class="fas fa-exclamation-triangle"></i> 删除确认
            </h3>
        </div>
        
        <div class="card-body">
            <div class="alert alert-warning">
                <i class="fas fa-warning"></i>
                <strong>警告：</strong>删除操作不可恢复，请确认是否要删除以下页面？
            </div>
            
            <div class="page-info">
                <table class="table table-bordered">
                    <tr>
                        <th width="120">页面ID</th>
                        <td>{$page_info.id}</td>
                    </tr>
                    <tr>
                        <th>页面标题</th>
                        <td><strong>{$page_info.title}</strong></td>
                    </tr>
                    <tr>
                        <th>页面路径</th>
                        <td><code>{$page_info.path}</code></td>
                    </tr>
                    <tr>
                        <th>页面URL</th>
                        <td>
                            <a href="{$page_info.url}" target="_blank" class="text-primary">
                                {$page_info.url}
                                <i class="fas fa-external-link-alt fa-xs"></i>
                            </a>
                        </td>
                    </tr>
                    <tr>
                        <th>状态</th>
                        <td>
                            {if $page_info.status == 1}
                                <span class="badge badge-success">启用</span>
                            {else}
                                <span class="badge badge-secondary">禁用</span>
                            {/if}
                        </td>
                    </tr>
                    <tr>
                        <th>创建时间</th>
                        <td>{php}echo date('Y-m-d H:i:s', $page_info['created_at']);{/php}</td>
                    </tr>
                    <tr>
                        <th>更新时间</th>
                        <td>{php}echo date('Y-m-d H:i:s', $page_info['updated_at']);{/php}</td>
                    </tr>
                    {if $page_info.meta_description}
                    <tr>
                        <th>页面描述</th>
                        <td>{$page_info.meta_description}</td>
                    </tr>
                    {/if}
                </table>
            </div>
            
            <div class="delete-warning">
                <h5>删除后将会：</h5>
                <ul class="warning-list">
                    <li><i class="fas fa-times text-danger"></i> 从数据库中永久删除页面记录</li>
                    <li><i class="fas fa-times text-danger"></i> 删除PC端HTML文件：<code>{$page_info.path}</code></li>
                    <li><i class="fas fa-times text-danger"></i> 删除移动端HTML文件：<code>m/{$page_info.path}</code></li>
                    <li><i class="fas fa-times text-danger"></i> 页面将无法访问</li>
                </ul>
            </div>
            
            <div class="action-buttons">
                <form method="POST" action="pages.php?action=delete" style="display: inline;">
                    <input type="hidden" name="id" value="{$page_info.id}">
                    <button type="submit" class="btn btn-danger" onclick="return confirm('确定要删除这个页面吗？此操作不可恢复！')">
                        <i class="fas fa-trash"></i> 确认删除
                    </button>
                </form>
                
                <a href="pages.php?action=edit&id={$page_info.id}" class="btn btn-primary">
                    <i class="fas fa-edit"></i> 编辑页面
                </a>
                
                <a href="pages.php" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> 取消删除
                </a>
            </div>
        </div>
    </div>
</div>

<style>
.page-info {
    margin: 20px 0;
}

.delete-warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 15px;
    margin: 20px 0;
}

.warning-list {
    margin: 10px 0 0 0;
    padding: 0;
    list-style: none;
}

.warning-list li {
    padding: 5px 0;
    font-size: 14px;
}

.warning-list li i {
    margin-right: 8px;
    width: 16px;
}

.action-buttons {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #eee;
    text-align: center;
}

.action-buttons .btn {
    margin: 0 5px;
}

.card-header.bg-danger {
    background-color: #f82f58 !important;
}

.card-header.bg-danger .card-title {
    margin: 0;
    color: white;
}
</style>

{include file="footer.htm"}
