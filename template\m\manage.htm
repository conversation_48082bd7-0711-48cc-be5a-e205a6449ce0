<!DOCTYPE html>
{php}
// 获取主题设置
$theme = isset($_COOKIE['site_theme']) ? $_COOKIE['site_theme'] : 'red';
$theme_class = 'theme-' . $theme;
{/php}
<html lang="zh-CN" class="<?php echo $theme_class; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>信息管理 - {$site_name}</title>
    <link rel="stylesheet" href="/static/font-awesome/css/all.min.css">
    <link rel="stylesheet" href="/template/m/css/common.css">
    <link rel="stylesheet" href="/static/css/themes.css">
    <style>
        /* 确保简约主题头部为白色背景 */
        .theme-simple header {
            background-color: #ffffff !important;
        }
        
        body {
            background-color: #f5f5f5;
            padding-bottom: 20px;
            margin: 0;
        }
        .post-info {
            background-color: white;
            padding: 15px;
            margin-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        .post-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .post-meta {
            display: flex;
            flex-direction: column;
            font-size: 13px;
            color: #666;
            gap: 8px;
        }
        .post-meta-item {
            padding: 4px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .post-meta-item:last-child {
            border-bottom: none;
        }
        .meta-label {
            font-weight: 500;
            display: inline-block;
            width: 80px;
            color: #333;
        }
        
        .form-section {
            background: white;
            padding: 15px;
            margin-bottom: 10px;
        }
        .form-control {
            display: block;
            width: 100%;
            height: 40px;
            padding: 8px 12px;
            font-size: 14px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
            margin-bottom: 15px;
        }
        
        .hint-box {
            background-color: #fff8e1;
            border-left: 3px solid #ffb300;
            padding: 10px;
            margin: 10px 15px;
            font-size: 13px;
            color: #666;
            border-radius: 4px;
        }
        .hint-box p {
            margin: 0 0 5px 0;
        }
        
        /* 扁平化操作按钮 */
        .action-btns {
            display: flex;
            justify-content: space-between;
            padding: 0 15px;
            margin-top: 15px;
            gap: 10px;
        }
        .action-btn {
            flex: 1;
            text-align: center;
            padding: 12px 0;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
        }
        .btn-edit {
            background-color: var(--primary-color);
        }
        .btn-refresh {
            background-color: #3498db;
        }
        .btn-delete {
            background-color: #e74c3c;
        }
        
        /* 底部导航 */
        .footer-nav {
            background: white;
            padding: 15px;
            margin-top: 10px;
            text-align: center;
            border-top: 1px solid #eee;
        }
        .back-link {
            color: #666;
            text-decoration: none;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <header>
        <div class="header-inner">
            <div class="header-left">
                <a href="javascript:history.back();" class="header-back">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </div>
            <div class="header-title">信息管理</div>
            <div class="header-right"></div>
        </div>
    </header>

    <div class="post-info">
        <div class="post-title">{$post.title}</div>
        <div class="post-meta">
            <span class="post-meta-item"><span class="meta-label">分类：</span>{$post.category_name}</span>
            <span class="post-meta-item"><span class="meta-label">发布时间：</span>{php}echo friendlyTime($post['created_at']);{/php}</span>
            <span class="post-meta-item"><span class="meta-label">联系人：</span>{$post.contact_name}</span>
            <span class="post-meta-item"><span class="meta-label">联系电话：</span>{$post.contact_mobile}</span>
            {if !empty($post.contact_weixin)}
            <span class="post-meta-item"><span class="meta-label">微信号：</span>{$post.contact_weixin}</span>
            {/if}
        </div>
    </div>

    {if isset($verified) && $verified === true}
    <div class="hint-box" style="background-color: #e8f5e9; border-left-color: #4caf50;">
        <p>身份已验证，您可以管理此信息</p>
    </div>
    
    <!-- 已验证，显示操作按钮 -->
    <div class="action-btns">
        <a href="/manage.php?id={$post.id}&action=edit" class="action-btn btn-edit">
            修改
        </a>
        <a href="/manage.php?id={$post.id}&action=refresh" class="action-btn btn-refresh">
            刷新
        </a>
        <a href="javascript:void(0)" onclick="if(confirm('确定要删除此信息吗？删除后无法恢复！')) { window.location.href='/manage.php?id={$post.id}&action=delete'; }" class="action-btn btn-delete">
            删除
        </a>
    </div>
    {else}
    <div class="hint-box">
        <p>请输入管理密码完成身份验证</p>
    </div>
    
    <!-- 未验证，显示密码输入框 -->
    <form action="/manage.php" method="post">
        <input type="hidden" name="id" value="{$post.id}">
        
        <div class="form-section">
            <input type="password" name="password" id="password" class="form-control" placeholder="请输入管理密码" required>
        </div>
        
        <div class="action-btns">
            <button type="submit" name="action" value="修改" class="action-btn btn-edit">
                修改
            </button>
            <button type="submit" name="action" value="刷新" class="action-btn btn-refresh">
                刷新
            </button>
            <button type="submit" name="action" value="删除" class="action-btn btn-delete" onclick="return confirm('确定要删除此信息吗？删除后无法恢复！')">
                删除
            </button>
        </div>
    </form>
    {/if}
    
    <div class="footer-nav">
        <a href="/{$post.category_pinyin}/{$post.id}.html" class="back-link">
            <i class="fas fa-arrow-left"></i> 返回信息详情
        </a>
    </div>
</body>
</html>