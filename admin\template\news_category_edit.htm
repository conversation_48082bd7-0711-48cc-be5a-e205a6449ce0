{include file="header.htm"}

<style>
/* 页面标题样式 */
.page-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid #ddd;
}

.page-title h1 {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.d-flex {
    display: flex !important;
}

.gap-2 {
    gap: 0.5rem !important;
}

/* 表单样式 - 紧凑布局 */
.form-group {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16px;
    gap: 20px;
}

.form-group.compact {
    margin-bottom: 12px;
}

.form-label {
    flex: 0 0 120px;
    margin: 6px 0 0 0;
    font-weight: 600;
    color: #333;
    font-size: 14px;
    text-align: right;
}

.form-field {
    flex: 1;
    min-width: 0;
}

.form-control, .form-select {
    display: block;
    width: 100%;
    max-width: 400px;
    padding: 6px 12px;
    font-size: 14px;
    line-height: 1.5;
    color: #333;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus, .form-select:focus {
    border-color: #1b68ff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(27, 104, 255, 0.25);
}

/* 表单描述在右侧 */
.form-description {
    flex: 0 0 200px;
    margin: 6px 0 0 0;
    font-size: 12px;
    color: #666;
    line-height: 1.4;
}

/* 帮助文本样式 */
.form-hint {
    display: block;
    margin-top: 4px;
    font-size: 12px;
    color: #666;
    line-height: 1.4;
}

/* 单选按钮组样式 */
.radio-inline {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    margin-right: 20px;
    font-weight: normal;
    cursor: pointer;
    font-size: 14px;
}

.radio-inline input[type="radio"] {
    margin: 0;
}

/* 按钮样式 */
.btn {
    display: inline-block;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.5;
    text-align: center;
    text-decoration: none;
    vertical-align: middle;
    cursor: pointer;
    border: 1px solid transparent;
    border-radius: 4px;
    transition: all 0.15s ease-in-out;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

.btn-primary {
    color: #fff;
    background-color: #1b68ff;
    border-color: #1b68ff;
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
    color: #fff;
    text-decoration: none;
}

.btn-secondary {
    color: #666;
    background-color: #f8f9fa;
    border-color: #ddd;
}

.btn-secondary:hover {
    background-color: #e2e6ea;
    border-color: #ccc;
    color: #666;
    text-decoration: none;
}

.btn-light-info {
    background-color: #e6f7ff;
    color: #00aaff;
    border: 1px solid #b3e0ff;
}

.btn-light-info:hover {
    background-color: #d1f0ff;
    color: #0088cc;
    text-decoration: none;
}

.btn-light-secondary {
    background-color: #f0f0f0;
    color: #666666;
    border: 1px solid #dddddd;
}

.btn-light-secondary:hover {
    background-color: #e0e0e0;
    color: #444444;
    text-decoration: none;
}

/* 卡片样式 - 紧凑版 */
.card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.card-header {
    padding: 16px 20px;
    border-bottom: 1px solid #eee;
    background-color: #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.card-body {
    padding: 20px;
}

/* 表单按钮区域 */
.form-actions {
    display: flex;
    gap: 12px;
    padding-top: 16px;
    border-top: 1px solid #eee;
    margin-top: 20px;
    margin-left: 140px; /* 与标签对齐 */
}

.form-btn-group {
    border-top: 1px solid #eee;
    padding-top: 16px;
    margin-top: 20px;
}

.form-btn-group .form-field {
    display: flex;
    gap: 12px;
    margin-left: 140px; /* 与标签对齐 */
}

/* 移除链接下划线 */
a, a:hover, a:focus, a:active {
    text-decoration: none !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .form-group {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .form-label {
        flex: none;
        text-align: left;
        margin: 0 0 4px 0;
    }

    .form-description {
        flex: none;
        margin: 4px 0 0 0;
    }

    .form-actions, .form-btn-group .form-field {
        margin-left: 0;
        flex-direction: column;
    }

    .form-control, .form-select {
        max-width: none;
    }
}
</style>

<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <div>
            <h3 class="card-title">编辑新闻栏目</h3>
        </div>
        <div>
            {if isset($category) && $category.parentid > 0}
            <a href="news_category.php?parent_id={$category.parentid}" class="btn btn-sm btn-light-info">返回父栏目</a>
            {/if}
            {if isset($return_parent_id) && $return_parent_id > 0}
            <a href="news_category.php?parent_id={$return_parent_id}" class="btn btn-sm btn-light-secondary">返回</a>
            {/if}
        </div>
    </div>
    
    <div class="card-body">
        <form action="news_category.php?op=edit&catid={$category.catid}{if isset($return_parent_id) && $return_parent_id > 0}&return_parent_id={$return_parent_id}{/if}" method="post" class="form-horizontal">
            <div class="form-group">
                <label class="form-label">上级栏目</label>
                <div class="form-field">
                    <select name="parentid" class="form-control">
                        <option value="0" {if $category.parentid == 0}selected{/if}>作为顶级栏目</option>
                        {if isset($topCategories)}
                        {foreach from=$topCategories item=cat}
                        <option value="{$cat.catid}" {if $category.parentid == $cat.catid}selected{/if}>{$cat.catname}</option>
                        {/foreach}
                        {/if}
                    </select>
                </div>
                <div class="form-description">选择上级栏目，如无则作为顶级栏目</div>
            </div>

            <div class="form-group">
                <label class="form-label">栏目名称</label>
                <div class="form-field">
                    <input type="text" name="catname" value="{$category.catname}" class="form-control" required>
                </div>
                <div class="form-description">必填，栏目的显示名称</div>
            </div>

            <div class="form-group">
                <label class="form-label">栏目拼音</label>
                <div class="form-field">
                    <input type="text" name="pinyin" value="{$category.pinyin}" class="form-control" placeholder="用于伪静态URL，如：botou-news">
                    <div class="form-hint">用于生成伪静态URL，只能包含字母、数字和连字符，如：botou-news</div>
                </div>
                <div class="form-description">用于生成伪静态URL</div>
            </div>

            <div class="form-group compact">
                <label class="form-label">排序</label>
                <div class="form-field">
                    <input type="number" name="sort_order" value="{$category.sort_order}" class="form-control" style="width: 120px;">
                </div>
                <div class="form-description">数字越小排序越靠前</div>
            </div>

            <div class="form-group compact">
                <label class="form-label">是否显示</label>
                <div class="form-field">
                    <label class="radio-inline">
                        <input type="radio" name="is_show" value="1" {if $category.is_show == 1}checked{/if}> 显示
                    </label>
                    <label class="radio-inline">
                        <input type="radio" name="is_show" value="0" {if $category.is_show == 0}checked{/if}> 隐藏
                    </label>
                </div>
                <div class="form-description">设置栏目在前台是否显示</div>
            </div>
            <div class="form-group form-btn-group">
                <div class="form-field">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> 保存栏目
                    </button>
                    <a href="news_category.php{if isset($return_parent_id) && $return_parent_id > 0}?parent_id={$return_parent_id}{/if}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> 返回列表
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

{include file="footer.htm"}

<script>
// 只在DOM加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 设置当前菜单选中状态
    var menuElement = document.getElementById("menu_news_category");
    if (menuElement) {
        menuElement.className += " active";
    }
});
</script> 