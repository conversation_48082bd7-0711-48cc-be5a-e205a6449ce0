-- 手机号发布记录表（用于防时间篡改的安全检查）
CREATE TABLE IF NOT EXISTS `mobile_post_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `mobile` varchar(20) NOT NULL COMMENT '手机号码',
  `post_timestamp` int(10) NOT NULL COMMENT '发布时间戳（客户端时间）',
  `server_timestamp` int(10) NOT NULL COMMENT '服务器时间戳（真实时间）',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `created_at` int(10) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_mobile` (`mobile`),
  KEY `idx_server_timestamp` (`server_timestamp`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_mobile_server_time` (`mobile`, `server_timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='手机号发布记录表（防时间篡改）';

-- 为现有的mobile_daily_stats表添加服务器时间字段
ALTER TABLE `mobile_daily_stats` 
ADD COLUMN `server_timestamp` int(10) DEFAULT NULL COMMENT '服务器时间戳' AFTER `updated_at`,
ADD INDEX `idx_server_timestamp` (`server_timestamp`);
